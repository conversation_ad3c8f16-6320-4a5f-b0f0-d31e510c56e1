# Translation of Odoo Server.
 # This file contains the translation of the following modules:
 # * hr_payroll_community_v13
 # 
 # Translators:
 # <AUTHOR> <EMAIL>, 2018
 # <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON>, 2018
 # <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON>ot<PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON> <<EMAIL>>, 2018
 # <AUTHOR> <EMAIL>, 2018
 # 
 msgid ""
 msgstr ""
 "Project-Id-Version: Odoo Server 13.0\n"
 "Report-Msgid-Bugs-To: \n"
 "POT-Creation-Date: 2019-01-09 10:31+0000\n"
 "PO-Revision-Date: 2018-08-24 09:19+0000\n"
 "Last-Translator: Maksym <<EMAIL>>, 2018\n"
 "Language-Team: Polish (https://www.transifex.com/odoo/teams/41243/pl/)\n"
 "MIME-Version: 1.0\n"
 "Content-Type: text/plain; charset=UTF-8\n"
 "Content-Transfer-Encoding: \n"
 "Language: pl\n"
 "Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:42
 #, python-format
 msgid "%s (copy)"
 msgstr "%s (kopia)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__state
 msgid ""
 "* When the payslip is created the status is 'Draft'\n"
 "                \n"
 "* If the payslip is under verification, the status is 'Waiting'.\n"
 "                \n"
 "* If the payslip is confirmed then status is set to 'Done'.\n"
 "                \n"
 "* When user cancel payslip the status is 'Rejected'."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "<span class=\"o_form_label\">Payroll Rules</span>"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid ""
 "<span colspan=\"4\" nolabel=\"1\">This wizard will generate payslips for all"
 " selected employee(s) based on the dates and credit note specified on "
 "Payslips Run.</span>"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Address</strong>"
 msgstr "<strong>Adres</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Authorized signature</strong>"
 msgstr "<strong>Podpis autoryzujący</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Bank Account</strong>"
 msgstr "<strong>Konto bankowe</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Date From:</strong>"
 msgstr "<strong>Data od:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Date From</strong>"
 msgstr "<strong>Data od</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Date To:</strong>"
 msgstr "<strong>Data do:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Date To</strong>"
 msgstr "<strong>Data do</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Designation</strong>"
 msgstr "<strong>Designation</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Email</strong>"
 msgstr "<strong>Email</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Identification No</strong>"
 msgstr "<strong>Numer identyfikacyjny</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Name</strong>"
 msgstr "<strong>Nazwa</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Reference</strong>"
 msgstr "<strong>Odnośnik</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Register Name:</strong>"
 msgstr "<strong>Nazwa funduszu:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Total</strong>"
 msgstr "<strong>Suma</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.actions.act_window,help:hr_payroll_community_v13.action_contribution_register_form
 msgid ""
 "A contribution register is a third party involved in the salary\n"
 "            payment of the employees. It can be the social security, the\n"
 "            state or anyone that collect or inject money on payslips."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_account_accountant
 msgid "Account Accountant"
 msgstr "Konto Księgowy"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Accounting"
 msgstr "Księgowość"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Accounting Information"
 msgstr "Informacje księgowe"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__active
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__active
 msgid "Active"
 msgstr "Aktywne"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.actions.act_window,help:hr_payroll_community_v13.action_contribution_register_form
 msgid "Add a new contribution register"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Add an internal note..."
 msgstr "Dodaj notatkę wewnetrzną ..."
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contract_advantage_template_view_form
 msgid "Advantage Name"
 msgstr "Nazwa dodatku"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_children_salary_rules
 msgid "All Children Rules"
 msgstr "Wszystkie reguły podrzędne"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.ALW
 msgid "Allowance"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Always True"
 msgstr "Zawsze prawda"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__amount
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Amount"
 msgstr "Kwota"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_select
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_select
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Amount Type"
 msgstr "Rodzaj Kwoty"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Annually"
 msgstr "Co rok"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__appears_on_payslip
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__appears_on_payslip
 msgid "Appears on Payslip"
 msgstr "Pojawi się w pasku"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_python
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_python
 msgid ""
 "Applied this rule for calculation if condition is true. You can specify "
 "condition like basic > 1000."
 msgstr ""
 "Ta reguła będzie zastosowana, jeśli warunek będzie True (prawda). Warunek "
 "może wyglądać tak: basic > 1000."
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.BASIC
 msgid "Basic"
 msgstr "Podstawowe"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_basic
 msgid "Basic Salary"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_be_hr_payroll_community_v13
 msgid "Belgium Payroll"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Bi-monthly"
 msgstr "Co 2 miesiące"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Bi-weekly"
 msgstr "Co dwa tygodnie"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_form
 msgid "Calculations"
 msgstr "Obliczenia"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Cancel"
 msgstr "Anuluj"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Cancel Payslip"
 msgstr "Anuluj Pasek wypłaty"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:96
 #, python-format
 msgid "Cannot cancel a payslip that is done."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__category_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__category_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Category"
 msgstr "Kategoria"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Child Rules"
 msgstr "Reguły podrzędne"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__child_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__child_ids
 msgid "Child Salary Rule"
 msgstr "Podrzędne reguły wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__children_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__children_ids
 msgid "Children"
 msgstr "Podrzędne"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Children Definition"
 msgstr "Definicja podrzędnych"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Choose a Payroll Localization"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.run,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 msgid "Close"
 msgstr "Zamknij"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__code
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Code"
 msgstr "Kod"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payroll_community_v13_structure_view_kanban
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_view_kanban
 msgid "Code:"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Companies"
 msgstr "Firmy"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__company_id
 msgid "Company"
 msgstr "Firma"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.COMP
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Company Contribution"
 msgstr "Składki firmowe"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Computation"
 msgstr "Obliczenia"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Compute Sheet"
 msgstr "Oblicz listę"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_select
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_select
 msgid "Condition Based on"
 msgstr "Warunek oparty o"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Conditions"
 msgstr "Warunki"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_res_config_settings
 msgid "Config Settings"
 msgstr "Ustawienia konfiguracji"
 
 #. module: hr_payroll_community_v13
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_configuration
 msgid "Configuration"
 msgstr "Konfiguracja"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Confirm"
 msgstr "Potwierdź"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__contract_id
 msgid "Contract"
 msgstr "Kontrakt"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.hr_contract_advantage_template_action
 #: model:ir.ui.menu,name:hr_payroll_community_v13.hr_contract_advantage_template_menu_action
 msgid "Contract Advantage Templates"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_form
 msgid "Contribution"
 msgstr "Składka"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contribution_register
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__register_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__register_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Contribution Register"
 msgstr "Rejestr składek"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Contribution Register's Payslip Lines"
 msgstr "Pozycje funduszy paska wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_contribution_register_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_action_hr_contribution_register_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_tree
 msgid "Contribution Registers"
 msgstr "Rejestry składek"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_convanceallowance1
 msgid "Conveyance Allowance"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_ca_gravie
 msgid "Conveyance Allowance For Gravie"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__create_uid
 msgid "Created by"
 msgstr "Utworzona przez"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__create_date
 msgid "Created on"
 msgstr "Data utworzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__credit_note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__credit_note
 msgid "Credit Note"
 msgstr "Korekta"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__date_from
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__date_start
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__date_from
 msgid "Date From"
 msgstr "Data Początkowa"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__date_to
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__date_end
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__date_to
 msgid "Date To"
 msgstr "Data Końcowa"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.DED
 msgid "Deduction"
 msgstr "Potrącenie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__default_value
 msgid "Default value for this advantage"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract__schedule_pay
 msgid "Defines the frequency of the wage payment."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__struct_id
 msgid ""
 "Defines the rules that have to be applied to this payslip, accordingly to "
 "the contract chosen. If you let empty the field contract, this field isn't "
 "mandatory anymore and thus the rules applied will be all the rules set on "
 "the structure of all contracts of the employee valid for the chosen period"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__note
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Description"
 msgstr "Opis"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Details By Salary Rule Category"
 msgstr "Szczegóły wg kategorii reguł wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__details_by_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Details by Salary Rule Category"
 msgstr "Szcegóły przez kategorie zasad wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_contributionregister__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_payslipdetails__display_name
 msgid "Display Name"
 msgstr "Nazwa wyświetlana"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Done"
 msgstr "Wykonano"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Done Payslip Batches"
 msgstr "Wykonane listy płac"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Done Slip"
 msgstr "Pasek wykonany"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0 selection:hr.payslip.run,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Draft"
 msgstr "Projekt"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Draft Payslip Batches"
 msgstr "Projekty list płac"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Draft Slip"
 msgstr "Projekt listy płac"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_employee
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__employee_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__employee_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Employee"
 msgstr "Pracownik"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contract
 msgid "Employee Contract"
 msgstr "Umowa pracownika"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_employee_grade_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_v13_structure_list_view
 msgid "Employee Function"
 msgstr "Funkcja pracownika"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_view_hr_payslip_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_department_tree
 msgid "Employee Payslips"
 msgstr "Paski wypłat"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contract_advantage_template
 msgid "Employee's Advantage on Contract"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract__resource_calendar_id
 msgid "Employee's working schedule."
 msgstr "Czas pracy pracownika"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__employee_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Employees"
 msgstr "Pracownicy"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:92
 #, python-format
 msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:179
 #, python-format
 msgid "Error! You cannot create recursive hierarchy of Salary Rules."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__register_id
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__register_id
 msgid "Eventual third party involved in the salary payment of the employees."
 msgstr "Ewentualny podmiot zewnętrzny związany z wynagrodzeniem pracownika."
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_fix
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_fix
 msgid "Fixed Amount"
 msgstr "Kwota stała"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage
 msgid "For example, enter 50.0 to apply a percentage of 50%"
 msgstr "Na przykład, wprowadź 50.0 dla oprocentowania 50%"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/report/report_contribution_register.py:35
 #, python-format
 msgid "Form content is missing, this report cannot be printed."
 msgstr "Brakuje treści formularza, tego raportu nie można wydrukować"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_fr_hr_payroll_community_v13
 msgid "French Payroll"
 msgstr "Francuskie wynagrodzenie"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "General"
 msgstr "Ogólne"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid "Generate"
 msgstr "Generuj"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payslip_by_employees
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 msgid "Generate Payslips"
 msgstr "Generuj paski wypłat"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_employees
 msgid "Generate payslips for all selected employees"
 msgstr "Generuj odcinki wypłaty dla wybranych pracowników"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_sales_commission
 msgid "Get 1% of sales"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:182
 #, python-format
 msgid "Global Leaves"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_taxable
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.GROSS
 msgid "Gross"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Group By"
 msgstr "Grupuj wg"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_houserentallowance1
 msgid "House Rent Allowance"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_contributionregister__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_payslipdetails__id
 msgid "ID"
 msgstr "ID"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_run__credit_note
 msgid ""
 "If its checked, indicates that all payslips generated from here are refund "
 "payslips."
 msgstr ""
 "Zaznaczone oznacza, że wszystkie wygenerowane tutaj paski są paskami korekt."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__active
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__active
 msgid ""
 "If the active field is set to false, it will allow you to hide the salary "
 "rule without removing it."
 msgstr ""
 "Jeśli nie chcesz widzieć reguły płacowej na listach, ale nie chcesz jej "
 "usuwać, to odznacz pole aktywne."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_in_hr_payroll_community_v13
 msgid "Indian Payroll"
 msgstr "Indyjskie wynagrodzenie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__credit_note
 msgid "Indicates this payslip has a refund of another"
 msgstr "Oznacza, ze ten pasek jest zwrotem z innego paska"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Input Data"
 msgstr "Dane wejściowe"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__input_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__input_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Inputs"
 msgstr "Wprowadzanie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__note
 msgid "Internal Note"
 msgstr "Notatka wewnętrzna"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_view_kanban
 msgid "Is a Blocking Reason?"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__quantity
 msgid ""
 "It is used in computation for percentage and fixed amount. For e.g. A rule "
 "for Meal Voucher having fixed amount of 1€ per worked day can have its "
 "quantity defined in expression like worked_days.WORK100.number_of_days."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__amount
 msgid ""
 "It is used in computation. For e.g. A rule for sales having 1% commission of"
 " basic salary for per product can defined in expression like result = "
 "inputs.SALEURO.amount * contract.wage*0.01."
 msgstr ""
 "To jest stosowane do obliczeń. Np. Reguła prowizji sprzedażowej 1% "
 "wynagrodzenia podstawowego może być wyrażona jako  result = "
 "inputs.SALEURO.amount * contract.wage*0.01."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_contributionregister____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_payslipdetails____last_update
 msgid "Last Modified on"
 msgstr "Data ostatniej modyfikacji"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__write_uid
 msgid "Last Updated by"
 msgstr "Ostatnio aktualizowane przez"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__write_date
 msgid "Last Updated on"
 msgstr "Data ostatniej aktualizacji"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule_category__parent_id
 msgid ""
 "Linking a salary category to its parent is used only for the reporting "
 "purpose."
 msgstr ""
 "Łączenie kategorii wynagrodzenia do jego kategorii nadrzędnej jest stosowane"
 " tylko do celów raportowych."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__lower_bound
 msgid "Lower Bound"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract_advantage_template__lower_bound
 msgid "Lower bound authorized by the employer for this advantage"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__paid
 msgid "Made Payment Order ? "
 msgstr "Wykonano polecenie płatności ? "
 
 #. module: hr_payroll_community_v13
 #: model:res.groups,name:hr_payroll_community_v13.group_hr_payroll_community_v13_manager
 msgid "Manager"
 msgstr "Menedżer"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range_max
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range_max
 msgid "Maximum Range"
 msgstr "Zakres maksymalny"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_meal_voucher
 msgid "Meal Voucher"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range_min
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range_min
 msgid "Minimum Range"
 msgstr "Zakres minimalny"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Miscellaneous"
 msgstr "Różne"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Monthly"
 msgstr "Miesięcznie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__name
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Name"
 msgstr "Nazwa"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.NET
 msgid "Net"
 msgstr "Netto"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_net
 msgid "Net Salary"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:201
 #, python-format
 msgid "Normal Working Days paid at 100%"
 msgstr "Zwykłe dni robocze płatne 100%"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_form
 msgid "Notes"
 msgstr "Notatki"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__number_of_days
 msgid "Number of Days"
 msgstr "Liczba dni"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__number_of_hours
 msgid "Number of Hours"
 msgstr "Liczba godzin"
 
 #. module: hr_payroll_community_v13
 #: model:res.groups,name:hr_payroll_community_v13.group_hr_payroll_community_v13_user
 msgid "Officer"
 msgstr "Urzędnik"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Other Inputs"
 msgstr "Inne parametry"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__parent_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__parent_id
 msgid "Parent"
 msgstr "Nadrzędny"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__parent_rule_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__parent_rule_id
 msgid "Parent Salary Rule"
 msgstr "Reguła wynagrodzenia nadrzędnego"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__partner_id
 msgid "Partner"
 msgstr "Partner"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__payslip_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__slip_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__payslip_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Pay Slip"
 msgstr "Pasek wypłaty"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "PaySlip Batch"
 msgstr "Lista płac"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.payslip_details_report
 msgid "PaySlip Details"
 msgstr "Pasek wypłaty szczegółowo"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_payslip_lines_contribution_register
 msgid "PaySlip Lines"
 msgstr "Pozycje paska wypłaty"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.action_contribution_register
 msgid "PaySlip Lines By Conribution Register"
 msgstr "Pozycje paska wg funduszy"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "PaySlip Lines by Contribution Register"
 msgstr "Pozycje paska wypłaty wg Rejestru Składek"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "PaySlip Name"
 msgstr "Nazwa paska"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.open_payroll_modules
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_root
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll"
 msgstr "Wynagrodzenie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_report_hr_payroll_community_v13_report_contributionregister
 msgid "Payroll Contribution Register Report"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll Entries"
 msgstr "Zapisy wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_v13_structure_filter
 msgid "Payroll Structures"
 msgstr "Struktura wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll rules that apply to your country"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.action_report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Payslip"
 msgstr "Pasek wypłaty"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:82
 #, python-format
 msgid "Payslip 'Date From' must be earlier 'Date To'."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_run
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__payslip_run_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Payslip Batches"
 msgstr "Listy płac"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_payslip_lines
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__payslip_count
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Payslip Computation Details"
 msgstr "Wyliczenia Paska wypłaty szczegółowo"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_employee__payslip_count
 msgid "Payslip Count"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_report_hr_payroll_community_v13_report_payslipdetails
 msgid "Payslip Details Report"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_input
 msgid "Payslip Input"
 msgstr "Wejścia Paska wypłaty"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__input_line_ids
 msgid "Payslip Inputs"
 msgstr "Dane do paska wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_line
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_form
 msgid "Payslip Line"
 msgstr "Pozycja paska wypłaty"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_contribution_reg_payslip_lines
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__line_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Payslip Lines"
 msgstr "Pozycje paska wypłaty"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Payslip Lines by Contribution Register"
 msgstr "Pozycje paska wg funduszy"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_payslip_lines_contribution_register
 msgid "Payslip Lines by Contribution Registers"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__name
 msgid "Payslip Name"
 msgstr "Nazwa paska"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_worked_days
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__worked_days_line_ids
 msgid "Payslip Worked Days"
 msgstr "Dni robocze paska"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_hr_employee_payslip_list
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_employee__slip_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__slip_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.payroll_hr_employee_view_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_tree
 msgid "Payslips"
 msgstr "Paski płac"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payslip_run_tree
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payslip_run
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_tree
 msgid "Payslips Batches"
 msgstr "Generowanie Listy płac"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid "Payslips by Employees"
 msgstr "Listy płac wg pracowników"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage
 msgid "Percentage (%)"
 msgstr "Procentowo (%)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage_base
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage_base
 msgid "Percentage based on"
 msgstr "Procentowo bazując na"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Period"
 msgstr "Okres"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Post payroll slips in accounting"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Print"
 msgstr "Drukuj"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_professionaltax1
 msgid "Professional Tax"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_providentfund1
 msgid "Provident Fund"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_python_compute
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_python_compute
 msgid "Python Code"
 msgstr "Kod Python"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_python
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_python
 msgid "Python Condition"
 msgstr "Warunek Python"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Python Expression"
 msgstr "Wyrażenie Python"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__quantity
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__quantity
 msgid "Quantity"
 msgstr "Ilość"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "Quantity/Rate"
 msgstr "Ilość/Przelicznik"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Quantity/rate"
 msgstr "Ilość/Przelicznik"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Quarterly"
 msgstr "Kwartalnie"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Range"
 msgstr "Zakres"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range
 msgid "Range Based on"
 msgstr "Zakres bazujący na"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__rate
 msgid "Rate (%)"
 msgstr "Przelicznik (%)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__number
 msgid "Reference"
 msgstr "Odnośnik"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Refund"
 msgstr "Korekta"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:102
 #, python-format
 msgid "Refund: "
 msgstr "Refundacja: "
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__register_line_ids
 msgid "Register Line"
 msgstr "Pozycja funduszu"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 msgid "Rejected"
 msgstr "Odrzucone"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__salary_rule_id
 msgid "Rule"
 msgstr "Reguła"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_form
 msgid "Salary Categories"
 msgstr "Kategorie wynagrodzeń"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Salary Computation"
 msgstr "Obliczanie wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_salary_rule
 msgid "Salary Rule"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_salary_rule_category
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_salary_rule_category_filter
 msgid "Salary Rule Categories"
 msgstr "Kategorie Reguł wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Salary Rule Category"
 msgstr "Kategoria reguły wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_rule_input
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__input_id
 msgid "Salary Rule Input"
 msgstr "Wejście Reguły wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_salary_rule_form
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__rule_ids
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_action_hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_list
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_employee_grade_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Salary Rules"
 msgstr "Reguły wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:403
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:453
 #, python-format
 msgid "Salary Slip of %s for %s"
 msgstr "Wynagrodzenie %s dla %s"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payroll_community_v13_structure
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__struct_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_v13_structure_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_tree
 msgid "Salary Structure"
 msgstr "Struktura wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_view_hr_payroll_community_v13_structure_list_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_structure_view
 msgid "Salary Structures"
 msgstr "Struktury wynagrodzeń"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__schedule_pay
 msgid "Scheduled Pay"
 msgstr "Zaplanowane płatności"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Search Payslip Batches"
 msgstr "Szukaj list płac"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Search Payslip Lines"
 msgstr "Szukaj pozycji pasków"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Search Payslips"
 msgstr "Szukaj pasków"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Search Salary Rule"
 msgstr "Szukaj według reguł wynagrodzenia"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Semi-annually"
 msgstr "Półrocznie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__sequence
 msgid "Sequence"
 msgstr "Numeracja"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Set to Draft"
 msgstr "Ustaw jako projekt"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payroll_community_v13_configuration
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_global_settings
 msgid "Settings"
 msgstr "Ustawienia"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "States"
 msgstr "Stany"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__state
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__state
 msgid "Status"
 msgstr "Status"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__struct_id
 msgid "Structure"
 msgstr "Struktura"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__code
 msgid ""
 "The code of salary rules can be used as reference in computation of other "
 "rules. In that case, it is case sensitive."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_worked_days__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_rule_input__code
 msgid "The code that can be used in the salary rules"
 msgstr "Kod, który może być używany przy tworzeniu reguł płac"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_select
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_select
 msgid "The computation method for the rule amount."
 msgstr "Metoda obliczania dla wartości reguły."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__contract_id
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_worked_days__contract_id
 msgid "The contract for which applied this input"
 msgstr "Umowa, której dotyczą wprowadzone dane"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range_max
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range_max
 msgid "The maximum amount, applied for this rule."
 msgstr "Maksymalna wartość dla tej reguły"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range_min
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range_min
 msgid "The minimum amount, applied for this rule."
 msgstr "Wartość minimalna dla tej reguły"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range
 msgid ""
 "This will be used to compute the % fields values; in general it is on basic,"
 " but you can also use categories code fields in lowercase as a variable "
 "names (hra, ma, lta, etc.) and the variable basic."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__total
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Total"
 msgstr "Suma"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Total Working Days"
 msgstr "Suma dni roboczych"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__upper_bound
 msgid "Upper Bound"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract_advantage_template__upper_bound
 msgid "Upper bound authorized by the employer for this advantage"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__sequence
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__sequence
 msgid "Use to arrange calculation sequence"
 msgstr "Stosuj do kolejności obliczeń"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__appears_on_payslip
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__appears_on_payslip
 msgid "Used to display the salary rule on payslip."
 msgstr "Stosowane do wyświetlenia reguły płacowej na pasku"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 msgid "Waiting"
 msgstr "Oczekiwanie"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Weekly"
 msgstr "Tygodniowo"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Day"
 msgstr "Dzień roboczy"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Days"
 msgstr "Dni robocze"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Days & Inputs"
 msgstr "Dni robocze i dane do wprowadzenia"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__resource_calendar_id
 msgid "Working Schedule"
 msgstr "Godziny pracy"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:211
 #, python-format
 msgid "Wrong percentage base or quantity defined for salary rule %s (%s)."
 msgstr ""
 "Reguła wynagrodzenia %s (%s) zawiera niepoprawną podstawę procentu lub "
 "ilość."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:217
 #, python-format
 msgid "Wrong python code defined for salary rule %s (%s)."
 msgstr "Niepoprawny kod python dla reguły wynagrodzenia %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:240
 #, python-format
 msgid "Wrong python condition defined for salary rule %s (%s)."
 msgstr "Niepoprawny warunek python dla reguły wynagrodzenia %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:204
 #, python-format
 msgid "Wrong quantity defined for salary rule %s (%s)."
 msgstr "Reguła wynagrodzenia %s (%s) zawiera niepoprawną ilość."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:234
 #, python-format
 msgid "Wrong range condition defined for salary rule %s (%s)."
 msgstr "Niepoprawny warunek zakresu dla reguły wynagrodzenia %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:36
 #, python-format
 msgid "You cannot create a recursive salary structure."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:127
 #, python-format
 msgid "You cannot delete a payslip which is not draft or cancelled!"
 msgstr ""
 "Nie możesz usuwać paska, który nie jest w stanie Projekt lub Anulowane"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/wizard/hr_payroll_community_v13_payslips_by_employees.py:24
 #, python-format
 msgid "You must select employee(s) to generate payslip(s)."
 msgstr "Musisz wybrać pracowników do generacji pasków"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:525
 #, python-format
 msgid "You must set a contract to create a payslip line."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage_base
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage_base
 msgid "result will be affected to a variable"
 msgstr "rezultat będzie zależny od zmiennej"
 