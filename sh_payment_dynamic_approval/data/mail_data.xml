<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="email_template_for_approve_payment" model="mail.template">
            <field name="name">Account: Approve Payment</field>
            <field name="model_id" ref="sh_payment_dynamic_approval.model_account_payment" />
            <field name="subject">Submit for Approval</field>
            <field name="email_from"></field>
            <field name="partner_to"></field>

            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;">
                        <h2>
                            <b>Payment : Submitted for Approval</b>
                        </h2>
                        <br />
                        <br />
                        <p>
                            Dear Approver,
                            <br />
                            <br />
                            This is inform you that payment is submitted for approval.
                            <br />
                            <b>User:</b>
                            <t t-out="object.user_id.name or ''" />
                            <br />
                            <b>Total Amount:</b>
                            <t t-out="format_amount(object.amount, object.currency_id) or '' " />
                            <br />
                            <b>Approval Level:</b>
                            <t t-out="object.approval_config_id.name or ''" />
                            <br />
                            <b>Minimum Amount:</b>
                            <t t-out="format_amount(object.approval_config_id.min_amount, object.currency_id) or '' " />
                            <br />
                            <br />
                            Payment Approval information as below
                        </p>
                        <br />
                        <br />

                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th scope="col">Level</th>
                                    <th scope="col">Approvers</th>
                                    <th scope="col">Approved Date</th>
                                    <th scope="col">Approved By</th>
                                    <th scope="col">Status</th>
                                </tr>
                            </thead>


                            <t t-foreach="object.approval_info_line" t-as="line">
                                <tbody>
                                    <tr>
                                        <th>
                                            <t t-out="line.level or ''" />
                                        </th>
                                        <td>
                                            <t t-if="line.user_ids">
                                                <t t-foreach="line.user_ids" t-as="user">
                                                    <i><t t-out="user.name or ''" />,</i>
                                                </t>
                                            </t>
                                            <t t-else = "">
                                                <t t-foreach = "line.group_ids" t-as = "group">
                                                    <i><t t-out="group.name or ''" />,</i>
                                                </t>
                                            </t>
                                        </td>
                                        <td>
                                            <t t-out="line.approval_date or ''" />
                                        </td>
                                        <td>
                                            <t t-out="line.approved_by.name or ''" />
                                        </td>
                                        <td>
                                            <t t-out="line.status or ''" />
                                        </td>
                                    </tr>
                                </tbody>
                            </t>
                        </table>
                        <br />
                        <br />
                        if you are authorize approver as per approval level.
                        <br />
                        You can use the following link and approve the Payment
                        <br />
                        <br />

                        <div style="display: inline-block; margin: 15px; text-align: center">
                            <a t-att-href="'/mail/view?model=account.payment&amp;res_id=%s'%object.id" target="_blank" style="padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px">Payment Reference</a>
                        </div>
                        <br />
                        Thanks,
                    </p>
                </div>
            </field>
        </record>

        <record id="email_template_reject_payment" model="mail.template">
            <field name="name">Account: Reject Payment</field>
            <field name="model_id" ref="sh_payment_dynamic_approval.model_account_payment" />
            <field name="subject">Reject Payment</field>
            <field name="email_from"></field>
            <field name="partner_to"></field>

            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;">
                        <h2>
                            <b>Payment Rejected</b>
                        </h2>
                        <br />
                        <br />
                        Dear User,
                        <br />
                        <br />
                        Rejected Payment Information.
                        <br />
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th scope="col">Date</th>
                                    <th scope="col">Reject By</th>
                                    <th scope="col">Reason</th>
                                </tr>
                            </thead>
                            <tbody>
                                <td>
                                    <t t-out="object.rejection_date or ''" />
                                </td>
                                <td>
                                    <t t-out="object.reject_by.name or ''" />
                                </td>
                                <td>
                                    <t t-out="object.reject_reason or ''" />
                                </td>
                            </tbody>
                        </table>
                        <br />
                        <br />
                        You can use the following link to access the Payment.
                        <br />
                        <br />
                        <div style="display: inline-block; margin: 15px; text-align: center">
                            <a t-att-href="'/mail/view?model=account.payment&amp;res_id=%s'%object.id" target="_blank" style="padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px">Payment Reference</a>
                        </div>
                        <br />
                        Thanks,
                    </p>
                </div>
            </field>
        </record>

        <record id="email_template_posted_payment" model="mail.template">
            <field name="name">Account: Posted Payment</field>
            <field name="model_id" ref="sh_payment_dynamic_approval.model_account_payment" />
            <field name="subject">Posted Payment</field>
            <field name="email_from"></field>
            <field name="partner_to"></field>

            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;">
                        <h2>
                            <b>Payment Posted</b>
                        </h2>
                        <br />
                        <br />
                        Dear User,
                        <br />
                        <br />
                        This is inform you that your Payment is Posted.
                        <br />
                        You can use the following link to access the payment.
                        <br />
                        <br />
                        <div style="display: inline-block; margin: 15px; text-align: center">
                            <a t-att-href="'/mail/view?model=account.payment&amp;res_id=%s'%object.id" target="_blank" style="padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px">Payment Reference</a>
                        </div>
                        <br />
                        Thanks,
                    </p>
                </div>
            </field>
        </record>

    </data>
</odoo>
