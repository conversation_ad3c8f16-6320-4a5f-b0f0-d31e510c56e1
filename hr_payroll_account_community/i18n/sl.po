# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll_account_community
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-08-24 09:19+0000\n"
"Last-Translator: matjaz k <<EMAIL>>, 2019\n"
"Language-Team: Slovenian (https://www.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: hr_payroll_account_community
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_community.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_community.hr_salary_rule_form_inherit
msgid "Accounting"
msgstr "Knjigovodstvo"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "Knjigovodski zapis"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:114
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:129
#, python-format
msgid "Adjustment Entry"
msgstr "Postavka prilagoditve"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "Analitični konto"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_credit
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "Konto v dobro"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__date
msgid "Date Account"
msgstr "Datumski konto"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_debit
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "Konto v breme"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_contract
msgid "Employee Contract"
msgstr "Pogodba zaposlenega"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Generiranje plačilnih list za vse zaposlene, ki so izbrani."

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr ""
"Naj ostane prazno za uporabo obdobja glede na datum potrditve (plačilna "
"lista)."

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip
msgid "Pay Slip"
msgstr "Pay Slip"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Payslip Batches"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_line
msgid "Payslip Line"
msgstr "Payslip Line"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:65
#, python-format
msgid "Payslip of %s"
msgstr "Plačilna lista %s"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_contract__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_run__journal_id
msgid "Salary Journal"
msgstr "Dnevnik prodaje"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_salary_rule
msgid "Salary Rule"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_tax_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_tax_id
msgid "Tax"
msgstr "Davek"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:112
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr "Dnevnik stroškov \"%s\" nima pravilno nastavljenega konta v dobro!"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:127
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr "Dnevnik stroškov \"%s\" nima pravilno nastavljenega konta v breme!"
