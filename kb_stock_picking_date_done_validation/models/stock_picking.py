# -*- coding: utf-8 -*-

from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def _action_done(self):
        """
        Override _action_done to ensure date_done is set to scheduled_date
        when button_validate is called, regardless of any other customizations.
        """
        # Call the parent method first
        result = super(StockPicking, self)._action_done()

        # Force set date_done to scheduled_date for all pickings being validated
        for picking in self:
            if picking.state == 'done':
                # Use scheduled_date as the date_done value
                scheduled_date = picking.scheduled_date or fields.Datetime.now()

                # Use sudo() to bypass any potential access restrictions
                # and directly update the date_done field
                picking.sudo().write({'date_done': scheduled_date})
                _logger.info(
                    f"Stock Picking {picking.name}: date_done set to {scheduled_date} "
                    f"(from scheduled_date) when button_validate was called"
                )

        return result

    def button_validate(self):
        """
        Override button_validate to ensure date_done is set to scheduled_date.
        This method is called when the user clicks the Validate button.
        """
        _logger.info(f"button_validate called for pickings: {self.mapped('name')}")

        # Call the parent button_validate method
        result = super(StockPicking, self).button_validate()

        # After validation, ensure date_done is set to the scheduled_date
        # for any pickings that were successfully validated
        for picking in self:
            if picking.state == 'done':
                # Use scheduled_date as the date_done value
                scheduled_date = picking.scheduled_date or fields.Datetime.now()
                picking.sudo().write({'date_done': scheduled_date})
                _logger.info(
                    f"Stock Picking {picking.name}: Final date_done set to {scheduled_date} "
                    f"(from scheduled_date) after button_validate completion"
                )

        return result
