# -*- coding: utf-8 -*-
{
    'name': 'Stock Picking Date Done from Scheduled Date',
    'version': '********.0',
    'category': 'Inventory/Inventory',
    'summary': 'Set date_done to scheduled_date when button_validate is called',
    'description': """
Stock Picking Date Done from Scheduled Date
===========================================

This module ensures that the date_done field in stock.picking is set to the
scheduled_date when the button_validate method is called, overriding any other customizations.

Features:
---------
* Sets date_done to scheduled_date when validation occurs
* Takes precedence over backdate modules
* Maintains proper validation workflow
* Falls back to current datetime if scheduled_date is not set

    """,
    'author': 'Custom Development',
    'depends': [
        'stock',
        'sh_all_in_one_backdate',  # Ensure we load after backdate modules
    ],
    'data': [],
    'test': [
        'tests/test_stock_picking_date_done.py',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
