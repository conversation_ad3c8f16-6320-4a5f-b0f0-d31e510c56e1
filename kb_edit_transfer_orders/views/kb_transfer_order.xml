<odoo>
    <data>
        <record id="kb_transfer_order" model="ir.ui.view">
            <field name="name">kb_transfer_order</field>
            <field name="model">kb.transfer.orders.fields</field>
            <field name="priority">100</field>
            <field name="inherit_id" ref="kb_transfer_orders.transfer_orders_form_id"/>
            <field name="arch" type="xml">
                <xpath expr="//group" position="before">
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_entry"
                                type="object"
                                string="Entries"
                                class="oe_stat_button"
                                icon="fa-archive">
                        </button>
                        <button name="action_open_po"
                                type="object"
                                string="Po"
                                class="oe_stat_button"
                                icon="fa-archive">
                        </button>
                        <button name="action_open_so"
                                type="object"
                                string="So"
                                class="oe_stat_button"
                                icon="fa-archive">
                        </button>
                    </div>
                </xpath>
                <xpath expr="//field[@name='kb_outside_vehicle']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//group[2]//group[2]" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='kb_outside_driver']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='kb_amount']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='kb_kilometres']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='kb_vehicle']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//field[@name='kb_driverName']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='kb_visibility']" position="after">
                    <field name="kb_product_tmpl_id"/>
                    <field name="kb_vehicle" attrs="{'invisible':[('kb_trip_type','!=','in')]}"/>
                    <field name="kb_driver_id" attrs="{'invisible':[('kb_trip_type','!=','in')]}"/>
                    <field name="kb_out_vendor_id" attrs="{'invisible':[('kb_trip_type','!=','out')]}"/>
                    <field name="kb_transport_amount" attrs="{'invisible':[('kb_trip_type','!=','out')]}"/>
                    <field name="kb_outside_driver" attrs="{'invisible':[('kb_trip_type','!=','out')]}"
                           string="Trip Details"/>
                    <field name="kb_actual_direction" widget="url"/>
                    <field name="kb_google_map" widget="url"/>
                </xpath>
                <xpath expr="//header" position="inside">
                    <button name="action_get_gps_vehicle" type="object" string="Gps" invisible="1"/>
                </xpath>
                <xpath expr="//field[@name='kb_shipmentTypes']" position="after">
                    <field name="company_id"/>
                    <field name="kb_trip_date_start"/>
                    <field name="kb_trip_date_end"/>
                    <field name="kb_trip_type" widget="radio"/>
                </xpath>
                <xpath expr="//sheet" position="inside">
                    <notebook>
                        <page name="lines" string="Traffic line">
                            <field name="kb_transfer_order_lines">
                                <tree editable="bottom">
                                    <field name="kb_from"/>
                                    <field name="kb_to"/>
                                    <field name="kb_amount"/>
                                    <field name="kb_distance"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </xpath>
            </field>
        </record>
    </data>
</odoo>