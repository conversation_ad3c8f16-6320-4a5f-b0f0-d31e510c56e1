#loader {
  animation: loader 5s cubic-bezier(.8,0,.2,1) infinite;
  height: 40px;
  width: 41px;
  position: absolute;
  top:calc(50% - 20px);
  left:calc(50% - 20px);
  visibility: hidden;
  background-color: #c5a6c7;
}
@keyframes loader {
  90% { transform: rotate(0deg); }
  100% { transform: rotate(180deg); }
}
#top {
  animation: top 5s linear infinite;
  border-top: 20px solid #fff;
  border-right: 20px solid transparent;
  border-left: 20px solid transparent;
  height: 0px;
  width: 1px;
  transform-origin: 50% 100%;
}
@keyframes top {
  90% { transform: scale(0); }
  100% { transform: scale(0);}
}
#bottom {
  animation: bottom 5s linear infinite;
  border-right: 20px solid transparent;
  border-bottom: 20px solid #fff;
  border-left: 20px solid transparent;
  height: 0px;
  width: 1px;
  transform: scale(0);
  transform-origin: 50% 100%;
}
@keyframes bottom {
  10% { transform: scale(0); }
  90% { transform: scale(1); }
  100% { transform: scale(1); }
}
#line {
  animation: line 5s linear infinite;
  border-left: 1px dotted #fff;
  height: 0px;
  width: 0px;
  position: absolute;
  top: 20px;
  left: 20px;
}
@keyframes line {
  10% { height: 20px; }
  100% { height: 20px; }
}

/* --------- Loader ends ----------------*/

.py-main-container {
    height: 100%;
    display: flex;
    flex-flow: column nowrap;

    margin: 0;
    font-family: "Roboto", "Odoo Unicode Support Noto", sans-serif;
    font-size: 1.08333333rem;
    font-weight: 400;
    line-height: 1.5;
    color: #666666;
    text-align: left;
    background-color: #f1e4e4;

}
.py-filter-container {

}
.py-control-panel {
    border-bottom: 1px solid #cccccc;
    padding-top: 10px;
    padding-right: 16px;
    padding-bottom: 10px;
    padding-left: 16px;
    background-color: white;
}
.py-data-container {
    flex: 1 1 100%;
    position: relative;
    overflow: auto;
}

.py-data-summary {
    width: 100%;
    background-color: inherit;
    text-align: right;
    font-size: inherit;
    font-family: inherit
}

.py-data-summary  table{
    width: 100%;
    font-size: 13px;
    font-family: inherit;
    font-weight: bold;
    margin-top: 15px;
}

.py-breadcrumb {
    font-size: 18px;
}

.py-breadcrumb li {
    display: inline-block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
}

.py-control-div {
    display: flex;
    justify-content: space-between;
    min-height: 30px;
}

.py-control-panel .py_cntrl_left {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
}

.py-control-panel .py_cntrl_right {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
}

.py-control-panel .py-ctrl-buttons {
    box-sizing: border-box;
    display: block;
}

.py-btn-primary{
    color: #FFFFFF;
    background-color: #00A09D;
    border-color: #00A09D;
    border-radius: 0px;
}

.py-btn-secondary{
    color: #FFFFFF;
    background-color: #00A0AD;
    border-color: #00A09D;
    border-radius: 3px;
}

.py-search-buttons{
    display: block;
    margin: auto 0px;
}

.py-search-buttons a{
    color: white;
}

.py-search-buttons > div{
    border: 1px solid grey;
    padding: 0px 2px 0px 2px;
    border-radius: 6px;
    background-color: #00a0ad;
    color: white !important;
}

.py-search-btn-date,
.py-search-type,
.py-search-partner-type,
.py-search-partner-tags,
.py-search-reconciled-filter,
.py-search-date-filter,
.py-search-journals,
.py-search-partners,
.py-search-accounts,
.py-search-accounts-tag,
.py-search-analytics,
.py-search-analytic-tag,
.py-search-extra{
    white-space: nowrap;
    display: inline-block;
    cursor: pointer;
    user-select: none;
    position: relative;
}

.py-filters-menu{
    overflow: auto;
    z-index: 1;
    display: none;
    position: absolute;
    padding: 5px;
}

.dropdown-menu{
    border-radius: 3px;
}

.dropdown-item{
    display: block;
    width: 100%;
    clear: both;
    font-weight: 400;
    color: rgb(102, 102, 102);
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    padding: 0.25rem 1.5rem;
    border-width: 0px;
    border-style: initial;
    border-color: initial;
    border-image: initial;
    position: relative;
}

.py-data-container{
    background-color: white;
    padding:5px;
}

/* ============= Table for data =============== */

.system-data-table{
    background-color: white;
    padding: 0px 10px 0px 10px;
    margin-top: 50px;
    font-size: 12px;
    color: #666666;

    a{
        color: inherit;
    }

    thead > tr{
        th{
            padding-left: 15px;
        }
        .amt-head{
            text-align: right;
        }
        th:last-child{
            padding-right: 20px;
            padding-left: 0px;
        }
        th:first-child{
            padding-left: 5%;
        }
    }

    .py-total-line{
        background-color: #e6e6e6;
        border-bottom: 2px solid #a9a9a9;
        border-top: 2px solid #a9a9a9;
        font-weight: bold;
        color: #666666;
        td:first-child{
            text-align: right;
            padding-right:15px
        }
        td > span{
            margin-left: 15px;
        }
        .amt{
            text-align: right;
        }
    }

    .py-mline{
        border-bottom: 1px solid #bbb;
        border-top: 1px solid #bbb;
        background-color: #e6e6e6;
        font-weight: bold;
        color: #666666;
        td:first-child{
            padding-left:10px;
        }
        td > span{
            margin-left: 15px;
        }
        .amt{
            text-align: right;
        }
        :hover{
            cursor:pointer;
        }
    };

    ul{
        li{
            display: inline;
            a {
                border: 1px solid #a2a2bb;
                border-radius: 3px;
                margin: 0px 3px 0px 3px;
                padding: 0px 2px 0px 2px;
                cursor: pointer;
            }
        }
    };

    .py-mline-data-table{
        width:97%;
        margin: 0px 15px 0px 15px;
        thead > tr{
            th{
                padding-left: 15px;
            }
            th:last-child{
                padding-right: 0px;
                padding-left: 0px;
            }
        }

        tbody > tr{
            a{
                white-space: nowrap;
            }
            td{
                vertical-align: top;
            }
            td:first-child{
                width:8%;
            }
            td:nth-last-child(2){ // balance
                width:8%;
            }
            td:nth-last-child(3){ // cerdit
                width:8%;
            }
            td:nth-last-child(4){ // debit
                width:8%;
            }
            td:nth-child(2){ // Jrnl
                width:8%;
            }
            td:nth-child(3){ // Partner
                width:20%;
            }
            td:last-child{
                width:8%;
            }
        }


        .amt-head{
            text-align: right;
        };
        .amt{
            text-align: right;
        }
        tr:hover{
            //background-color:#00ede8;
            font-weight: bold;
        }
    }

    .view-source{
        white-space: nowrap;
    }

    .py-mline-sub{
        .view-source:hover{
            cursor:pointer;
        }
    };
}

// --------------------------- Ageing Report ------------------------ //

.system-data-table-age{
    background-color: white;
    padding: 0px 10px 0px 10px;
    margin-top: 50px;
    font-size: 12px;
    color: #666666;

    a{
        color: inherit;
    }

    thead > tr{
        th{
            padding-left: 15px;
        }
        .amt-head{
            text-align: right;
        }
        th:last-child{
            padding-right: 20px;
            padding-left: 0px;
        }
        th:first-child{
            padding-left: 5%;
        }
    }

    .py-total-line{
        background-color: #e6e6e6;
        border-bottom: 2px solid #a9a9a9;
        border-top: 2px solid #a9a9a9;
        font-weight: bold;
        color: #666666;
        td:first-child{
            text-align: right;
            padding-right:15px
        }
        td > span{
            margin-left: 15px;
        }
        .amt{
            text-align: right;
        }
    }

    .py-mline{
        border-bottom: 1px solid #bbb;
        border-top: 1px solid #bbb;
        background-color: #e6e6e6;
        font-weight: bold;
        color: #666666;
        td:first-child{
            padding-left:10px;
        }
        td > span{
            margin-left: 15px;
        }
        .amt{
            text-align: right;
        }
        :hover{
            cursor:pointer;
        }
    };

    ul{
        li{
            display: inline;
            a {
                border: 1px solid #a2a2bb;
                border-radius: 3px;
                margin: 0px 3px 0px 3px;
                padding: 0px 2px 0px 2px;
                cursor: pointer;
            }
        }
    };

    .py-mline-data-table{
        width:97%;
        margin: 0px 15px 0px 15px;
        thead > tr{
            th{
                padding-left: 15px;
            }
            th:last-child{
                padding-right: 0px;
                padding-left: 0px;
            }
        }

        tbody > tr{
            a{
                white-space: nowrap;
            }
            td{
                vertical-align: top;
            }
            td:first-child{ // Entry label
                width:8%;
            }
            td:nth-child(2){ // Due Date
                text-align: center;
            }
            td:nth-child(3){ // Journal
                width:15%;
            }
            td:nth-child(3){ // Account
                width:15%;
            }
            td:last-child{
                width:8%;
            }
        }


        .amt-head{
            text-align: right;
        };
        .amt{
            text-align: right;
        }
        tr:hover{
            //background-color:#00ede8;
            font-weight: bold;
        }
    }

    .view-source{
        white-space: nowrap;
    }

    .py-mline-sub{
        .view-source:hover{
            cursor:pointer;
        }
    };
}

// --------------------------- Trial Balance ------------------------ //

.system-data-table-tb{
    background-color: white;
    padding: 0px 10px 0px 10px;
    margin-top: 50px;
    color: #666666;

    a{
        color: inherit;
    }

    thead > tr:first-child{
        th{
            padding-left: 15px;
            text-align: center;
            background-color: #82728f;
            color: white;

        }
    }

    thead > tr:nth-child(2){
        th{
            padding-left: 15px;
            text-align: center;
        }
//         .amt-head{
//             text-align: right;
//         }
        th:nth-child(4),th:nth-child(7),th:nth-child(10){
            background-color: cyan;
            border-right: 2px solid grey;
            border-left: 2px solid grey;
        }
        th:last-child{
            padding-right: 20px;
            padding-left: 0px;
        }
    }

    tbody > tr:last-child{
        background-color: #dcdcdc;
    }

    .py-mline{
        border-bottom: 1px solid #bbb;
        background-color: white;
        //border-top: 2px solid #bbb;
        color: #666666;
        td:first-child{
            padding-left:20px;
        }
        td > span{
            margin-left: 15px;
        }
        .bld{
            font-weight: bold;
        }
        .amt{
            text-align: right;
        }
        :hover{
            cursor:pointer;
        }
    };

    .py-mline-data-table{
        width:97%;
        margin: 0px 15px 0px 15px;
        thead > tr{
            th{
                padding-left: 15px;
            }
            th:last-child{
                padding-right: 0px;
                padding-left: 0px;
            }
        }
        .amt-head{
            text-align: right;
        };
        .amt{
            text-align: right;
        }
        tr:hover{
            background-color:#00ede8;
        }
    }

    .view-source{
        white-space: nowrap;
    }

    .py-mline-sub{

        .view-source:hover{
            cursor:pointer;
        }
    };
//     .tb-data-sec-1,.tb-head-sec-1{
//         border-bottom: 1px solid blue;
//     }
//     .tb-data-sec-2,.tb-head-sec-2{
//         border-bottom: 1px solid blue;
//     }
//     .tb-data-sec-3,.tb-head-sec-3{
//         border-bottom: 1px solid blue;
//     }
    .bld.tb-data-sec-1{
        background-color: cyan;
        border-right: 2px solid grey;
        border-left: 2px solid grey;
    }
    .bld.tb-data-sec-2{
        background-color: cyan;
        border-right: 2px solid grey;
        border-left: 2px solid grey;
    }
    .bld.tb-data-sec-3{
        background-color: cyan;
        border-right: 2px solid grey;
        border-left: 2px solid grey;
    }
}

// --------------------------- Financial Reports ------------------------ //
.system-data-table-fr{
    background-color: white;
    padding: 0px 10px 0px 10px;
    margin-top: 50px;
    color: #666666;

    a{
        color: inherit;
    }

    thead > tr{
        th{
            padding-left: 15px;
        }
        .amt-head{
            text-align: right;
        }
        th:last-child{
            padding-right: 20px;
            padding-left: 0px;
        }
    }
    tr:hover{
        background-color:#00fffa;
    }

    .py-mline{
        border-bottom: 1px solid #bbb;
        background-color: white;
        //border-top: 2px solid #bbb;
        color: #666666;
        td:first-child{
            padding-left:20px;
        }
        td > span{
            margin-left: 15px;
        }
        .bld{
            font-weight: bold;
        }
        .amt{
            text-align: right;
        }
        :hover{
            cursor:pointer;
        }
        td:last-child{
            padding-right: 20px;
            padding-left: 0px;
        }
    };

    .py-mline-data-table{
        width:97%;
        margin: 0px 15px 0px 15px;
        thead > tr{
            th{
                padding-left: 15px;
            }
            th:last-child{
                padding-right: 0px;
                padding-left: 0px;
            }
        }
        .amt-head{
            text-align: right;
        };
        .amt{
            text-align: right;
        }
        tr:hover{
            background-color:#00ede8;
        }
    }

    .view-source{
        white-space: nowrap;
    }

    .py-mline-sub{
        .view-source:hover{
            cursor:pointer;
        }
    };
}