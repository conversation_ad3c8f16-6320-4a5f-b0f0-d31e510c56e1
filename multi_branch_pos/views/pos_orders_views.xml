<?xml version="1.0" encoding="UTF-8"?>
<odoo>
	<!--inheriting pos.order form view-->
	<record id="pos_order_view_form" model="ir.ui.view">
		<field name="name">pos.order.view.form.inherit.multi.branch.pos</field>
		<field name="model">pos.order</field>
		<field name="inherit_id" ref="point_of_sale.view_pos_pos_form"/>
		<field name="arch" type="xml">
			<xpath expr="//page[@name='extra']//field[@name='company_id']"
				   position="after">
				<field name="branch_id" options="{'no_create': True}"/>
			</xpath>
		</field>
	</record>
	<!--inheriting pos.order filter view-->
	<record id="pos_order_view_filter" model="ir.ui.view">
		<field name="name">pos.order.view.filter.inherit.multi.branch.pos</field>
		<field name="model">pos.order</field>
		<field name="inherit_id" ref="point_of_sale.view_pos_order_filter"/>
		<field name="arch" type="xml">
			<xpath expr="//search/group" position="inside">
				<filter string="Branch" name="Branch"
						context="{'group_by':'branch_id'}"/>
			</xpath>
		</field>
	</record>
	<!--inheriting pos.order tree view-->
	<record id="pos_order_view_tree" model="ir.ui.view">
		<field name="name">pos.order.view.tree.inherit.multi.branch.pos</field>
		<field name="model">pos.order</field>
		<field name="inherit_id" ref="point_of_sale.view_pos_order_tree"/>
		<field name="arch" type="xml">
			<xpath expr="//field[@name='partner_id']" position="after">
				<field name="branch_id"/>
			</xpath>
		</field>
	</record>
</odoo>