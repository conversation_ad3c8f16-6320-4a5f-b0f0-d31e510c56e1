<?xml version="1.0"?>
<odoo >

    <record id = "view_search_maintenance_reports" model = "ir.ui.view" >
        <field name = "name" >search_maintenance.wizard.form</field >
        <field name = "model" >carshop_maintenance_wizard</field >
        <field name = "arch" type = "xml" >
            <form string = "Print Report" >
                <group >

                     <group >
                        <field name = "operating_code"/>
                    </group >

                    <group >


                    </group >

                </group >

                <footer >
                    <button name = "search_maintenance" string = "Print" type = "object" class = "btn-primary" />
                    <button string = "Cancel" special = "cancel" class = "btn-secondary" />
                </footer >
            </form >
        </field >
    </record >

    <record id = "action_maintenance_wizard_report" model = "ir.actions.act_window" >
        <field name = "name" >Maintenance Report</field >
        <field name = "type" >ir.actions.act_window</field >
        <field name = "res_model" >carshop_maintenance_wizard</field >
        <field name = "view_mode" >form</field >
        <field name = "view_id" ref = "view_search_maintenance_reports" />

        <field name = "target" >new</field >
    </record >


    <menuitem id = "menu_view_search_maintenance_reports_shop"
              name = "Maintenance Reports"
              action = "action_maintenance_wizard_report"
              groups = "kb_car_workshop_s.group_end_of_administration"
              parent = "car_workshop"
              sequence = "100" />


</odoo >
