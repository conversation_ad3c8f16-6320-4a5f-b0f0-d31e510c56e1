<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="view_hr_payslip_leave_salary_form">
            <field name="name">hr.payslip.form</field>
            <field name="model">hr.payslip</field>
            <field name="inherit_id" ref="hr_payroll_community.view_hr_payslip_form"/>
            <field name="arch" type="xml">
                <field name="credit_note" position="after">
                    <field name="leave_salary" attrs="{'readonly': [('state','=', 'done')]}"/>
                </field>
            </field>
        </record>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">Configure Payroll</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="hr_payroll_community.res_config_settings_view_form_payroll"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@id='hr_payroll_localization']" position="after">
                    <h2>Leaves</h2>
                    <div class="row mt16 o_settings_container" id="hr_leave_salary">
                        <div class="col-md-6 col-xs-12 o_setting_box">
                            <div class="o_setting_right_pane">
                                <h3>Leave Salary</h3>
                                <div class="text-muted">
                                    Leave salary calculation
                                </div>
                                <div class="mt16">
                                    <field name="default_leave_salary" widget="radio"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>