 
/*
Created by: @LogicalStack
DEMO URL: http://logicalstack/demo/

[TABLE OF CONTENTS]

1.  COMMON CSS Style for all Horizontal and Vertical  Navigation

*/

/* Common CSS for  */
html{}
body{
	background: #e9ecf3; 
	font-family: "Open Sans",sans-serif; 
	font-weight: 400;
	overflow-x: hidden;
	position:relative;
	color: #333;
    padding: 0 ;
    margin: 0;
    direction: "ltr";
	font-size: 14px; 
	width:100%;
	min-height:100%;
}
 
a:focus {
    outline: none;
    outline: none ; 
    outline-offset: 0px;
}
.scoop .scoop-container {
  position: relative;
  background: #d5dae6;
}
.scoop .scoop-header {
  position: relative;
  display:block;
}
.scoop .scoop-navbar {
  display:block;
}
.scoop-main-container{
	display:block;
	position:relative;
	background:#e9e9e9;
}
.scoop .scoop-content {
  position: relative;
  display:block;
}
.scoop-inner-navbar {
	display:block;
	position:relative;
}
.scoop-inner-content {
	padding:10px;
}
.scoop .scoop-navbar .scoop-item {
  display: block;
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
} 
.scoop  .scoop-navbar .scoop-item > li {
  display: block;
  list-style: outside none none;
  margin: 0;
  padding: 0;
  position: relative;
}
.scoop  .scoop-navbar .scoop-item  > li > a {
  display: block;
  font-size: 14px;
  padding: 0 15px;
  text-decoration:none;
  position: relative;
}
.scoop .scoop-navbar .scoop-item  .scoop-hasmenu .scoop-submenu{
  list-style: outside none none;
  margin: 0;
  padding: 0;
} 
.scoop .scoop-navbar .scoop-item  .scoop-hasmenu .scoop-submenu li{
  display: block;
  list-style: outside none none;
  margin: 0;
  padding: 0;
  position: relative;
}
.scoop .scoop-navbar .scoop-item  .scoop-hasmenu .scoop-submenu li > a {
  display: block;
  font-size: 14px;
  padding: 0 15px;
  text-decoration:none;
  position: relative;
}
.scoop .scoop-navbar .scoop-item   > li > a > .scoop-micon {
    font-size: 15px;
    padding-right: 10px;
}
.scoop .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-micon {
  font-size: 10px;
  padding-right: 5px;
}
 
.scoop-inner-navbar{
	height:100%;
	padding-bottom:50px
}
.scoop[theme-layout="vertical"] .scoop-header {
  height: 50px;
  width:100%;
  box-shadow: 2px 6px 6px -8px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 2px 6px 6px -8px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 2px 6px 6px -8px rgba(0, 0, 0, 0.2);
}

.scoop[theme-layout="vertical"] .scoop-header .scoop-left-header  {
	display:block;
	z-index:1028;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-left-header .scoop-logo {
  opacity: 1;
  text-align: center;
  visibility: visible;
  height:50px;
  white-space: nowrap;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-left-header .scoop-logo a {
  display: block;
  text-decoration: none;
  padding: 9px 0;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-left-header .scoop-logo .logo-icon {
	bottom: -21px;
    font-size: 60px;
    left: 8px;
    position: absolute;
	z-index: -1;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-header.iscollapsed .scoop-left-header .scoop-logo .logo-icon {
    font-size: 45px;
	bottom: -7px;
	left: 5px;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
}
.scoop[theme-layout="vertical"][scoop-device-type="desktop"] .scoop-header .scoop-left-header .scoop-logo .logo-text,
.scoop[theme-layout="vertical"][scoop-device-type="tablet"] .scoop-header .scoop-left-header .scoop-logo .logo-text,
.scoop[theme-layout="vertical"][scoop-device-type="phone"] .scoop-header.iscollapsed .scoop-left-header .scoop-logo .logo-text {
  font-family: open sans;
  font-size: 16px;
  font-weight: 600;
  left: 60px;
  margin: 0 auto !important;
  opacity: 1;
  position: absolute;
  text-transform: uppercase;
  top: 12px;
  transform: rotateX(0deg);
  -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg); 
  -moz-transform: rotateX(0deg); 
  -o-transform: rotateX(0deg);
  visibility: visible;
}
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-header .scoop-left-header .scoop-logo .logo-text,
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-header .scoop-left-header .scoop-logo .logo-text,
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-header.iscollapsed .scoop-left-header .scoop-logo .logo-text {
  left: 15px;
  top: 13px;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header .scoop-logo .logo-text {
    font-family: open sans;
    font-size: 16px;
    font-weight: 400;
    margin: 0 auto !important;
    opacity: 1;
    visibility: visible;
    transform: rotateX(0deg);
	-webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg); 
  -moz-transform: rotateX(0deg); 
  -o-transform: rotateX(0deg);
    position: relative;
    top: 5px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-header.iscollapsed .scoop-left-header .scoop-logo .logo-text {
  opacity: 0;
  position: absolute;
  transform: rotateX(90deg);
   -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
  visibility: hidden;
}
.scoop[theme-layout="vertical"][vertical-nav-type="offcanvas"] .scoop-header.iscollapsed .scoop-left-header .scoop-logo {
 opacity: 0;
 visibility: hidden;
 transform: rotateX(90deg);
 -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg); 
}
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-header .scoop-left-header .scoop-logo a  .hide-in-smallsize{
display:none;
}

.scoop[theme-layout="vertical"] .scoop-header .sidebar_toggle a {
  border-radius: 4px;
  float: left;
  font-size: 18px;
  height: 35px;
  margin-right: 5px;
  position: relative;
  text-align: center;
  top: 7px;
  width: 40px;
  border-width:0px;
  border-style:solid;
}  
.scoop[theme-layout="vertical"] .scoop-header .sidebar_toggle a i {
  position: relative;
  top: -7px;
}
.scoop[theme-layout="vertical"] .scoop-header .sidebar_toggle a:hover {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
}
.scoop[theme-layout="vertical"] .scoop-header .sidebar_toggle a:hover, .sidebar_toggle a:focus {
    text-decoration: none;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header{
    line-height: 50px;
    padding: 0 15px;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rl-header {
	float: left;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rr-header {
	float: right;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rl-header {
	padding-left: 20px;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"] .scoop-header .scoop-right-header .scoop-rl-header {
	padding-left: 0px;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rl-header ul, .scoop-right-header .scoop-rr-header ul {
  list-style: outside none none;
  margin: 0;
  padding: 0;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rl-header ul > li, 
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rr-header ul > li {
  float: left;
  list-style: outside none none;
  margin: 0;
  padding: 0 5px;
  position: relative;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rl-header ul > li > a, 
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rr-header ul > li > a {
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rl-header li.icons > a > i,
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rr-header li.icons > a > i {
  font-size: 18px;
  position: relative;
  top: 3px;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rl-header li.icons > a, 
.scoop[theme-layout="vertical"] .scoop-header .scoop-right-header .scoop-rr-header li.icons > a{
  padding: 0 5px;
}

.scoop[theme-layout="vertical"] .scoop-right-header .scoop-badge {
  background-color: #777;
  border-radius: 10px;
  color: #fff;
  font-size: 10px;
  font-weight: 700;
  line-height: 1;
  min-height: 17px;
  min-width: 10px;
  opacity: 1;
  padding: 4px 7px;
  position: absolute;
  right: -4px;
  text-align: center;
  top: 9px;
  transition: opacity 0.3s linear 0s;
  -webkit-transition: opacity 0.3s linear 0s;
	-ms-transition: opacity 0.3s linear 0s;
	-moz-transition: opacity 0.3s linear 0s;
	-o-transition: opacity 0.3s linear 0s;
  vertical-align: middle;
  visibility: visible;
  white-space: nowrap;
}
.scoop[theme-layout="vertical"] .scoop-badge.badge-success {
  background-color: #70ca63;
  color: #fff;
}
.scoop[theme-layout="vertical"] .scoop-badge.badge-warning {
  background-color: #fecd33;
  color: #fff;
}
.scoop[theme-layout="vertical"] .scoop-badge.badge-danger {
  background-color: #f13b48;
  color: #fff;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"] .hide-small-device {
	display:none;
}
 
.scoop[theme-layout="vertical"] .scoop-navbar {
  z-index:1027;
  webkit-box-shadow: 0 0 5px 0 rgba(0,0,0,.1);
    -moz-box-shadow: 0 0 5px 0 rgba(0,0,0,.1);
    box-shadow: 0 0 5px 0 rgba(0,0,0,.1);
	height:100%;
}
.scoop[theme-layout="vertical"] .scoop-content {
  position: relative;
  display:block;
}
.scoop[theme-layout="vertical"] .scoop-container {
    overflow: hidden;
	position: relative;
	margin: 0 auto;
}
.scoop[theme-layout="vertical"] .scoop-main-container {
	position: relative;
	margin: 0 auto;
}
.scoop[theme-layout="vertical"].scoop-wrapper{
	position: relative;
	margin: 0 auto;
}
 
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-search {
  border-top-width: 1px;
  border-top-style:solid;
  padding: 5px 10px 20px;
  position: relative;
}
.scoop[theme-layout="vertical"][vnavigation-view="view2"] .scoop-navbar .scoop-search {
  border-radius: 4px 4px 0 0;
}
.search-wrapper {}
.scoop-search input[type="text"]{
	display: block;
    margin: 0;
    width: 100%;
    font-family: "Open Sans", sans-serif;
    font-size: 14px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    -webkit-border-radius: none;
    -moz-border-radius: none;
    -ms-border-radius: none;
    -o-border-radius: none;
    border-radius: none;
	background: transparent; 
}
.scoop-search  input[type="text"] {
    padding: 6px 22px 6px 3px;
    border: none; 
	border-bottom-width:1px;
	border-bottom-style:solid;
    -webkit-transition: border 0.3s;
    -moz-transition: border 0.3s;
    -o-transition: border 0.3s;
    transition: border 0.3s;
}
.scoop-search input[type="text"]:focus, .style-4 input[type="text"].focus {
    border-bottom-width: 1px;
	border-bottom-style:solid;
}
 
.scoop-search .search-icon {
  cursor: pointer;
  margin-top: -33px;
  padding: 4px;
  position: absolute;
  right: 10px;
}
.scoop[vertical-nav-type="collapsed"] .scoop-search-box  {
	visibility: hidden;
	opacity:0;
	transform:rotateX(90deg);
	 -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
	 position: absolute;
}
.scoop[vertical-nav-type="collapsed"] .scoop-search input[type="text"] {
  padding: 10px 22px 10px 3px;
}
 .searchbar-toggle:before {
  content: "\f002";
  cursor: pointer;
  float: right;
  font-family: FontAwesome;
  left: 0;
  position: absolute;
  top: 10px;
  visibility: hidden;
  opacity:0;
  transform:rotateX(90deg);
   -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
}
.scoop[vertical-nav-type="collapsed"] .searchbar-toggle:before  {
	left: 15px;
	top: 10px;
	visibility: visible;
    opacity:1;
    transform:rotateX(0deg);
	 -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg); 
  -moz-transform: rotateX(0deg); 
  -o-transform: rotateX(0deg);
}
.scoop[vertical-nav-type="collapsed"] .scoop-search.open .searchbar-toggle:before  {
	 content: "\f05c";
}
.scoop[vertical-nav-type="collapsed"] .scoop-search.open .scoop-search-box {
  margin-left: 35px;
  opacity: 1;
  position: absolute;
  top: 0px;
  transform: rotateX(0deg);
   -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg); 
  -moz-transform: rotateX(0deg); 
  -o-transform: rotateX(0deg);
  visibility: visible;
  width: 200px;
  padding-right: 10px;
}
.scoop[vertical-nav-type="collapsed"] .scoop-search .search-icon {
    margin-top: -35px;
}
.searchbar-toggle{}
/* Searchbar in asidebar  CSS start here*/

.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  > li > a {
	text-align: left;
	padding:8px 15px;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  .scoop-hasmenu .scoop-submenu{
  position: relative;
  width:100%;
} 
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  .scoop-hasmenu .scoop-submenu li > a {
  text-align: left;
  padding: 8.7px 10px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="expanded"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li > a {
  padding-left: 30px;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  .scoop-hasmenu .scoop-submenu{
  width:100%;
  list-style: outside none none;
  margin: 0;
  padding: 0;
  opacity: 0;
  visibility: hidden;
  position:absolute; 
} 

.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  .scoop-hasmenu.scoop-trigger .scoop-submenu{
	-o-transform-origin: 0% 0%;
  -ms-transform-origin: 0% 0%;
  -moz-transform-origin: 0% 0%;
  -webkit-transform-origin: 0% 0%;
  transform-origin: 10% 10%;
  -o-transition: -o-transform 0.5s, opacity 0.5s;
  -ms-transition: -ms-transform 0.5s, opacity 0.5s;
  -moz-transition: -moz-transform 0.5s, opacity 0.5s;
  -webkit-transition: -webkit-transform 0.5s, opacity 0.5s;
  transition: transform 0.5s, opacity 0.5s;
} 
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  .scoop-hasmenu .scoop-submenu{
   transform-style: preserve-3d;
  -o-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  transform: rotateX(-90deg);
  -o-transform: rotateX(-90deg);
  -moz-transform: rotateX(-90deg);
  -webkit-transform: rotateX(-90deg);
} 
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  .scoop-hasmenu.scoop-trigger  > .scoop-submenu{
	position:relative;
	opacity: 1;
	visibility: visible;
	transform: rotateX(0deg);
	-o-transform: rotateX(0deg);
	-moz-transform: rotateX(0deg);
	-webkit-transform: rotateX(0deg); 
}

/*##################### Item Border Style Css Start Here##################### */
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item[item-border="true"][item-border-style="dashed"]  li > a {
	border-bottom-style: dashed;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item[item-border="true"][item-border-style="dotted"]  li > a {
	border-bottom-style: dotted;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item[item-border="true"][item-border-style="solid"]  li > a {
	border-bottom-style: solid;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item[item-border="false"] > li > a {
    border-bottom-width: 0px;
} 
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item[item-border="true"] > li > a {
    border-bottom-width: 1px;
} 

.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item[subitem-border="false"] .scoop-hasmenu .scoop-submenu li > a {
    border-bottom-width: 0px;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item[subitem-border="true"] .scoop-hasmenu  .scoop-submenu li > a {
    border-bottom-width: 1px;
}
/* #####################Item Border Stle Css Close Here #####################*/
 /* DropDown Icon Style Start Here */
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  > li.scoop-hasmenu > a:after {
  float: right;
  font-style: normal;
  margin-left: 3px;
  font-size: 10px;
  position: absolute;
  right: 15px;
  top:0px;
  padding-top: 13px;
  transition: opacity 0.3s linear;
  -webkit-transition: opacity 0.3s linear;
	-ms-transition: opacity 0.3s linear;
	-moz-transition: opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu > a:after {
  float: right;
  font-style: normal;
  margin-left: 3px;
  font-size: 10px;
  position: absolute;
  right: 15px;
  top:0px;
  padding-top: 13px;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  float: right;
  font-style: normal;
  margin-left: 3px;
  position: absolute;
  right: 15px;
  font-size: 12px;
  top:0px;
  padding-top: 13px;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu.scoop-trigger > a:after {
  float: right;
  font-style: normal;
  margin-left: 3px;
  position: absolute;
  right: 15px;
  font-size: 12px;
  top:0px;
  padding-top: 13px;
  transition: opacity 0.3s linear;
   -webkit-transition: opacity 0.3s linear;
	-ms-transition: opacity 0.3s linear;
	-moz-transition: opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
}
 
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  li.scoop-hasmenu[dropdown-icon="style1"] > a:after {
  content: "\e606";
  font-family: simple-line-icons;
}

.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  li.scoop-hasmenu.scoop-trigger[dropdown-icon="style1"] > a:after {
  content: "\e604";
  font-family: simple-line-icons;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  li.scoop-hasmenu[dropdown-icon="style2"] > a:after {
  content: "\e095";
  font-family: simple-line-icons;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  li.scoop-hasmenu.scoop-trigger[dropdown-icon="style2"] > a:after {
  content: "\e615";
  font-family: simple-line-icons;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  li.scoop-hasmenu[dropdown-icon="style3"] > a:after {
  content: "\f218";
  font-family: Ionicons;
  font-size:11px;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  li.scoop-hasmenu.scoop-trigger[dropdown-icon="style3"] > a:after {
  content: "\f209";
  font-family: Ionicons;
  font-size:11px;
}

/* #####################Toggle animation Start here #####################*/
/* .scoop.scoop-toggle-animate .scoop-navbar,
.scoop.scoop-toggle-animate .scoop-header .scoop-left-header ,
.scoop.scoop-toggle-animate .scoop-content,
.scoop.scoop-toggle-animate .scoop-header .scoop-right-header {
	-webkit-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;

}  */
/* .scoop.scoop-toggle-animate .scoop-navbar .scoop-item  > li  > a .scoop-mtext{
	transition-delay: 0.20s;
	-webkit-transition: opacity 0.35s linear;
    -moz-transition: opacity 0.35s linear;
    -o-transition: opacity 0.435s linear;
    transition: opacity 0.35s linear;
	transform: rotateX(90deg);
} */
/* .scoop.scoop-toggle-animate .scoop-navbar .scoop-item > li.scoop-trigger > a .scoop-mtext {
	transition: all 0.2s linear;
	 -webkit-transition: all 0.2s linear;
	-ms-transition: all 0.2s linear;
	-moz-transition: all 0.2s linear;
	-o-transition: all 0.2s linear;
	transform: rotateX(90deg);
	 -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
}  */
/* .scoop.scoop-toggle-animate  .scoop-navbar .scoop-item li a .scoop-badge {
	transition: all 0.2s linear;
	 -webkit-transition: all 0.2s linear;
	-ms-transition: all 0.2s linear;
	-moz-transition: all 0.2s linear;
	-o-transition: all 0.2s linear;
	transform: rotateX(90deg);
	 -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
	
} */

/* .scoop.scoop-toggle-animate .scoop-navbar .scoop-item  li.scoop-hasmenu  > a:after {
	transition: all 0.3s linear;
	 -webkit-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-moz-transition:all 0.3s linear;
	-o-transition: all 0.3s linear;
	transform: rotateX(90deg);
	 -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
} */ 
/* .scoop.scoop-toggle-animate .scoop-navbar .scoop-item  li.scoop-hasmenu.scoop-trigger  a:after {
	transition: all 0.3s linear;
	 -webkit-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-moz-transition:all 0.3s linear;
	-o-transition: all 0.3s linear;
	transform: rotateX(90deg);
	 -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
}  */ 
/* .scoop.scoop-toggle-animate  .scoop-left-header .scoop-logo{
	transition: width 0.3s linear;
	 -webkit-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-moz-transition:all 0.3s linear;
	-o-transition: all 0.3s linear;
} */
/* .scoop.scoop-toggle-animate .scoop-left-header .logo-text{
	transition: all 0.3s linear;
	 -webkit-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-moz-transition:all 0.3s linear;
	-o-transition: all 0.3s linear;
	opacity: 0;
    visibility: hidden;
	transform: rotateX(90deg);
	 -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
}  */
/* .scoop.scoop-toggle-animate .scoop-navbar .scoop-item > li.scoop-hasmenu > a:after {
	transform: rotateX(90deg);
	 -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
	transition: opacity 0.3s linear;
	 -webkit-transition: opacity 0.3s linear;
	-ms-transition: opacity 0.3s linear;
	-moz-transition:opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
	
} */

 
 
/* #####################Toggle animation Close here##################### */


 /* DropDown Icon Style Close Here */

/*###########################Vertical Componet Position CSS Start Here ###############################*/

.scoop[theme-layout="vertical"] .scoop-header .scoop-left-header[scoop-lheader-position="relative"]  {
	position: relative;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-left-header[scoop-lheader-position="fixed"]  {
	position: fixed;
}
.scoop[theme-layout="vertical"] .scoop-header .scoop-left-header[scoop-lheader-position="absolute"]  {
	position: absolute;
}


.scoop[theme-layout="vertical"] .scoop-navbar[ scoop-navbar-position="relative"] {
  position: relative;
}
.scoop[theme-layout="vertical"] .scoop-navbar[ scoop-navbar-position="fixed"] {
  position: fixed;
}
.scoop[theme-layout="vertical"] .scoop-navbar[ scoop-navbar-position="absolute"] {
  position: absolute;
}


.scoop[theme-layout="vertical"] .scoop-header[scoop-header-position="relative"]  {
	position: relative;
}
.scoop[theme-layout="vertical"] .scoop-header[scoop-header-position="fixed"]  {
	position: fixed;
	z-index: 1028;
}
.scoop[theme-layout="vertical"] .scoop-header[scoop-header-position="absolute"]  {
	position: absolute;
}

/*########################### Vertical Componet Position CSS Close Here #######################*/



/* ########################### Screen Size CSS Style Start Here ###############################*/
	/* Screen Size Wide CSS Start */
.scoop[theme-layout="vertical"][vertical-layout="wide"] .scoop-container {
	width:100%;
	margin: 0 auto;
}
	/* Screen Size Wide CSS Close */
	/* Screen Size BOX CSS Start */
.scoop[theme-layout="vertical"][scoop-device-type="desktop"][vertical-layout="box"] .scoop-container {
    max-width: 1200px;
    width: 100%;
	margin: 0 auto;
}
.scoop[theme-layout="vertical"][scoop-device-type="tablet"][vertical-layout="box"] .scoop-container {
    max-width: 900px;
    width: 100%;
	margin: 0 auto;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"][vertical-layout="box"] .scoop-container {
    max-width: 100%;
    width: 100%;
	margin: 0 auto;
}
	/* Screen Size BOX CSS Close */
	/* Screen Size WideBox CSS Start */
.scoop[theme-layout="vertical"][vertical-layout="widebox"] .scoop-container {
	width:100%;
	margin: 0 auto;
}
.scoop[theme-layout="vertical"][scoop-device-type="desktop"][vertical-layout="widebox"] .scoop-main-container {
    max-width: 1200px;
    width: 100%;
	margin: 0 auto;
}
.scoop[theme-layout="vertical"][scoop-device-type="desktop"][vertical-layout="widebox"] .scoop-wrapper{
    max-width: 1200px;
    width: 100%;
	margin: 0 auto;
}

.scoop[theme-layout="vertical"][scoop-device-type="tablet"][vertical-layout="widebox"] .scoop-main-container {
    max-width: 900px;
    width: 100%;
	margin: 0 auto;
}
.scoop[theme-layout="vertical"][scoop-device-type="tablet"][vertical-layout="widebox"] .scoop-wrapper{
    max-width: 900px;
    width: 100%;
	margin: 0 auto;
}

.scoop[theme-layout="vertical"][scoop-device-type="phone"][vertical-layout="widebox"] .scoop-main-container {
    max-width: 100%;
    width: 100%;
	margin: 0 auto;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"][vertical-layout="widebox"] .scoop-wrapper{
    max-width: 100%;
    width: 100%;
	margin: 0 auto;
}
.scoop[theme-layout="vertical"][vertical-layout="widebox"] .scoop-wrapper{
	overflow:hidden;
}
	/* Screen Size WideBox CSS Close */
/* ########################### Screen Size CSS Style Close Here ###############################*/

/* ############# Overlay on overlay effect CSS start here */
.scoop  .scoop-overlay-box {
  background: #000 none repeat scroll 0 0;
  bottom: 0;
  height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: -1;
  opacity:0;
  visibility: hidden;
  transition: opacity 0.3s linear;
  -webkit-transition: opacity 0.3s linear;
	-ms-transition: opacity 0.3s linear;
	-moz-transition:opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
  overflow:hidden;
}

.scoop[vertical-nav-type="compact"][vertical-effect="overlay"] .scoop-overlay-box,
.scoop[vertical-nav-type="expanded"][vertical-effect="overlay"] .scoop-overlay-box,
.scoop[vertical-nav-type="sub-expanded"][vertical-effect="overlay"] .scoop-overlay-box ,
.scoop[vertical-nav-type="ex-popover"][vertical-effect="overlay"] .scoop-overlay-box,
.scoop[vertical-nav-type="fullsub-collapsed"][vertical-effect="overlay"] .scoop-overlay-box 
 {
  visibility: visible;
  z-index: 1026;
  opacity:0.3;
  transition: opacity 0.3s linear;
  -webkit-transition: opacity 0.3s linear;
	-ms-transition: opacity 0.3s linear;
	-moz-transition:opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
  overflow:hidden;
}


/* ############# Overlay on overlay effect CSS Close here */  

 
 
/* ############## Subitem Icon Style Css Start Here ###################### */
.scoop .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-micon {
  display:none;
  font-size: 10px;
  padding-right: 5px;
}

.scoop .scoop-navbar .scoop-item .scoop-hasmenu  .scoop-submenu li > a .scoop-mtext:before  {
  font-style: normal;
  padding-right: 4px;
  font-size: 10px;
  position: relative;
  left: 0px;
  top:0px;
  opacity: 1;
  visibility: visible;
}
.scoop .scoop-navbar .scoop-item .scoop-hasmenu[subitem-icon="style1"] .scoop-submenu li > a .scoop-mtext:before  {
  content: "\f105";
  font-family: FontAwesome;
  padding-right: 5px;
  font-size: 12px;
}

.scoop .scoop-navbar .scoop-item .scoop-hasmenu[subitem-icon="style2"] .scoop-submenu li > a .scoop-mtext:before  {
  content: "\f22d";
  font-family: FontAwesome;
  padding-right: 5px;
  font-size: 12px;
}
.scoop .scoop-navbar .scoop-item .scoop-hasmenu[subitem-icon="style3"] .scoop-submenu li > a .scoop-mtext:before  {
  content: "\e08d";
  font-family: simple-line-icons;
}
.scoop .scoop-navbar .scoop-item .scoop-hasmenu[subitem-icon="style4"] .scoop-submenu li > a .scoop-mtext:before  {
  content: "\f124";
  font-family: FontAwesome;
  padding-right: 5px;
  font-size: 12px;
}
.scoop .scoop-navbar .scoop-item .scoop-hasmenu[subitem-icon="style5"] .scoop-submenu li > a .scoop-mtext:before  {
  content: "\f0da";
  font-family: FontAwesome;
  padding-right: 5px;
  font-size: 12px;
} 
/*.scoop .scoop-navbar .scoop-item .scoop-hasmenu[subitem-icon="style6"] .scoop-submenu li > a .scoop-mtext:before  {
  content: "\f375";
  font-family: Ionicons;
  padding-right: 5px;
  font-size: 12px;
} 
 */


 









/* ############## Subitem Icon Style Css Close Here ###################### */
 
/* ################### Active Item Cart Icon Css Start Here ############### */
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item > li.active > a > .scoop-mcaret {
  background: transparent none repeat scroll 0 0;
  border-bottom: 15px solid transparent;
  border-right: 10px solid #e9e9e9;
  border-top: 15px solid transparent;
  display: block;
  float: right;
  height: 0;
  position: absolute;
  right: 0;
  top: 5px;
  width: 0;
}
/* ################### Active Item Cart Icon Css Start Here ############### */  

 /* ############## Left Border on active and hover item Css Start Here #################### */
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  > li:hover > a:before{
	border-left-style:solid;
	border-left-width:5px;
    content: " ";
    height: 100%;
    left: 0;
    position: absolute;
	top:0px;
}  
 .scoop .scoop-navbar .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a:before {
	border-left-style:solid;
	border-left-width:5px;
    content: " ";
    height: 100%;
    left: 0;
    position: absolute;
	top:0px;
} 
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item > li.active > a:before {
	border-left-style:solid;
	border-left-width:5px;
    content: " ";
    height: 100%;
    left: 0;
    position: absolute;
	top: 0px;
} 
 /* ############## Left Border on active and hover item Css Close Here #################### */

/* ########################### Vertical Navigation Placement CSS Style Start Here ###############################*/
	/* Right Side Navigation Placement CSS Start */
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar {
  float: right;
  right: 0;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-content {
  float: unset;
  left: 0;
}
.scoop[theme-layout="vertical"][vertical-placement="left"] .scoop-navbar {
  float: left;
}
.scoop[theme-layout="vertical"][vertical-placement="left"] .scoop-content {
  float: unset;
  right: 0;
}

.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-header .scoop-left-header {
  float: right;
  right: 0;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-header .scoop-right-header {
  float: unset;
  left: unset;
}
	/* Right Side Navigation Placement CSS Close */
	/* Left Side Navigation Placement CSS Start */
.scoop[theme-layout="vertical"][vertical-placement="left"] .scoop-header .scoop-left-header {
  float: left;
}
.scoop[theme-layout="vertical"][vertical-placement="left"] .scoop-header .scoop-right-header {
  float: unset;
  right: unset;
}
	/* Left Side Navigation Placement CSS Close */
/* ########################### Vertical Navigation Placement CSS Style Close Here ###############################*/

/* ################## Style Section According Navigation Type CSS Style Start Here ###############################*/
	/* ####### Expanded Navigation With Effect CSS Start Here ##########*/

.scoop[theme-layout="vertical"][vertical-nav-type="expanded"]  .scoop-header .scoop-left-header {
	width: 235px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="expanded"] .scoop-navbar  {
	width: 235px;
}

		/* Push Effect CSS Start Here */
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="expanded"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="expanded"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:235px;
	margin-right:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="expanded"][vertical-effect="push"] .scoop-content {
	margin-left:235px;
	margin-right:-235px;
} 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:0px;
	 margin-left:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][vertical-effect="push"] .scoop-content {
	 margin-right:235px;
	 margin-left:-235px;
} 
		/* Push Effect CSS Close Here */
		/* Shrink Effect CSS Start Here */
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="expanded"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="expanded"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="expanded"][vertical-effect="shrink"] .scoop-content {
	margin-left:235px;
} 

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][vertical-effect="shrink"] .scoop-content {
	 margin-right:235px;
} 
	/* Shrink Effect CSS Close Here */	
	/* Overlay Effect CSS Start Here */
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="expanded"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="expanded"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="expanded"][vertical-effect="overlay"] .scoop-content {
	margin-left:0px;
} 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][vertical-effect="overlay"] .scoop-content {
	 margin-right:0px;
}
.scoop[theme-layout="vertical"][vertical-effect="overlay"] .scoop-navbar .scoop-item > li.active > a > .scoop-mcaret {
    background: transparent !important;
    border-bottom: transparent !important;
    border-right: transparent !important;
    border-top: 15px solid transparent;
}

	/* Overlay Effect CSS Close Here */	
.scoop[theme-layout="vertical"][vertical-nav-type="expanded"][scoop-device-type="desktop"] .scoop-navbar.is-hover .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu,
.scoop[theme-layout="vertical"][vertical-nav-type="expanded"][scoop-device-type="tablet"] .scoop-navbar.is-hover .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
    left: 100%;
    position: absolute;
    width: 230px;
    z-index: 1024;
	top:0;
}
.scoop[theme-layout="vertical"][vertical-nav-type="expanded"][scoop-device-type="desktop"] .scoop-navbar .scoop-item > .scoop-hasmenu.is-hover.scoop-trigger .scoop-submenu,
.scoop[theme-layout="vertical"][vertical-nav-type="expanded"][scoop-device-type="tablet"] .scoop-navbar .scoop-item > .scoop-hasmenu.is-hover.scoop-trigger .scoop-submenu {
    left: 100%;
    position: absolute;
    width: 230px;
    z-index: 1024;
	top:0;
}
/* ####### Expanded Navigation With Effect CSS Close Here ##########*/


/* ####### collapsed Navigation With Effect CSS Start Here ##########*/
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"]  .scoop-header .scoop-left-header {
	width: 235px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar  {
	width: 45px;
}
	/* Push Effect CSS Start Here */
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="push"]  .scoop-header.iscollapsed .scoop-left-header {
	width: 45px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:45px;
	margin-right:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="push"] .scoop-content {
	margin-left:45px;
	margin-right:0px;
} 	
 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="push"]  .scoop-header.iscollapsed .scoop-left-header {
	 width:45px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:45px;
	 margin-left:0px;
}
 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="push"] .scoop-content {
	 margin-right:45px;
	 margin-left:0px;
} 
	/* Push Effect CSS Close Here */
	/* Shrink Effect CSS Start Here */
 
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="shrink"]  .scoop-header.iscollapsed .scoop-left-header {
	width: 45px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:45px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="shrink"] .scoop-content {
	margin-left:45px;
} 

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="shrink"]  .scoop-header.iscollapsed .scoop-left-header {
	 width:45px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:45px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="shrink"] .scoop-content {
	 margin-right:45px;
} 
	/* Shrink Effect CSS Close Here */
	/* overlay Effect CSS Start Here */
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="overlay"]  .scoop-header.iscollapsed .scoop-left-header {
	width: 45px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:45px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="collapsed"][vertical-effect="overlay"] .scoop-content {
	margin-left:45px;
} 

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="overlay"]  .scoop-header.iscollapsed .scoop-left-header {
	 width:45px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:45px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"][vertical-effect="overlay"] .scoop-content {
	 margin-right:45px;
} 

	/* overlay Effect CSS Close Here */



.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  > li  > a .scoop-mtext{
	opacity: 1;
	visibility: visible;
	position:absolute;
}
.scoop[theme-layout="vertical"] .scoop-navbar .scoop-item  li.scoop-hasmenu  > a:after {
	opacity: 1;
	visibility: visible;
} 
 

.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item  > li  > a .scoop-mtext{
	opacity: 0;
	visibility: hidden;
	position:absolute; 
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item > li.scoop-trigger > a .scoop-mtext {
	opacity: 1;
	visibility: visible;
	position:absolute;
	padding-left: 22px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item  li.scoop-hasmenu  > a:after {
	opacity: 0;
	visibility: hidden;
} 
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item  li.scoop-hasmenu.scoop-trigger  a:after {
	opacity: 1;
	visibility: visible;
} 
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item > li.scoop-trigger  {
  display: block;
  position: relative;
  width: 255px;
  z-index: 1024;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item  > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  left: 45px;
  position: absolute;
  width: 210px;
  z-index: 1024; 
  border-left-style: solid;
  border-left-width: 1px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item  > .scoop-hasmenu.scoop-trigger > .scoop-submenu .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  position: relative;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item  > .scoop-hasmenu > .scoop-submenu .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  position: absolute;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item > li.scoop-trigger.active > a > .scoop-mcaret {
    display: none;
}
 
 	
	/* ####### collapsed Navigation With Effect CSS Close Here ##########*/

	/* ####### Offcanvas Navigation With Effect CSS Start Here ##########*/
		/* push Effect CSS start Here */
 .scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="push"]  .scoop-header .scoop-left-header {
	width: 235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="push"]  .scoop-header.iscollapsed .scoop-left-header {
	width: 0px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:0px;
	 margin-right:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="push"] .scoop-navbar  {
	width: 235px;
	margin-left:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="push"] .scoop-content {
	margin-left:0px;
	margin-right:0px;
} 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="push"]  .scoop-header .scoop-left-header {
	 width:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	 margin-right:0px;
}

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="push"]  .scoop-header.iscollapsed .scoop-left-header {
	 width:0;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:0px;
	 margin-left:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="push"] .scoop-navbar  {
	 width: 235px;
	 margin-right:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="push"] .scoop-content {
	 margin-right:0px;
	 margin-left:0px;
} 
		/* push Effect CSS Close Here */
		/* shrink Effect CSS Start Here */

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="shrink"]  .scoop-header .scoop-left-header {
	width: 235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="shrink"]  .scoop-header.iscollapsed .scoop-left-header {
	width: 0;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="shrink"] .scoop-navbar  {
	width: 235px;
	margin-left:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="shrink"] .scoop-content {
	margin-left:0px;
} 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="shrink"]  .scoop-header .scoop-left-header {
	 width:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="shrink"]  .scoop-header.iscollapsed .scoop-left-header {
	 width:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="shrink"] .scoop-navbar  {
	 width: 235px;
	 margin-right: -235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="shrink"] .scoop-content {
	 margin-right:0px;
} 
		/* shrink Effect CSS Close Here */
		/* overlay Effect CSS Start Here */

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="overlay"]  .scoop-header .scoop-left-header {
	width: 235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="overlay"]  .scoop-header.iscollapsed .scoop-left-header {
	width: 0px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="overlay"] .scoop-navbar  {
	width: 235px;
	margin-left:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="offcanvas"][vertical-effect="overlay"] .scoop-content {
	margin-left:0px;
} 


.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="overlay"]  .scoop-header .scoop-left-header {
	 width:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="overlay"]  .scoop-header.iscollapsed .scoop-left-header {
	 width:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="overlay"] .scoop-navbar  {
	 width: 235px;
	 margin-right:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="offcanvas"][vertical-effect="overlay"] .scoop-content {
	 margin-right:0px;
} 
		/* overlay Effect CSS Close Here */
	/* ####### Offcanvas Navigation With Effect CSS Close Here ##########*/
	/* ####### Compact Navigation With Effect CSS Start Here ##########*/
		/* Push Effect CSS Start Here */
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="push"]  .scoop-header.iscollapsed .scoop-left-header {
	width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:190px;
	margin-right:-190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="push"]  .scoop-header .scoop-left-header {
	width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	margin-left:190px;
}


.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="push"] .scoop-navbar  {
	width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="push"] .scoop-content {
	margin-left:190px;
	margin-right:-190px;
} 


.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="push"]  .scoop-header.iscollapsed .scoop-left-header {
	 width:190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:190px;
	 margin-left:-190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="push"]  .scoop-header .scoop-left-header {
	 width:190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	 margin-right:190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="push"] .scoop-navbar  {
	 width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="push"] .scoop-content {
	 margin-right:190px;
	 margin-left:-190px;
} 
		/* Push Effect CSS Close Here */
		/* Shrink Effect CSS Start Here */
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="shrink"]  .scoop-header.iscollapsed .scoop-left-header {
	width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="shrink"]  .scoop-header .scoop-left-header {
	width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	margin-left:190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="shrink"] .scoop-navbar  {
	width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="shrink"] .scoop-content {
	margin-left:190px;
} 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="shrink"]  .scoop-header.iscollapsed .scoop-left-header {
	 width:190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="shrink"]  .scoop-header .scoop-left-header {
	 width:190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	 margin-right:190px;
}

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="shrink"] .scoop-navbar  {
	 width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="shrink"] .scoop-content {
	 margin-right:190px;
} 
	/* Shrink Effect CSS Close Here */
	/* Overlay Effect CSS Start Here */

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="overlay"]  .scoop-header.iscollapsed .scoop-left-header {
	width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="overlay"]  .scoop-header .scoop-left-header {
	width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	margin-left:190px;
}

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="overlay"] .scoop-navbar  {
	width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="compact"][vertical-effect="overlay"] .scoop-content {
	margin-left:0px;
} 

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="overlay"]  .scoop-header .scoop-left-header {
	 width:190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	 margin-right:190px;
}

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="overlay"]  .scoop-header.iscollapsed .scoop-left-header {
	 width:190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="overlay"] .scoop-navbar  {
	 width: 190px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="compact"][vertical-effect="overlay"] .scoop-content {
	 margin-right:0px;
} 
	/* Overlay Effect CSS Close Here */

.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-navbar .scoop-item > li > a {
    line-height: unset;
    text-align: center;
}
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-navbar .scoop-item > li > a .scoop-mtext {
    opacity: 1;
    position: relative;
    text-align: center;
    visibility: visible;
}
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-navbar .scoop-item > li > a > .scoop-micon {
    display: block;
    font-size: 18px;
    padding: 5px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-navbar .scoop-item > li.active > a > .scoop-mcaret {
    background: transparent none repeat scroll 0 0;
    border-bottom: 18px solid transparent;
    border-right: 12px solid #e9e9e9;
    border-top: 18px solid transparent;
    top: 18px;
}  
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-navbar .scoop-item > li.active > a:before {
    height: 100%;
}
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-navbar .scoop-item > li:hover > a:before {
    height: 100%;
}
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-navbar .scoop-item  > li.scoop-hasmenu[dropdown-icon="style1"] > a:after {
    top: 18px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-navbar .scoop-item > li.scoop-hasmenu.scoop-trigger[dropdown-icon="style1"] > a:after {
    top: 18px;
}
 

/* .scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-navbar .scoop-item  > li.scoop-hasmenu > a:after {
  content: "\e604";
  float: right;
  font-family: simple-line-icons;
  font-style: normal;
  margin-left: 3px;
  font-size: 10px;
  position: absolute;
  right: 16px;
  top:20px
}
.scoop[theme-layout="vertical"][vertical-nav-type="compact"] .scoop-navbar .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  content: "\e607";
  float: right;
  font-family: simple-line-icons;
  font-style: normal;
  margin-left: 3px;
  position: absolute;
  right: 16px;
  font-size: 10px;
  top:20px
} */

.scoop[theme-layout="vertical"][vertical-nav-type="compact"][scoop-device-type="desktop"] .scoop-navbar.is-hover .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu,
.scoop[theme-layout="vertical"][vertical-nav-type="compact"][scoop-device-type="tablet"] .scoop-navbar.is-hover .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
    left: 100%;
    position: absolute;
    width: 210px;
    z-index: 1024;
	top:0;
}
.scoop[theme-layout="vertical"][vertical-nav-type="compact"][scoop-device-type="desktop"] .scoop-navbar .scoop-item > .scoop-hasmenu.is-hover.scoop-trigger .scoop-submenu ,
.scoop[theme-layout="vertical"][vertical-nav-type="compact"][scoop-device-type="tablet"] .scoop-navbar .scoop-item > .scoop-hasmenu.is-hover.scoop-trigger .scoop-submenu{
    left: 100%;
    position: absolute;
    width: 210px;
    z-index: 1024;
	top:0;
}
/* ####### Compact Navigation With Effect CSS Close Here ##########*/




/* ####### sub-expanded Navigation With Effect CSS Start Here ##########*/

.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"]  .scoop-header .scoop-left-header {
	width: 235px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar  {
	width: 235px;
}
	/* Push Effect CSS Start Here */
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="sub-expanded"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="sub-expanded"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:235px;
	margin-right:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="sub-expanded"][vertical-effect="push"] .scoop-content {
	margin-left:235px;
	margin-right:-235px;
} 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="sub-expanded"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="sub-expanded"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:0px;
	 margin-left:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="sub-expanded"][vertical-effect="push"] .scoop-content {
	 margin-right:235px;
	 margin-left:-235px;
} 
	/* Push Effect CSS Close Here */

	/* Shrink Effect CSS Start Here */

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="sub-expanded"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="sub-expanded"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="sub-expanded"][vertical-effect="shrink"] .scoop-content {
	margin-left:465px;
} 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="sub-expanded"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="sub-expanded"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="sub-expanded"][vertical-effect="shrink"] .scoop-navbar  {
	 width: 235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="sub-expanded"][vertical-effect="shrink"] .scoop-content {
	 margin-right:235px;
} 

	/* Shrink Effect CSS Close Here */

	/* overlay Effect CSS Start Here */

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="sub-expanded"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="sub-expanded"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="sub-expanded"][vertical-effect="overlay"] .scoop-navbar  {
	width: 235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="sub-expanded"][vertical-effect="overlay"] .scoop-content {
	margin-left:0px;
} 

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="sub-expanded"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="sub-expanded"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="sub-expanded"][vertical-effect="overlay"] .scoop-content {
	 margin-right:0px;
} 
	/* overlay Effect CSS Close Here */
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="desktop"] .scoop-navbar .scoop-item > li,
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="tablet"] .scoop-navbar .scoop-item > li {
   position: unset;
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="desktop"] .scoop-navbar .scoop-item,
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="tablet"] .scoop-navbar .scoop-item  {
  position: unset;
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="desktop"] .scoop-inner-navbar,
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="tablet"] .scoop-inner-navbar {
  position: unset;
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="desktop"] .scoop-navbar .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu,
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="tablet"] .scoop-navbar .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
    left: 100%;
    position: absolute;
    width: 230px;
    z-index: 1024;
	top:0;
	bottom: 0;
}

.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="desktop"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li,
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="tablet"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li {
    position: unset;
}

.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="desktop"] .scoop-navbar .scoop-item > .scoop-hasmenu.scoop-trigger .scoop-submenu,
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="tablet"] .scoop-navbar .scoop-item > .scoop-hasmenu.scoop-trigger .scoop-submenu {
    left: 100%;
    position: absolute;
    width: 230px;
    z-index: 1024;
	top:0;
	bottom: 0;
} 
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar .scoop-item  > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-style: solid;
  border-left-width: 1px;
}
/* ####### sub-expanded Navigation With Effect CSS Close Here ##########*/

/* ####### ex-popover Navigation With Effect CSS Start Here ##########*/
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-header .scoop-left-header {
	width: 235px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-header.iscollapsed .scoop-left-header {
	width: 235px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar  {
	width: 235px;
}
	/* push Effect CSS Start Here */
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="ex-popover"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="ex-popover"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:235px;
	margin-right:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="ex-popover"][vertical-effect="push"] .scoop-content {
	margin-left:235px;
	margin-right:-235px;
} 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="ex-popover"][vertical-effect="push"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="ex-popover"][vertical-effect="push"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:0px;
	 margin-left:-235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="ex-popover"][vertical-effect="push"] .scoop-content {
	 margin-right:235px;
	 margin-left:-235px;
} 

	/* push Effect CSS Close Here */
	/* shrink Effect CSS Start Here */


.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="ex-popover"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="ex-popover"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="ex-popover"][vertical-effect="shrink"] .scoop-content {
	margin-left:235px;
} 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="ex-popover"][vertical-effect="shrink"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="ex-popover"][vertical-effect="shrink"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="ex-popover"][vertical-effect="shrink"] .scoop-content {
	 margin-right:235px;
} 
	/* shrink Effect CSS Close Here */
	/* overlay Effect CSS Start Here */
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="ex-popover"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	margin-left:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="ex-popover"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	margin-left:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="ex-popover"][vertical-effect="overlay"] .scoop-content {
	margin-left:0px;
} 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="ex-popover"][vertical-effect="overlay"]  .scoop-header .scoop-right-header {
	 margin-right:235px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="ex-popover"][vertical-effect="overlay"]  .scoop-header.iscollapsed  .scoop-right-header {
	 margin-right:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="ex-popover"][vertical-effect="overlay"] .scoop-content {
	 margin-right:0px;
} 
	/* overlay Effect CSS Close Here */
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"][scoop-device-type="desktop"] .scoop-navbar .scoop-item > li,
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"][scoop-device-type="tablet"] .scoop-navbar .scoop-item > li {
   position: relative;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"][scoop-device-type="desktop"] .scoop-navbar .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu,
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"][scoop-device-type="tablet"] .scoop-navbar .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
    left: 100%;
    position: absolute;
    width: 230px;
    z-index: 1024;
	top:0;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"][scoop-device-type="desktop"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li,
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"][scoop-device-type="tablet"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li {
    position: relative;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"][scoop-device-type="desktop"] .scoop-navbar .scoop-item > .scoop-hasmenu.scoop-trigger .scoop-submenu,
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"][scoop-device-type="tablet"] .scoop-navbar .scoop-item > .scoop-hasmenu.scoop-trigger .scoop-submenu {
    left: 100%;
    position: absolute;
    width: 230px;
    z-index: 1024;
	top:0;
} 
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"][scoop-device-type="desktop"] .scoop-navbar .scoop-item  li.scoop-trigger > a > .scoop-mcaret, 
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"][scoop-device-type="tablet"] .scoop-navbar .scoop-item  li.scoop-trigger > a > .scoop-mcaret{
    background: transparent none repeat scroll 0 0;
    border-bottom: 19px solid transparent;
    border-right: 12px solid;
    border-top: 19px solid transparent;
    display: block;
    float: right;
    height: 0;
    position: absolute;
    right: 0px;
    top: 0px;
    width: 0;
    z-index: 1025;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-style: solid;
  border-left-width: 1px;
}
/* ####### ex-popover Navigation With Effect CSS Close Here ##########*/


/* ####### Fullpage Navigation With Effect CSS Start Here ##########*/
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="fullpage"]  .scoop-header .scoop-left-header {
	width: 235px;
}
.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="fullpage"]  .scoop-header .scoop-right-header {
	margin-left:235px;
} 

.scoop[theme-layout="vertical"][vertical-placement="left"][vertical-nav-type="fullpage"] .scoop-header.iscollapsed .scoop-left-header {
	width: 235px;
}

.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar  {
	width: 100%;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-content {
	margin-left:0px;
	margin-right:0px;
} 


.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="fullpage"]  .scoop-header .scoop-left-header {
	 width:235px;
}
 .scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="fullpage"] .scoop-header .scoop-right-header {
	 margin-right:235px;
} 

.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="fullpage"]  .scoop-header.iscollapsed .scoop-left-header {
	 width:235px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li.active > a {
  background: transparent !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item[item-border="true"] > li > a {
  border-bottom-width: 0 !important;
}

 
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar{
	top:0; 
	bottom:0;
	left:0;
	right:0;
	z-index:1030;
	opacity:1;
	visibility:visible;
	width:100%;
	/* transition: all 0.3s linear; 
	-webkit-transition: all 0.3s linear; 
	-ms-transition: all 0.3s linear; 
	-moz-transition:all 0.3s linear; 
	-o-transition: all 0.3s linear;  */
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[scoop-navbar-position="absolute"] {
    position: absolute;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[scoop-navbar-position="fixed"] {
    position: fixed;
}
 
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-inner-navbar {
  margin-top: 40px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-main-container {
    display: block;
    position: unset;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li > a .scoop-mtext {
    opacity: 1;
    position: relative;
    visibility: visible;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li > a {
  height: 45px;
  line-height: 45px;
  text-align: center;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li > a {
    font-size: 24px;
    font-weight: 500;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li > a > .scoop-micon {
  display: none;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-navigatio-lavel {
    display: none !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li.active > a:before {
   display:none;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li  a  .scoop-badge {
  display: none;
}

.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li:hover > a {
     background: transparent !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li:hover > a:before {
    display:none;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li.active > a > .scoop-mcaret {
    display: none;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li > a {
    padding: 0;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .sidebar_toggle {
  height: 30px;
}
.scoop-navbar .sidebar_toggle a{
	opacity: 1;
	visibility: visible;
	transform: rotateX(90deg);
	 -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
	position: absolute;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .sidebar_toggle a {
  color: #f1f1f1;
  float: right;
  font-size: 36px;
  opacity: 1;
  position: relative;
  right: 10px;
  text-decoration: none;
  top: 0;
  transition: opacity 0.8s linear 0s;
  -webkit-transition: opacity 0.8s linear 0s;
	-ms-transition: opacity 0.8s linear 0s;
	-moz-transition:opacity 0.8s linear 0s;
	-o-transition: opacity 0.8s linear 0s;
  visibility: visible;
  transform: rotateX(0deg);
   -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg); 
  -moz-transform: rotateX(0deg); 
  -o-transform: rotateX(0deg);
}
.scoop-navbar .sidebar_toggle a:hover {
  color: #fff;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li.scoop-hasmenu > a:after {
  display:none;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item > li.scoop-trigger > a {
  background: transparent !important;
  border-bottom-color: transparent !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"]  .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu {
  background: transparent !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item[subitem-border="true"] .scoop-hasmenu .scoop-submenu li > a {
  border-bottom-width: 0px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li > a {
  text-align: center;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"]  .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before {
	display:none;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"]  .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li > a {
  font-size: 24px;
  font-weight: 500;
}
 
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a {
    background: transparent !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a:before {
    border-left-width: 0px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar .scoop-item .scoop-submenu > li.active > a {
  background: transparent !important;
  color: #FFF !important;
}
/* ####### Fullpage Navigation With Effect CSS Close Here ##########*/


/* ################## Style Section According Navigation Type CSS Style Close Here ###############################*/
.scoop[theme-layout="vertical"][scoop-device-type="phone"]  .scoop-header.nocollapsed .scoop-left-header {
	width: 100% !important;
	padding-left: 235px;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"][vertical-nav-type="collapsed"]  .scoop-header.nocollapsed .scoop-left-header {
	padding-left: 45px;
} 
.scoop[theme-layout="vertical"][scoop-device-type="phone"][vertical-nav-type="offcanvas"]  .scoop-header.nocollapsed .scoop-left-header {
	padding-left: 0px;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"][vertical-nav-type="fullpage"]  .scoop-header.nocollapsed .scoop-left-header {
	padding-left: 0px;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"][vertical-effect="overlay"]  .scoop-header.nocollapsed .scoop-left-header {
	padding-left: 0px;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"]  .scoop-header.nocollapsed {
  height: 100px !important;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-right-header {
  padding: 50px 5px 0 !important;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"][vertical-nav-type="offcanvas"] .scoop-header.nocollapsed .scoop-right-header {
  margin: 0 !important;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"][vertical-effect="overlay"] .scoop-header.nocollapsed .scoop-right-header {
  margin: 0 !important;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"]  .scoop-header.nocollapsed  .sidebar_toggle a {
  background: transparent none repeat scroll 0 0;
  border-radius: 4px;
  color: #fff;
  float: left;
  font-size: 18px;
  height: 35px;
  margin-right: 5px;
  position: fixed;
  right: 0;
  text-align: center;
  top: 6px;
  width: 40px;
  z-index: 1028;
}

.scoop.nocollapsed[theme-layout="vertical"][scoop-device-type="phone"] .scoop-navbar {
  top: 0;
  z-index:1030;
} 
.scoop.nocollapsed[theme-layout="vertical"][scoop-device-type="phone"] .scoop-main-container {
  position: unset;
}
.scoop.nocollapsed[theme-layout="vertical"][scoop-device-type="phone"] #styleSelector{
	top:100px;
}




.scoop .scoop-navbar .scoop-navigatio-lavel {
  font-size: 14px;
  font-weight: 600;
  opacity: 1;
  padding: 15px 10px 2px;
  text-transform: uppercase;
  visibility: visible;
  width: 100%;
  transform: rotateX(0deg);
   -webkit-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg); 
  -moz-transform: rotateX(0deg); 
  -o-transform: rotateX(0deg);
  transition: opacity 0.3s linear;
   -webkit-transition: opacity 0.3s linear;
	-ms-transition: opacity 0.3s linear;
	-moz-transition:opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
  border-bottom: 2px solid;
  margin-bottom: 5px;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar .scoop-navigatio-lavel{
	opacity: 0;
	visibility: hidden;
	/* position:absolute; */
	transform: rotateX(90deg);
	 -webkit-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg); 
  -moz-transform: rotateX(90deg); 
  -o-transform: rotateX(90deg);
	transition: opacity 0.3s linear;
	-webkit-transition: opacity 0.3s linear;
	-ms-transition: opacity 0.3s linear;
	-moz-transition:opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
}
.scoop[vertical-nav-type="compact"] .scoop-navbar .scoop-navigatio-lavel{
	text-align: center;
}

.scoop .scoop-navbar .scoop-item li a .scoop-badge {
  background-color: #777;
  border-radius: 10px;
  color: #fff;
  font-size: 10px;
  font-weight: 700;
  line-height: 1;
  min-width: 10px;
  padding: 3px 7px;
  position: absolute;
  right: 30px;
  text-align: center;
  top: 12px;
  vertical-align: middle;
  white-space: nowrap;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s linear;
  -webkit-transition: opacity 0.3s linear;
	-ms-transition: opacity 0.3s linear;
	-moz-transition:opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar .scoop-item > li > a .scoop-badge {
	opacity: 0;
	visibility: hidden;
	transition: opacity 0.3s linear;
	-webkit-transition: opacity 0.3s linear;
	-ms-transition: opacity 0.3s linear;
	-moz-transition:opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar .scoop-item > li.scoop-trigger > a .scoop-badge {
 opacity: 1;
  visibility: visible;
  transition: opacity 0.3s linear;
  -webkit-transition: opacity 0.3s linear;
	-ms-transition: opacity 0.3s linear;
	-moz-transition:opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
}

.scoop .scoop-navbar .scoop-item li a .scoop-badge.badge-success{
	  color: #fff;
	  background-color: #70ca63;
} 
.scoop .scoop-navbar .scoop-item li a .scoop-badge.badge-danger{
	  color: #fff;
	  background-color: #f13b48;
} 

.scoop .scoop-navbar .scoop-item li a .scoop-badge.badge-warning{
	  color: #000;
	  background-color: #fecd33;
}   
/* ################### Componet Adjustment According RTL CSS Start hear ################## */
 
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item > li.active > a:before {
    left: auto;
    right: 0;
}

.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item > li.active > a > .scoop-mcaret {
  background: transparent none repeat scroll 0 0;
  border-bottom: 15px solid transparent;
  border-left: 10px solid #e9e9e9;
  border-top: 15px solid transparent;
  left: 0;
  right: auto;
  top: 4px;
  border-right:0px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item > li > a .scoop-micon i {
    float: right;
    padding-right: 17px;
    position: absolute;
    right: 0;
    top: 11px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item > li.scoop-hasmenu  > a:after {
  left: 8px;
  right: auto;
  top: -1px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item > li > a .scoop-mtext {
    left: auto;
    right: 0;
	padding-right:45px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item li a .scoop-badge {
    position: relative;
    right: -1px;
    top: -2px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item > li:hover > a:before {
    left: auto;
    right: 0;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a:before {
     left: auto;
    right: 0;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before {
    float: right;
    left: 6px;
    top: 3px;
	padding-right: 10px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li > a {
    text-align: right;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item li .scoop-submenu li > a .scoop-badge {
    left: 23px;
    position: absolute;
    right: auto;
    top: 11px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-navigatio-lavel {
  text-align: right;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item  li.scoop-hasmenu[dropdown-icon="style1"] > a:after {
  content: "\e605";
  font-family: simple-line-icons;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item  li.scoop-hasmenu.scoop-trigger[dropdown-icon="style1"] > a:after {
  content: "\e604";
  font-family: simple-line-icons;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][scoop-device-type="desktop"] .scoop-navbar.is-hover .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu,
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][scoop-device-type="tablet"] .scoop-navbar.is-hover .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
    left: auto;
	right:100%;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][scoop-device-type="desktop"] .scoop-navbar .scoop-item > .scoop-hasmenu.is-hover.scoop-trigger .scoop-submenu, 
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="expanded"][scoop-device-type="tablet"] .scoop-navbar .scoop-item > .scoop-hasmenu.is-hover.scoop-trigger .scoop-submenu {
    left: auto;
    right: 100%;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu > a:after {
    left: 7px;
    right: auto;
    top: 0;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item > li.scoop-trigger {
    left: auto;
    right: 210px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
    left: auto;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"] .scoop-navbar .scoop-item li a .scoop-badge {
    position: absolute;
    right: auto;
    top: 10px;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-header .scoop-right-header .scoop-rl-header {
    float: right;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-header .scoop-right-header .scoop-rr-header {
    float: left;
}
.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-header .sidebar_toggle {
    float: right;
}
.scoop[theme-layout="vertical"][vertical-placement="right"][vertical-nav-type="collapsed"] .scoop-search.open .scoop-search-box {
    right: 0;
}
/* ###################################################################################################*/



/* Theme Patteren CSS Start */
body[themebg-pattern="pattern1"]{
	background-image: url("../images/pattern1.png");
}
body[themebg-pattern="pattern2"]{
	background-image: url("../images/pattern2.png");
}
body[themebg-pattern="pattern3"]{
	background-image: url("../images/pattern3.png");
}
body[themebg-pattern="pattern4"]{
	background-image: url("../images/pattern4.png");
}
body[themebg-pattern="pattern5"]{
	background-image: url("../images/pattern5.png");
}
body[themebg-pattern="pattern6"]{
	background-image: url("../images/pattern6.png");
}
body[themebg-pattern="pattern7"]{
	background-image: url("../images/pattern7.png");
}
body[themebg-pattern="pattern8"]{
	background-image: url("../images/pattern8.png");
}
body[themebg-pattern="pattern9"]{
	background-image: url("../images/pattern9.png");
}
/* Theme Patteren CSS Close */

/* ########### Theme1 Color Combation CSS Style Start Here ##################### */
.scoop .scoop-header[header-theme="theme1"] {
	background:#364760;
	color:#f0f4f6;
}  
.scoop .scoop-header[header-theme="theme1"] .sidebar_toggle a {
  background: transparent ;
  color: #f0f4f6;
  border-color: #f0f4f6;
}
.scoop .scoop-header[header-theme="theme1"] .scoop-right-header .scoop-rl-header ul > li > a, 
.scoop .scoop-header[header-theme="theme1"] .scoop-right-header .scoop-rr-header ul > li > a {
  color: #f0f4f6;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme1"]{
  background: transparent ;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme1"]{
  background: #2a394f ;
}
.scoop  .scoop-header .scoop-left-header[lheader-theme="theme1"] .scoop-logo a {
	color:#f0f4f6;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme1"] .scoop-logo .logo-icon {
  color: #364760;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme1"] .scoop-logo .logo-icon {
  color: #2a394f;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme1"] .scoop-logo a {
    color: inherit;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme1"]{
  background: #2a394f;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme1"] .scoop-logo .logo-icon {
  color: #364760;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme1"] .scoop-logo a {
    color: #f0f4f6;
}

.scoop .scoop-navbar[navbar-theme="theme1"]{
	background: #364760 ;
} 
.scoop .scoop-navbar[navbar-theme="theme1"] .profile-box {
  border-bottom-color: #3f4f66;
  border-top:1px solid #3f4f66;
  background:#364760 ;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .profile-box .media-body {
	color:#f0f4f6 ;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .profile-box .media-body .user-status b {
    color:#70ca63;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  > li > a {
  color: #d5dae6;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  >  li.active > a {
	background: #2a394f ;
	color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item > li.active > a:before {
  border-left-color:#15a4fa;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item > li.active > a {
  border-bottom-color: #304057;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  > li > a {
	border-bottom-color: #3f4f66;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  li.scoop-hasmenu .scoop-submenu{
  background: #2a3b54 ;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  li.scoop-hasmenu .scoop-submenu li > a {
	border-bottom-color: #304057;
	color:#d5dae6;
} 
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item > li.scoop-hasmenu > a:after {
  color: #d5dae6;
} 
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu > a:after {
  color: #d5dae6;
} 
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  color: #d5dae6;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu.scoop-trigger > a:after {
  color: #d5dae6;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  > li:hover > a{
	background: #2a394f;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  > li.active:hover > a{
	background: #2a394f;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a{
	background: #233146;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme1"]  .scoop-item  > li:hover > a:before{
	 border-left-color:#15a4fa;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item > li.scoop-hasmenu:hover > a:after {
  color: #15a4fa;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item > li.scoop-trigger > a {
    background: #2a394f  ;
	border-bottom-color: #3f4f66;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a:before {
   border-left-color:#15a4fa;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item > li.scoop-trigger.active > a {
    background: #2a394f;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu:hover > a:after {
  color: #15a4fa;
} 
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item  .scoop-hasmenu .scoop-submenu li.active > a {
	color:#FFF;
	background: #233146;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item > li.scoop-hasmenu.scoop-trigger.active > a:after {
  color: #15a4fa;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before  {
	border-bottom-color: #b4bcc8;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:before  {
	border-bottom-color: #15a4fa;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:after {
	border-left-color: #b4bcc8;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:after {
	border-left-color: #15a4fa;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-navigatio-lavel {
  color: #919191;
  border-bottom-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme1"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
	border-left-color: transparent; 
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar[navbar-theme="theme1"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme1"] .scoop-item li.scoop-trigger > a > .scoop-mcaret{
	border-right-color: #364760 ! important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme1"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent ;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme1"] {
	background-color: rgba(0,0,0, 0.9);
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme1"] .scoop-item > li:hover > a {
    color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme1"] .sidebar_toggle a {
  color: #f1f1f1;
}

.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-search {
    border-top-color: #3f4f66;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-search  input[type="text"] {
    border-bottom-color: #3f4f66;
	color:#d5dae6;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-search input[type="text"]:focus, 
.scoop .scoop-navbar[navbar-theme="theme1"] .style-4 input[type="text"].focus {
    border-bottom-color: #5c6c83;
	color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-search .search-icon i {
    color: #d5dae6;
}
.scoop .scoop-navbar[navbar-theme="theme1"] .scoop-search .searchbar-toggle:before {
	color: #d5dae6;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme1"] .scoop-search.open .scoop-search-box {
	background: #2a394f;
}
/* ########### Theme1 Color Combation CSS Style Close Here ##################### */

/* ########### Theme2 Color Combation CSS Style Start Here ##################### */

.scoop .scoop-header[header-theme="theme2"] {
	background:#FFF;
	color:#678098;
}
.scoop .scoop-header[header-theme="theme2"] .sidebar_toggle a {
  background: transparent ;
  color: #678098;
  border-color: #678098;
}
.scoop .scoop-header[header-theme="theme2"] .scoop-right-header .scoop-rl-header ul > li > a, 
.scoop .scoop-header[header-theme="theme2"] .scoop-right-header .scoop-rr-header ul > li > a {
  color: #678098;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme2"]{
  background: transparent ;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme2"]{
  background: #FFF;
}
.scoop  .scoop-header .scoop-left-header[lheader-theme="theme2"] .scoop-logo a {
	color:#678098;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme2"] .scoop-logo .logo-icon {
  color: #f0f0f0;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme2"] .scoop-logo .logo-icon {
  color: #f0f0f0;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme2"] .scoop-logo a {
    color: inherit;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme2"]{
  background: #FFF;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme2"] .scoop-logo .logo-icon {
  color: #f0f0f0;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme2"] .scoop-logo a {
    color: #678098;
}

.scoop .scoop-navbar[navbar-theme="theme2"]{
	background: #ffffff ;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .profile-box {
  border-bottom-color: #f5f5f5;
  border-top:1px solid #f5f5f5;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .profile-box .media-body {
	color:#678098 ;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .profile-box .media-body .user-status b {
    color:#70ca63;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  > li > a {
  color: #678098;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  >  li.active > a {
	background: #f2f6f9 ;
	color: #5c9acf;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item > li.active > a:before {
  border-left-color:#5c9acf;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item > li.active > a {
  border-bottom-color: #f2f6f9;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  > li > a {
	border-bottom-color: #f5f5f5;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  li.scoop-hasmenu .scoop-submenu{
  background: #ffffff ;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  li.scoop-hasmenu .scoop-submenu li > a {
	border-bottom-color: #f5f5f5;
	color:#678098;
} 
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item > li.scoop-hasmenu > a:after {
  color:#678098;
} 
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu > a:after {
  color:#678098;
} 
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  color:#678098;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu.scoop-trigger > a:after {
  color:#678098;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  > li:hover > a{
	background: #f2f6f9;
    color:#5c9acf;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  > li.active:hover > a{
	background: #f2f6f9;
    color:#5c9acf;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a{
	background: #f2f6f9;
    color:#5c9acf;
}
.scoop .scoop-navbar[navbar-theme="theme2"]  .scoop-item  > li:hover > a:before{
	 border-left-color:#5c9acf;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item > li.scoop-hasmenu:hover > a:after {
  color: #5c9acf;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item > li.scoop-trigger > a {
    background: #f2f6f9  ;
	border-bottom-color: #f5f5f5;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a:before {
   border-left-color:#5c9acf;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item > li.scoop-trigger.active > a {
    background: #f2f6f9;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu:hover > a:after {
  color: #5c9acf;
} 
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item  .scoop-hasmenu .scoop-submenu li.active > a {
	color:#5c9acf;
	background: #f2f6f9;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item > li.scoop-hasmenu.scoop-trigger.active > a:after {
  color: #5c9acf;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before  {
	border-bottom-color: #f5f5f5;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:before  {
	border-bottom-color: #5c9acf;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:after {
	border-left-color: #b4bcc8;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:after {
	border-left-color: #5c9acf;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme2"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
	border-left-color: #f5f5f5;
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar[navbar-theme="theme2"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: #f5f5f5;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-navigatio-lavel {
  color: #aaaaaa;
  border-bottom-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme2"] .scoop-item li.scoop-trigger > a > .scoop-mcaret{
	border-right-color: #5c9acf !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme2"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: #f5f5f5;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme2"] .scoop-item > li:hover > a {
    color: #414141;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme2"] .sidebar_toggle a {
  color: #000;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme2"]  .scoop-item > li.active > a {
  background: transparent !important;
  color: #414141;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme2"] .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a {
	color: #414141;
}

.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-search {
    border-top-color: #f5f5f5;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-search  input[type="text"] {
    border-bottom-color: #f5f5f5;
	color:#678098;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-search input[type="text"]:focus, 
.scoop .scoop-navbar[navbar-theme="theme2"] .style-4 input[type="text"].focus {
    border-bottom-color: #f1f1f1;
	color:#333;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-search .search-icon i {
    color: #678098;
}
.scoop .scoop-navbar[navbar-theme="theme2"] .scoop-search .searchbar-toggle:before {
	color: #678098;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme2"] .scoop-search.open .scoop-search-box {
	background: #f2f6f9;
}
/* ########### Theme2 Color Combation CSS Style Close  Here ##################### */


/* ########### Theme3 Color Combation CSS Style Start Here ##################### */
.scoop .scoop-header[header-theme="theme3"] {
	background:#f5f5f5;
	color:#777777;
}
.scoop .scoop-header[header-theme="theme3"] .sidebar_toggle a {
  background: transparent ;
  color: #777777;
  border-color: #777777;
}
.scoop .scoop-header[header-theme="theme3"] .scoop-right-header .scoop-rl-header ul > li > a, 
.scoop .scoop-header[header-theme="theme3"] .scoop-right-header .scoop-rr-header ul > li > a {
  color: #777777;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme3"]{
  background: transparent ;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme3"]{
  background: #f5f5f5;
}
.scoop  .scoop-header .scoop-left-header[lheader-theme="theme3"] .scoop-logo a {
	color:#777777;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme3"] .scoop-logo .logo-icon {
  color: #e9e9e9 ;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme3"] .scoop-logo .logo-icon {
  color: #f5f5f5;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme3"] .scoop-logo a {
    color: inherit;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme3"]{
  background: #f5f5f5;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme3"] .scoop-logo .logo-icon {
  color: #e9e9e9;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme3"] .scoop-logo a {
    color: #777777;
}
.scoop .scoop-navbar[navbar-theme="theme3"]{
	background: #f5f5f5 ;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .profile-box {
  border-bottom-color: #e8ebed;
  background:#e9e9e9;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .profile-box .media-body {
	color:#777777 ;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .profile-box .media-body .user-status b {
    color:#70ca63;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  > li > a {
  color: #777777;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  >  li.active > a {
	background: #FFF ;
	color: #448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item > li.active > a:before {
  border-left-color:#448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item > li.active > a {
  border-bottom-color: #dcdee0;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  > li > a {
	border-bottom-color: #e8ebed;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  li.scoop-hasmenu .scoop-submenu{
  background: #e9ebed ;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  li.scoop-hasmenu .scoop-submenu li > a {
	border-bottom-color: #dcdee0;
	color:#777777;
} 
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item > li.scoop-hasmenu > a:after {
  color: #777777;
} 
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu > a:after {
  color: #777777;
} 
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  color: #777777;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu.scoop-trigger > a:after {
  color: #777777;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  > li:hover > a{
	background: #FFF;
    color:#448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  > li.active:hover > a{
	background: #FFF;
    color:#448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a{
	background: #FFF;
    color:#448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"]  .scoop-item  > li:hover > a:before{
	 border-left-color:#448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item > li.scoop-hasmenu:hover > a:after {
  color: #448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item > li.scoop-trigger > a {
    background: #FFF  ;
	border-bottom-color: #dcdee0;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a:before {
   border-left-color:#448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item > li.scoop-trigger.active > a {
    background: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu:hover > a:after {
  color: #448fba;
} 
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item  .scoop-hasmenu .scoop-submenu li.active > a {
	color:#448fba;
	background: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item > li.scoop-hasmenu.scoop-trigger.active > a:after {
  color:#448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before  {
	border-bottom-color: #cdd2d6;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:before  {
	border-bottom-color: #448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:after {
	border-left-color: #cdd2d6;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:after {
	border-left-color: #448fba;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-navigatio-lavel {
  color: #9d9999;
  border-bottom-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme3"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
	border-left-color: transparent; 
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar[navbar-theme="theme3"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme3"] .scoop-item li.scoop-trigger > a > .scoop-mcaret{
	border-right-color: #e9ebed !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme3"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme3"] .scoop-item > li:hover > a {
    color: #414141;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme3"] .sidebar_toggle a {
  color: #000;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme3"]  .scoop-item > li.active > a {
  color: #414141;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme3"] .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a {
 color: #414141;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-search {
    border-top-color: #e8ebed;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-search  input[type="text"] {
    border-bottom-color: #e8ebed;
	color:#777777;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-search input[type="text"]:focus, 
.scoop .scoop-navbar[navbar-theme="theme3"] .style-4 input[type="text"].focus {
    border-bottom-color: #c9ccce;
	color:#323232;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-search .search-icon i {
    color: #777777;
}
.scoop .scoop-navbar[navbar-theme="theme3"] .scoop-search .searchbar-toggle:before {
	color: #777777;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme3"] .scoop-search.open .scoop-search-box {
	background: #FFF;
}
/* ########### Theme3 Color Combation CSS Style Close Here ##################### */
 
/* ########### Theme4 Color Combation CSS Style Start Here ##################### */
.scoop .scoop-header[header-theme="theme4"] {
	background:#354052;
	color:#8493ad;
}
.scoop .scoop-header[header-theme="theme4"] .sidebar_toggle a {
  background: transparent ;
  color: #8493ad;
  border-color: #8493ad;
}
.scoop .scoop-header[header-theme="theme4"] .scoop-right-header .scoop-rl-header ul > li > a, 
.scoop .scoop-header[header-theme="theme4"] .scoop-right-header .scoop-rr-header ul > li > a {
  color: #8493ad;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme4"]{
  background: transparent ;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme4"]{
  background: #354052;
}
.scoop  .scoop-header .scoop-left-header[lheader-theme="theme4"] .scoop-logo a {
	color:#8493ad;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme4"] .scoop-logo .logo-icon {
  color: #3f4c61 ;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme4"] .scoop-logo .logo-icon {
  color: #3f4c61;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme4"] .scoop-logo a {
    color: inherit;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme4"]{
  background: #354052;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme4"] .scoop-logo .logo-icon {
  color: #3f4c61;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme4"] .scoop-logo a {
    color: #8493ad;
}
.scoop .scoop-navbar[navbar-theme="theme4"]{
	background: #354052 ;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .profile-box {
  border-bottom-color: #485468;
 border-top: 1px solid #485468;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .profile-box .media-body {
	color:#8493ad ;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .profile-box .media-body .user-status b {
    color:#70ca63;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  > li > a {
  color: #b4bcc8;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  >  li.active > a {
	background: #3f4c61 ;
	color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item > li.active > a:before {
  border-left-color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item > li.active > a {
  border-bottom-color: #485468;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  > li > a {
	border-bottom-color: #485468;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  li.scoop-hasmenu .scoop-submenu{
  background: #3e495b ;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  li.scoop-hasmenu .scoop-submenu li > a {
	border-bottom-color: #485468;
	color:#b4bcc8;
} 
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item > li.scoop-hasmenu > a:after {
  color: #8493ad;
} 
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu > a:after {
  color: #8493ad;
} 
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  > li:hover > a{
	background: #3f4c61;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  > li.active:hover > a{
	background: #3f4c61;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a{
	background: #3f4c61;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"]  .scoop-item  > li:hover > a:before{
	 border-left-color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item > li.scoop-hasmenu:hover > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item > li.scoop-trigger > a {
    background: #3f4c61;
	border-bottom-color: #485468;
	color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a:before {
   border-left-color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item > li.scoop-trigger.active > a {
    background: #3f4c61;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu:hover > a:after {
  color: #FFF;
} 
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item  .scoop-hasmenu .scoop-submenu li.active > a {
	color:#FFF;
	background: #4f5b6f;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item > li.scoop-hasmenu.scoop-trigger.active > a:after {
  color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before  {
	border-bottom-color: #8493ad;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:before  {
	border-bottom-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:after {
	border-left-color: #8493ad;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:after {
	border-left-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-navigatio-lavel {
  color: #8493ad;
  border-bottom-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme4"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
	border-left-color: transparent; 
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar[navbar-theme="theme4"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme4"] .scoop-item li.scoop-trigger > a > .scoop-mcaret{
	border-right-color: #354052 !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme4"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme4"] .scoop-item > li:hover > a {
    color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme4"] .sidebar_toggle a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme4"]  .scoop-item > li.active > a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme4"] .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a {
 color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-search {
    border-top-color: #485468;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-search  input[type="text"] {
    border-bottom-color: #485468;
	color:#b4bcc8;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-search input[type="text"]:focus, 
.scoop .scoop-navbar[navbar-theme="theme4"] .style-4 input[type="text"].focus {
    border-bottom-color: #556175;
	color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-search .search-icon i {
    color: #b4bcc8;
}
.scoop .scoop-navbar[navbar-theme="theme4"] .scoop-search .searchbar-toggle:before {
	color: #b4bcc8;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme4"] .scoop-search.open .scoop-search-box {
	background: #3f4c61;
}
/* ########### Theme4 Color Combation CSS Style Close Here ##################### */

/* ########### Theme5 Color Combation CSS Style Start Here ##################### */
.scoop .scoop-header[header-theme="theme5"] {
	background:#3c75b5;
	color:#b9d4f2;
}
.scoop .scoop-header[header-theme="theme5"] .sidebar_toggle a {
  background: transparent ;
  color: #9abde3;
  border-color: #9abde3;
}
.scoop .scoop-header[header-theme="theme5"] .scoop-right-header .scoop-rl-header ul > li > a, 
.scoop .scoop-header[header-theme="theme5"] .scoop-right-header .scoop-rr-header ul > li > a {
  color: #9abde3;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme5"]{
  background: transparent ;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme5"]{
  background: #2b64a4;
}
.scoop  .scoop-header .scoop-left-header[lheader-theme="theme5"] .scoop-logo a {
	color:#9abde3;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme5"] .scoop-logo .logo-icon {
  color: #3c75b5;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme5"] .scoop-logo .logo-icon {
  color: #2b64a4;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme5"] .scoop-logo a {
    color: inherit;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme5"]{
  background: #2b64a4;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme5"] .scoop-logo .logo-icon {
  color: #3c75b5;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme5"] .scoop-logo a {
    color: #9abde3;
}
.scoop .scoop-navbar[navbar-theme="theme5"]{
	background: #3c75b5 ;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .profile-box {
  border-top: 1px solid #4f82bc;
  background:#3c75b5;
  border-bottom-color: transparent;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .profile-box .media-body {
	color:#9abde3 ;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .profile-box .media-body .user-status b {
    color:#70ca63;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  > li > a {
  color: #b9d4f2;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  >  li.active > a {
	background: #4f82bc ;
	color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item > li.active > a:before {
  border-left-color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item > li.active > a {
  border-bottom-color: #3c75b5 ;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  > li > a {
	border-bottom-color: #4f82bc;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  li.scoop-hasmenu .scoop-submenu{
  background: #2b64a4 ;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  li.scoop-hasmenu .scoop-submenu li > a {
	border-bottom-color: #356eae;
	color:#b9d4f2;
} 
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item > li.scoop-hasmenu > a:after {
  color: #b9d4f2;
} 
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu > a:after {
  color: #b9d4f2;
} 
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  > li:hover > a{
	background: #4f82bc;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  > li.active:hover > a{
	background: #4f82bc;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a{
	background: #4f82bc;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"]  .scoop-item  > li:hover > a:before{
	 border-left-color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item > li.scoop-hasmenu:hover > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item > li.scoop-trigger > a {
    background: #4f82bc  ;
	border-bottom-color: #3c75b5 ;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a:before {
   border-left-color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item > li.scoop-trigger.active > a {
    background: #4f82bc;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu:hover > a:after {
  color: #FFF;
} 
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item  .scoop-hasmenu .scoop-submenu li.active > a {
	color:#FFF;
	background: #4f82bc;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item > li.scoop-hasmenu.scoop-trigger.active > a:after {
  color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before  {
	border-bottom-color: #4f82bc;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:before  {
	border-bottom-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:after {
	border-left-color: #4f82bc;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:after {
	border-left-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-navigatio-lavel {
  color: #9bbde4;
  border-bottom-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme5"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
	border-left-color: transparent; 
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar[navbar-theme="theme5"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme5"] .scoop-item li.scoop-trigger > a > .scoop-mcaret{
	border-right-color: #2b64a4 !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme5"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme5"] .scoop-item > li:hover > a {
    color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme5"] .sidebar_toggle a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme5"]  .scoop-item > li.active > a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme5"] .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a {
 color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-search {
    border-top-color: #4f82bc;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-search  input[type="text"] {
    border-bottom-color: #4f82bc;
	color:#b9d4f2;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-search input[type="text"]:focus, 
.scoop .scoop-navbar[navbar-theme="theme5"] .style-4 input[type="text"].focus {
    border-bottom-color: #6699d3;
	color:#effbfb;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-search .search-icon i {
    color: #b9d4f2;
}
.scoop .scoop-navbar[navbar-theme="theme5"] .scoop-search .searchbar-toggle:before {
	color: #b9d4f2;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme5"] .scoop-search.open .scoop-search-box {
	background: #4f82bc;
}
/* ########### Theme5 Color Combation CSS Style Close Here ##################### */
/* ########### Theme6 Color Combation CSS Style Start Here ##################### */
.scoop .scoop-header[header-theme="theme6"] {
	background:#684d72;
	color:#FFF;
}
.scoop .scoop-header[header-theme="theme6"] .sidebar_toggle a {
  background: transparent ;
  color: #FFF;
  border-color: #FFF;
}
.scoop .scoop-header[header-theme="theme6"] .scoop-right-header .scoop-rl-header ul > li > a, 
.scoop .scoop-header[header-theme="theme6"] .scoop-right-header .scoop-rr-header ul > li > a {
  color: #FFF;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme6"]{
  background: transparent ;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme6"]{
  background: #583a63;
}
.scoop  .scoop-header .scoop-left-header[lheader-theme="theme6"] .scoop-logo a {
	color:#FFF;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme6"] .scoop-logo .logo-icon {
  color: #684d72;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme6"] .scoop-logo .logo-icon {
  color: #583a63;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme6"] .scoop-logo a {
    color: inherit;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme6"]{
  background: #583a63;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme6"] .scoop-logo .logo-icon {
  color: #684d72;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme6"] .scoop-logo a {
    color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"]{
	background: #583a63 ;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .profile-box {
  border-top: 1px solid transparent;
  background:#684d72;
  border-bottom-color: transparent;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .profile-box .media-body {
	color:#FFF ;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .profile-box .media-body .user-status b {
    color:#70ca63;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  > li > a {
  color: #ccbad2;
}
.scoop .scoop-navbar[navbar-theme="theme6"]  .scoop-item > li > a > .scoop-micon {
  color: #a492aa;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  >  li.active > a {
	background: #4b3154 ;
	color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"]  .scoop-item > li.active > a > .scoop-micon {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item > li.active > a:before {
  border-left-color:#af64cc;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item > li.active > a {
  border-bottom-color: #684d72 ;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  > li > a {
	border-bottom-color: #684d72;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  li.scoop-hasmenu .scoop-submenu{
  background: #51355b ;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  li.scoop-hasmenu .scoop-submenu li > a {
	border-bottom-color: #5d4865;
	color:#a492aa;
} 
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item > li.scoop-hasmenu > a:after {
  color: #a492aa;
} 
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu > a:after {
  color: #a492aa;
} 
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  > li:hover > a{
	background: #4b3154;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"]  .scoop-item > li:hover > a > .scoop-micon {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  > li.active:hover > a{
	background: #4b3154;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a{
	background: #4b3154;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"]  .scoop-item  > li:hover > a:before{
	 border-left-color:#af64cc;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item > li.scoop-hasmenu:hover > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item > li.scoop-trigger > a {
    background: #4b3154  ;
	border-bottom-color: #4f3a57 ;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a:before {
   border-left-color:#af64cc;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item > li.scoop-trigger.active > a {
    background: #4b3154;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu:hover > a:after {
  color: #FFF;
} 
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item  .scoop-hasmenu .scoop-submenu li.active > a {
	color:#FFF;
	background: #4b3154;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item > li.scoop-hasmenu.scoop-trigger.active > a:after {
  color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before  {
	border-bottom-color: #684d72;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:before  {
	border-bottom-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:after {
	border-left-color: #684d72;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:after {
	border-left-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-navigatio-lavel {
  color: #a492aa;
  border-bottom-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme6"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
	border-left-color: transparent; 
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar[navbar-theme="theme6"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme6"] .scoop-item li.scoop-trigger > a > .scoop-mcaret{
	border-right-color: #684d72 !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme6"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme6"] .scoop-item > li:hover > a {
    color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme6"] .sidebar_toggle a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme6"]  .scoop-item > li.active > a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme6"] .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a {
 color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-search {
    border-top-color: #684d72;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-search  input[type="text"] {
    border-bottom-color: #684d72;
	color:#ccbad2;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-search input[type="text"]:focus, 
.scoop .scoop-navbar[navbar-theme="theme6"] .style-4 input[type="text"].focus {
    border-bottom-color: #7a5f84;
	color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-search .search-icon i {
    color: #ccbad2;
}
.scoop .scoop-navbar[navbar-theme="theme6"] .scoop-search .searchbar-toggle:before {
	color: #a492aa;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme6"] .scoop-search.open .scoop-search-box {
	background: #4b3154;
}
/* ########### Theme6 Color Combation CSS Style Close Here ##################### */
/* ########### Theme7 Color Combation CSS Style Start Here ##################### */
.scoop .scoop-header[header-theme="theme7"] {
	background:#0aa699;
	color:#FFF;
}
.scoop .scoop-header[header-theme="theme7"] .sidebar_toggle a {
  background: transparent ;
  color: #FFF;
  border-color: #FFF;
}
.scoop .scoop-header[header-theme="theme7"] .scoop-right-header .scoop-rl-header ul > li > a, 
.scoop .scoop-header[header-theme="theme7"] .scoop-right-header .scoop-rr-header ul > li > a {
  color: #FFF;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme7"]{
  background: transparent ;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme7"]{
  background: #0b9c8f;
}
.scoop  .scoop-header .scoop-left-header[lheader-theme="theme7"] .scoop-logo a {
	color:#FFF;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme7"] .scoop-logo .logo-icon {
  color: #0aa699;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme7"] .scoop-logo .logo-icon {
  color: #079185;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme7"] .scoop-logo a {
    color: inherit;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme7"]{
  background: #0b9c8f;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme7"] .scoop-logo .logo-icon {
  color: #0aa699;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme7"] .scoop-logo a {
    color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"]{
	background: #0aa699 ;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .profile-box {
  border-top: 1px solid #0aa699;
  background:#0aa699;
  border-bottom-color: transparent;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .profile-box .media-body {
	color:#FFF ;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .profile-box .media-body .user-status b {
    color:#70ca63;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  > li > a {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"]  .scoop-item > li > a > .scoop-micon {
  color: #fff;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  >  li.active > a {
	background: #0b9c8f ;
	color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"]  .scoop-item > li.active > a > .scoop-micon {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item > li.active > a:before {
  border-left-color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item > li.active > a {
  border-bottom-color: #1ab0a3 ;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  > li > a {
	border-bottom-color: #1ab0a3;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  li.scoop-hasmenu .scoop-submenu{
  background: #079185 ;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  li.scoop-hasmenu .scoop-submenu li > a {
	border-bottom-color: #0b9c8f;
	color:#FFF;
} 
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item > li.scoop-hasmenu > a:after {
  color: #FFF;
} 
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu > a:after {
  color: #FFF;
} 
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  > li:hover > a{
	background: #0b9c8f;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"]  .scoop-item > li:hover > a > .scoop-micon {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  > li.active:hover > a{
	background: #0b9c8f;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a{
	background: #0b9c8f;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"]  .scoop-item  > li:hover > a:before{
	 border-left-color:#07786f;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item > li.scoop-hasmenu:hover > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item > li.scoop-trigger > a {
    background: #0b9c8f  ;
	border-bottom-color: #0b9c8f ;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a:before {
   border-left-color:#07786f;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item > li.scoop-trigger.active > a {
    background: #0b9c8f;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu:hover > a:after {
  color: #FFF;
} 
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item  .scoop-hasmenu .scoop-submenu li.active > a {
	color:#FFF;
	background: #14a598;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item > li.scoop-hasmenu.scoop-trigger.active > a:after {
  color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before  {
	border-bottom-color: #07786f;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:before  {
	border-bottom-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:after {
	border-left-color: #07786f;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:after {
	border-left-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-navigatio-lavel {
  color: #FFF;
  border-bottom-color: transparent;
  text-transform: capitalize;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme7"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
	border-left-color: transparent; 
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar[navbar-theme="theme7"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme7"] .scoop-item li.scoop-trigger > a > .scoop-mcaret{
	border-right-color: #079185 !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme7"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme7"] .scoop-item > li:hover > a {
    color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme7"] .sidebar_toggle a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme7"]  .scoop-item > li.active > a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme7"] .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a {
 color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-search {
    border-top-color: #1ab0a3;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-search  input[type="text"] {
    border-bottom-color: #1ab0a3;
	color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-search input[type="text"]:focus, 
.scoop .scoop-navbar[navbar-theme="theme7"] .style-4 input[type="text"].focus {
    border-bottom-color: #2fc5b8;
	color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-search .search-icon i {
    color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme7"] .scoop-search .searchbar-toggle:before {
	color: #FFF;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme7"] .scoop-search.open .scoop-search-box {
	background: #0b9c8f;
}
/* ########### Theme7 Color Combation CSS Style Close Here ##################### */
/* ########### Theme8 Color Combation CSS Style Start Here ##################### */
.scoop .scoop-header[header-theme="theme8"] {
	background:#4e6ca5;
	color:#FFF;
}
.scoop .scoop-header[header-theme="theme8"] .sidebar_toggle a {
  background: transparent ;
  color: #FFF;
  border-color: #FFF;
}
.scoop .scoop-header[header-theme="theme8"] .scoop-right-header .scoop-rl-header ul > li > a, 
.scoop .scoop-header[header-theme="theme8"] .scoop-right-header .scoop-rr-header ul > li > a {
  color: #FFF;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme8"]{
  background: transparent ;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme8"]{
  background: #3f5485;
}
.scoop .scoop-header .scoop-left-header[lheader-theme="theme8"] .scoop-logo a {
	color:#FFF;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme8"] .scoop-logo .logo-icon {
  color: #4e6ca5 ;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme8"] .scoop-logo .logo-icon {
  color: #3f5485 ;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme8"] .scoop-logo a {
    color: inherit;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme8"]{
  background: #3f5485;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme8"] .scoop-logo .logo-icon {
  color: #4e6ca5;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme8"] .scoop-logo a {
    color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"]{
	background: #3f5485 ;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .profile-box {
  border-top: 1px solid #4e6ca5;
  background:#3f5485;
  border-bottom-color: transparent;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .profile-box .media-body {
	color:#FFF ;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .profile-box .media-body .user-status b {
    color:#70ca63;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  > li > a {
  color: #d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme8"]  .scoop-item > li > a > .scoop-micon {
  color: #a0abc4;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  >  li.active > a {
	background: #4e6ca5 ;
	color: #f8f5f5;
}
.scoop .scoop-navbar[navbar-theme="theme8"]  .scoop-item > li.active > a > .scoop-micon {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item > li.active > a:before {
  border-left-color:#f8f5f5;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item > li.active > a {
  border-bottom-color: #3f5485 ;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  > li > a {
	border-bottom-color: #4a5f90;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  li.scoop-hasmenu .scoop-submenu{
  background: #2f4475;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  li.scoop-hasmenu .scoop-submenu li > a {
	border-bottom-color: #3f5485;
	color:#d8e3fc;
} 
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item > li.scoop-hasmenu > a:after {
  color: #d8e3fc;
} 
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu > a:after {
  color: #d8e3fc;
} 
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  > li:hover > a{
	background: #4e6ca5;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"]  .scoop-item > li:hover > a > .scoop-micon {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  > li.active:hover > a{
	background: #4e6ca5;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a{
	background: #3b5081;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"]  .scoop-item  > li:hover > a:before{
	 border-left-color:#d8e0f9;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item > li.scoop-hasmenu:hover > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item > li.scoop-trigger > a {
    background: #4e6ca5  ;
	border-bottom-color: #3f5485 ;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a:before {
   border-left-color:#d8e0f9;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item > li.scoop-trigger.active > a {
    background: #4e6ca5;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu:hover > a:after {
  color: #FFF;
} 
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item  .scoop-hasmenu .scoop-submenu li.active > a {
	color:#FFF;
	background: #3b5081;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item > li.scoop-hasmenu.scoop-trigger.active > a:after {
  color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before  {
	border-bottom-color: #07786f;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:before  {
	border-bottom-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:after {
	border-left-color: #07786f;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:after {
	border-left-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-navigatio-lavel {
  color: #c4c6d3;
  border-bottom-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme8"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
	border-left-color: transparent; 
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar[navbar-theme="theme8"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme8"] .scoop-item li.scoop-trigger > a > .scoop-mcaret{
	border-right-color: #2f4475 !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme8"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme8"] .scoop-item > li:hover > a {
    color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme8"] .sidebar_toggle a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme8"]  .scoop-item > li.active > a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme8"] .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a {
 color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-search {
    border-top-color: #4a5f90;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-search  input[type="text"] {
    border-bottom-color: #4a5f90;
	color:#d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-search input[type="text"]:focus, 
.scoop .scoop-navbar[navbar-theme="theme8"] .style-4 input[type="text"].focus {
    border-bottom-color: #667bac;
	color:#d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-search .search-icon i {
    color: #d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme8"] .scoop-search .searchbar-toggle:before {
	color: #a0abc4;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme8"] .scoop-search.open .scoop-search-box {
	background: #4e6ca5;
}
/* ########### Theme8 Color Combation CSS Style Close Here ##################### */
.scoop .scoop-header[header-theme="theme9"] {
	background:#576acc;
	color:#e7e7e0;
}
.scoop .scoop-header[header-theme="theme9"] .sidebar_toggle a {
  background: transparent ;
  color: #FFF;
  border-color: #FFF;
}
.scoop .scoop-header[header-theme="theme9"] .scoop-right-header .scoop-rl-header ul > li > a, 
.scoop .scoop-header[header-theme="theme9"] .scoop-right-header .scoop-rr-header ul > li > a {
  color: #FFF;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme9"]{
  background: transparent ;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme9"]{
  background: #3c4fb1;
}
.scoop .scoop-header .scoop-left-header[lheader-theme="theme9"] .scoop-logo a {
	color:#FFF;
}
.scoop .scoop-header.iscollapsed .scoop-left-header[lheader-theme="theme9"] .scoop-logo .logo-icon {
  color: #576acc ;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme9"] .scoop-logo .logo-icon {
  color: #576acc ;
}
.scoop .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme9"] .scoop-logo a {
    color: inherit;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme9"]{
  background: #3c4fb1;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme9"] .scoop-logo .logo-icon {
  color: #576acc;
}
.scoop[scoop-device-type="phone"] .scoop-header.nocollapsed .scoop-left-header[lheader-theme="theme9"] .scoop-logo a {
    color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"]{
	background: #576acc ;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .profile-box {
  border-top: transparent;
  background:#576acc;
  border-bottom-color:#576acc;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .profile-box .media-body {
	color:#FFF ;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .profile-box .media-body .user-status b {
    color:#70ca63;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  > li > a {
  color: #d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme9"]  .scoop-item > li > a > .scoop-micon {
  color: #d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  >  li.active > a {
	background: #485bbd;
	color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"]  .scoop-item > li.active > a > .scoop-micon {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item > li.active > a:before {
  border-left-color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item > li.active > a {
  border-bottom-color: #6174d6 ;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  > li > a {
	border-bottom-color: #6174d6;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  li.scoop-hasmenu .scoop-submenu{
  background: #485bbd;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  li.scoop-hasmenu .scoop-submenu li > a {
	border-bottom-color: #576acc;
	color:#d8e3fc;
} 
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item > li.scoop-hasmenu > a:after {
  color: #d8e3fc;
} 
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu > a:after {
  color: #d8e3fc;
} 
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  > li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  > li.scoop-hasmenu.scoop-trigger .scoop-submenu li.scoop-hasmenu.scoop-trigger > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  > li:hover > a{
	background: #485bbd;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"]  .scoop-item > li:hover > a > .scoop-micon {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  > li.active:hover > a{
	background: #485bbd;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a{
	background: #3f52b4;
    color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"]  .scoop-item  > li:hover > a:before{
	 border-left-color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item > li.scoop-hasmenu:hover > a:after {
  color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item > li.scoop-trigger > a {
    background: #485bbd;
	border-bottom-color: #576acc;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  .scoop-hasmenu .scoop-submenu li:hover > a:before {
   border-left-color:#d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item > li.scoop-trigger.active > a {
    background: #485bbd;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  > li.scoop-hasmenu  .scoop-submenu li.scoop-hasmenu:hover > a:after {
  color: #FFF;
} 
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item  .scoop-hasmenu .scoop-submenu li.active > a {
	color:#FFF;
	background: #3f52b4;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item > li.scoop-hasmenu.scoop-trigger.active > a:after {
  color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:before  {
	border-bottom-color: #d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:before  {
	border-bottom-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item .scoop-hasmenu .scoop-submenu li > a .scoop-mtext:after {
	border-left-color: #d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-item .scoop-hasmenu .scoop-submenu li.active > a .scoop-mtext:after {
	border-left-color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-navigatio-lavel {
  color: #7689ee;
  border-bottom-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme9"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
	border-left-color: transparent; 
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"] .scoop-navbar[navbar-theme="theme9"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme9"] .scoop-item li.scoop-trigger > a > .scoop-mcaret{
	border-right-color: #576acc !important;
}
.scoop[theme-layout="vertical"][vertical-nav-type="ex-popover"] .scoop-navbar[navbar-theme="theme9"]  .scoop-item > .scoop-hasmenu.scoop-trigger > .scoop-submenu {
  border-left-color: transparent;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme9"] .scoop-item > li:hover > a {
    color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme9"] .sidebar_toggle a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme9"]  .scoop-item > li.active > a {
  color: #FFF;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-navbar[navbar-theme="theme9"] .scoop-item .scoop-hasmenu .scoop-submenu li:hover > a {
 color: #FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-search {
    border-top-color: #6174d6;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-search  input[type="text"] {
    border-bottom-color: #6174d6;
	color:#d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-search input[type="text"]:focus, 
.scoop .scoop-navbar[navbar-theme="theme9"] .style-4 input[type="text"].focus {
    border-bottom-color: #7285e7;
	color:#FFF;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-search .search-icon i {
    color: #d8e3fc;
}
.scoop .scoop-navbar[navbar-theme="theme9"] .scoop-search .searchbar-toggle:before {
	color: #d8e3fc;
}
.scoop[vertical-nav-type="collapsed"] .scoop-navbar[navbar-theme="theme9"] .scoop-search.open .scoop-search-box {
	background: #485bbd;
}
/* ########### Theme9 Color Combation CSS Style Close Here ##################### */




/* ########### Theme9 Color Combation CSS Style Start Here ##################### */
 


 
 /*################## Active Theme 1 CSS Style Start Here ################*/
.scoop .scoop-navbar[active-item-theme="theme1"] .scoop-item   li.active > a{
	background: #ff5e3a !important;
}
.scoop .scoop-navbar[active-item-theme="theme1"] .scoop-item  li.active > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme1"] .scoop-item  li:hover > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme1"] .scoop-item  li:hover > a {
    background: #ff5e3a !important;
}
.scoop .scoop-navbar[active-item-theme="theme1"] .scoop-item > li.active > a:before {
    border-left-color: #ff5e3a !important;
}
.scoop .scoop-navbar[active-item-theme="theme1"] .scoop-item  li:hover > a:before {
    border-left-color: #ff5e3a !important;
}
.scoop .scoop-navbar[active-item-theme="theme1"] .scoop-item  li.active > a:after {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme1"] .scoop-item  li.scoop-hasmenu:hover > a:after {
    color: #FFF !important;
}

 /*################## Active Theme 1 CSS Style Close Here ################*/
 /*################## Active Theme 2 CSS Style Start  Here ################*/
.scoop .scoop-navbar[active-item-theme="theme2"] .scoop-item   li.active > a{
	background: #0096b1 !important;
}
.scoop .scoop-navbar[active-item-theme="theme2"] .scoop-item  li.active > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme2"] .scoop-item  li:hover > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme2"] .scoop-item  li:hover > a {
    background: #0096b1 !important;
}
.scoop .scoop-navbar[active-item-theme="theme2"] .scoop-item > li.active > a:before {
    border-left-color: #0096b1 !important;
}
.scoop .scoop-navbar[active-item-theme="theme2"] .scoop-item  li:hover > a:before {
    border-left-color: #0096b1 !important;
}
.scoop .scoop-navbar[active-item-theme="theme2"] .scoop-item > li.active > a:after {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme2"] .scoop-item  li.scoop-hasmenu:hover > a:after {
    color: #FFF !important;
}
 /*################## Active Theme 2 CSS Style Close Here ################*/
 /*################## Active Theme 3 CSS Style Start  Here ################*/

.scoop .scoop-navbar[active-item-theme="theme3"] .scoop-item   li.active > a{
	background: #e64056 !important;
}
.scoop .scoop-navbar[active-item-theme="theme3"] .scoop-item  li:hover > a {
    background: #e64056 !important;
}
.scoop .scoop-navbar[active-item-theme="theme3"] .scoop-item > li.active > a:before {
    border-left-color: #e64056 !important;
}
.scoop .scoop-navbar[active-item-theme="theme3"] .scoop-item  li:hover > a:before {
    border-left-color: #e64056 !important;
}
.scoop .scoop-navbar[active-item-theme="theme3"] .scoop-item  li.active > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme3"] .scoop-item  li:hover > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme3"] .scoop-item > li.active > a:after {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme3"] .scoop-item  li.scoop-hasmenu:hover > a:after {
    color: #FFF !important;
}
 /*################## Active Theme 3 CSS Style Close Here ################*/
 /*################## Active Theme 4 CSS Style Start  Here ################*/
.scoop .scoop-navbar[active-item-theme="theme4"] .scoop-item   li.active > a{
	background: #f39e10 !important;
}
.scoop .scoop-navbar[active-item-theme="theme4"] .scoop-item  li:hover > a {
    background: #f39e10 !important;
}
.scoop .scoop-navbar[active-item-theme="theme4"] .scoop-item > li.active > a:before {
    border-left-color: #f39e10 !important;
}
.scoop .scoop-navbar[active-item-theme="theme4"] .scoop-item  li:hover > a:before {
    border-left-color: #f39e10 !important;
}
.scoop .scoop-navbar[active-item-theme="theme4"] .scoop-item  li.active > a {
    color: #414141 !important;
}
.scoop .scoop-navbar[active-item-theme="theme4"] .scoop-item  li:hover > a {
    color: #414141 !important;
}
.scoop .scoop-navbar[active-item-theme="theme4"] .scoop-item > li.active > a:after {
    color: #414141 !important;
}
.scoop .scoop-navbar[active-item-theme="theme4"] .scoop-item  li.scoop-hasmenu:hover > a:after {
    color: #414141 !important;
}
 /*################## Active Theme 4 CSS Style Close Here ################*/
 /*################## Active Theme 5 CSS Style Start  Here ################*/

.scoop .scoop-navbar[active-item-theme="theme5"] .scoop-item   li.active > a{
	background: #8eb021 !important;
}
.scoop .scoop-navbar[active-item-theme="theme5"] .scoop-item  li:hover > a {
    background: #8eb021 !important;
}
.scoop .scoop-navbar[active-item-theme="theme5"] .scoop-item > li.active > a:before {
    border-left-color: #8eb021 !important;
}
.scoop .scoop-navbar[active-item-theme="theme5"] .scoop-item  li:hover > a:before {
    border-left-color: #8eb021 !important;
}
.scoop .scoop-navbar[active-item-theme="theme5"] .scoop-item  li.active > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme5"] .scoop-item  li:hover > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme5"] .scoop-item > li.active > a:after {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme5"] .scoop-item  li.scoop-hasmenu:hover > a:after {
    color: #FFF !important;
}
 /*################## Active Theme 5 CSS Style Close Here ################*/
 /*################## Active Theme 6 CSS Style Start  Here ################*/
.scoop .scoop-navbar[active-item-theme="theme6"] .scoop-item   li.active > a{
	background: #354a5f !important;
}
.scoop .scoop-navbar[active-item-theme="theme6"] .scoop-item  li:hover > a {
    background: #354a5f !important;
}
.scoop .scoop-navbar[active-item-theme="theme6"] .scoop-item > li.active > a:before {
    border-left-color: #354a5f !important;
}
.scoop .scoop-navbar[active-item-theme="theme6"] .scoop-item  li:hover > a:before {
    border-left-color: #354a5f !important;
}
.scoop .scoop-navbar[active-item-theme="theme6"] .scoop-item  li.active > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme6"] .scoop-item  li:hover > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme6"] .scoop-item > li.active > a:after {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme6"] .scoop-item  li.scoop-hasmenu:hover > a:after {
    color: #FFF !important;
}
 /*################## Active Theme 6 CSS Style Close Here ################*/
 /*################## Active Theme 7 CSS Style Start  Here ################*/
.scoop .scoop-navbar[active-item-theme="theme7"] .scoop-item   li.active > a{
	background: #967adc !important;
}
.scoop .scoop-navbar[active-item-theme="theme7"] .scoop-item  li:hover > a {
    background: #967adc !important;
}
.scoop .scoop-navbar[active-item-theme="theme7"] .scoop-item > li.active > a:before {
    border-left-color: #967adc !important;
}
.scoop .scoop-navbar[active-item-theme="theme7"] .scoop-item  li:hover > a:before {
    border-left-color: #967adc !important;
}
.scoop .scoop-navbar[active-item-theme="theme7"] .scoop-item  li.active > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme7"] .scoop-item  li:hover > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme7"] .scoop-item > li.active > a:after {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme7"] .scoop-item  li.scoop-hasmenu:hover > a:after {
    color: #FFF !important;
}
 /*################## Active Theme 7 CSS Style Close Here ################*/
 /*################## Active Theme 8 CSS Style Start  Here ################*/
.scoop .scoop-navbar[active-item-theme="theme8"] .scoop-item   li.active > a{
	background: #fe9375 !important;
}
.scoop .scoop-navbar[active-item-theme="theme8"] .scoop-item  li:hover > a {
    background: #fe9375 !important;
}
.scoop .scoop-navbar[active-item-theme="theme8"] .scoop-item > li.active > a:before {
    border-left-color: #fe9375 !important;
}
.scoop .scoop-navbar[active-item-theme="theme8"] .scoop-item  li:hover > a:before {
    border-left-color: #fe9375 !important;
}
.scoop .scoop-navbar[active-item-theme="theme8"] .scoop-item  li.active > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme8"] .scoop-item  li:hover > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme8"] .scoop-item > li.active > a:after {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme8"] .scoop-item  li.scoop-hasmenu:hover > a:after {
    color: #FFF !important;
}
 /*################## Active Theme 8 CSS Style Close Here ################*/
 /*################## Active Theme 9 CSS Style Start  Here ################*/
.scoop .scoop-navbar[active-item-theme="theme9"] .scoop-item   li.active > a{
	background: #565a90 !important;
}
.scoop .scoop-navbar[active-item-theme="theme9"] .scoop-item  li:hover > a {
    background: #565a90 !important;
}
.scoop .scoop-navbar[active-item-theme="theme9"] .scoop-item > li.active > a:before {
    border-left-color: #565a90 !important;
}
.scoop .scoop-navbar[active-item-theme="theme9"] .scoop-item  li:hover > a:before {
    border-left-color: #565a90 !important;
}
.scoop .scoop-navbar[active-item-theme="theme9"] .scoop-item  li.active > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme9"] .scoop-item  li:hover > a {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme9"] .scoop-item > li.active > a:after {
    color: #FFF !important;
}
.scoop .scoop-navbar[active-item-theme="theme9"] .scoop-item  li.scoop-hasmenu:hover > a:after {
    color: #FFF !important;
}
 /*################## Active Theme 9 CSS Style Close Here ################*/
 
 
 
 
 
 
 
/* ############## Vertical Navigation View2 CSS Style Start Here ################## */

.scoop[theme-layout="vertical"][vnavigation-view="view2"][scoop-device-type="desktop"] .scoop-navbar,
.scoop[theme-layout="vertical"][vnavigation-view="view2"][scoop-device-type="tablet"] .scoop-navbar{
	margin-top:10px;
	border-radius: 4px 4px 0 0;
}
.scoop[theme-layout="vertical"][vnavigation-view="view2"][scoop-device-type="desktop"] .scoop-navbar .profile-box,
.scoop[theme-layout="vertical"][vnavigation-view="view2"][scoop-device-type="tablet"] .scoop-navbar .profile-box{
	border-radius: 4px 4px 0 0;
}
.scoop[theme-layout="vertical"][vnavigation-view="view2"][scoop-device-type="desktop"] .scoop-main-container,
.scoop[theme-layout="vertical"][vnavigation-view="view2"][scoop-device-type="tablet"] .scoop-main-container {
    padding-left: 10px;
}
.scoop[theme-layout="vertical"][vnavigation-view="view2"][scoop-device-type="desktop"] .scoop-header,
.scoop[theme-layout="vertical"][vnavigation-view="view2"][scoop-device-type="tablet"] .scoop-header {
    padding-left: 10px;
}
.scoop[theme-layout="vertical"][vnavigation-view="view2"][vertical-layout="widebox"][scoop-device-type="desktop"] .scoop-header .scoop-wrapper,
.scoop[theme-layout="vertical"][vnavigation-view="view2"][vertical-layout="widebox"][scoop-device-type="tablet"] .scoop-header .scoop-wrapper {
	 padding-left: 5px;
}

/* ############## Vertical Navigation View2 CSS Style Close Here ################## */

/* ############## Vertical Navigation View3 CSS Style Start Here ################## */

.scoop[theme-layout="vertical"][vnavigation-view="view3"] .scoop-navbar{
 top:0;
}
.scoop[theme-layout="vertical"][vnavigation-view="view3"] .scoop-main-container {
    position: unset;
}
.scoop[theme-layout="vertical"][vnavigation-view="view3"] .scoop-header .scoop-left-header {
 display:none;
}
/* ############## Vertical Navigation View3 CSS Style Close Here ################## */

/*################## Style Selector Theme Color Start ###########################*/
.scoop #styleSelector {
  min-height: 500px;
}
.scoop[vertical-placement="left"] #styleSelector  {
    border: 1px solid #e9e9e9;
    background: #FFF;
    position: absolute;
    margin: 0;
    padding: 0 0 10px;
    width: 230px;
    height: auto;
    top: 5px;
    right: -230px;
    z-index: 100;
    height: auto; 
    transition: 0.5s;
	-webkit-transition:0.5s;
	-ms-transition: 0.5s;
	-moz-transition:0.5s;
	-o-transition: 0.5s;
}
.scoop[vertical-placement="left"] #styleSelector.open {
    background: #FFF none repeat scroll 0 0;
    right: 0;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.2);
}
.scoop[vertical-placement="right"] #styleSelector  {
    border: 1px solid #e9e9e9;
    background: #FFF;
    position: absolute;
    margin: 0;
    padding: 0 0 10px;
    width: 230px;
    height: auto;
    top: 5px;
    left: -230px;
    z-index: 100;
    height: auto; 
    transition: 0.5s;
	-webkit-transition:0.5s;
	-ms-transition: 0.5s;
	-moz-transition:0.5s;
	-o-transition: 0.5s;
}
.scoop[vertical-placement="right"] #styleSelector.open {
    background: #FFF none repeat scroll 0 0;
    left: 0;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.2);
}
.selector-toggle {
    position: relative;
}
.scoop[vertical-placement="left"] .selector-toggle > a {
    border: 1px solid #e9e9e9;
    border-right: 0;
    position: absolute;
    top: -1px;
    left: -35px;
    width: 35px;
    height: 36px;
    display: block;
    cursor: pointer;
    text-align: center;
    border-radius: 4px 0 0 4px;
    background: #FFF;
    color: #9c9c9c;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.2);
}
.scoop[vertical-placement="right"] .selector-toggle > a {
    border: 1px solid #e9e9e9;
    border-right: 0;
    position: absolute;
    top: -1px;
    right: -35px;
    width: 35px;
    height: 36px;
    display: block;
    cursor: pointer;
    text-align: center;
    border-radius: 0 4px 4px 0;
    background: #FFF;
    color: #9c9c9c;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.2);
}
.selector-toggle > a:before {
    font-family: FontAwesome;
    content: "\f100";
    font-size: 22px;
    position: relative;
    float: left;
    left: 13px;
    line-height: 35px;
	transition: 0.5s;
	-webkit-transition:0.5s;
	-ms-transition: 0.5s;
	-moz-transition:0.5s;
	-o-transition: 0.5s;
}
.scoop[vertical-placement="right"] .selector-toggle > a:before {
    content: "\f101";
}
.open .selector-toggle > a:before {
    font-family: FontAwesome;
    content: "\f101";
    font-size: 22px;
    position: relative;
    float: left;
    left: 13px;
    line-height: 35px;
	transition: 0.5s;
	-webkit-transition:0.5s;
	-ms-transition: 0.5s;
	-moz-transition:0.5s;
	-o-transition: 0.5s;
}
.scoop[vertical-placement="right"] .open .selector-toggle > a:before {
    content: "\f100";
}
#styleSelector ul {
    border: 0 none;
    margin: 0;
    padding: 0;
    width: 100%;
}
#styleSelector ul li {
    border-bottom: 0px solid #e9e9e9;
    display: block;
    margin: 0;
    padding: 0px 6px;
    text-align: left;
    width: 100%;
}
#styleSelector ul li p.selector-title {
    border-bottom: 1px solid #e9e9e9;
    color: #9c9c9c;
    font-size: 14px;
    margin: 0;
    padding: 6px;
    text-align: center;
}
#styleSelector > ul > li > .sub-title {
  color: #9c9c9c;
  display: block;
  font-size: 13px;
  margin: 0;
  padding: 5px 0px;
  position: relative;
  text-align: left;
}
#styleSelector li {
    padding: 5px;
}
.theme-option select {
    background-color: #f1f1f1;
    border: 1px solid #ccc;
    border-radius: 2px;
    padding: 3px 10px;
}
@-moz-document url-prefix() {
	.theme-option select {
		background-color: #f1f1f1;
		border: 1px solid #ccc;
		border-radius: 2px;
		display: inline-block;
		font: inherit;
		line-height: 1.5em;
		padding: 3px 10px;
		margin: 0;
		box-sizing: border-box;
		-webkit-appearance: none;
		-moz-appearance: none;
	}
    .theme-option select.minimal {
        background-image: -webkit-linear-gradient(45deg, transparent 50%, gray 50%), -webkit-linear-gradient(315deg, gray 50%, transparent 50%), -webkit-linear-gradient(left, #ccc, #ccc);
        background-image: linear-gradient(45deg, transparent 50%, gray 50%), linear-gradient(135deg, gray 50%, transparent 50%), linear-gradient(to right, #ccc, #ccc);
        background-position: calc(100% - 18px) calc(1em + -1px), calc(100% - 13px) calc(1em + -1px), calc(100% - 2.5em) 4px;
        background-size: 5px 5px, 5px 5px, 1px 1.5em;
        background-repeat: no-repeat;
    }
    .theme-option select.minimal:focus,
    .theme-option select.minimal:active {
        background-image: -webkit-linear-gradient(45deg, gray 50%, transparent 50%), -webkit-linear-gradient(315deg, transparent 50%, gray 50%), -webkit-linear-gradient(left, gray, gray);
        background-image: linear-gradient(45deg, gray 50%, transparent 50%), linear-gradient(135deg, transparent 50%, gray 50%), linear-gradient(to right, gray, gray);
        background-position: calc(100% - 15px) 13px, calc(100% - 20px) 13px, calc(100% - 2.5em) 0.3em;
        background-size: 5px 5px, 5px 5px, 1px 1.5em;
        background-repeat: no-repeat;
        border-color: #66afe9;
        outline: 0;
    }
}



.theme-color {
    padding: 0px;
    width: 100%;
}
/**/

.theme-color a {
    border-radius: 50%;
    cursor: pointer;
    display: inline-block;
    height: 18px;
    width: 18px;
    margin: 0px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
}
.theme-color a:hover,
.theme-color a:focus {
    text-decoration: none;
}
/*Left Header style selector Css start here*/

.theme-color a[lheader-theme="theme1"] {
    background: #2a394f;
}
.theme-color a[lheader-theme="theme2"] {
    background: #FFF;
}
.theme-color a[lheader-theme="theme3"] {
    background: #e9e9e9;
}
.theme-color a[lheader-theme="theme4"] {
    background: #354052;
}
.theme-color a[lheader-theme="theme5"] {
    background: #2b64a4;
}
.theme-color a[lheader-theme="theme6"] {
    background: #624e89;
}
.theme-color a[lheader-theme="theme7"] {
    background: #0b9c8f;
}
.theme-color a[lheader-theme="theme8"] {
    background: #3f5485;
}
.theme-color a[lheader-theme="theme9"] {
    background: #3c4fb1;
}
 
/*Left Header style selector CSS Close here*/
/*Header style selector Css start here*/
.theme-color a[header-theme="theme1"] {
    background: #364760 ;
}
.theme-color a[header-theme="theme2"] {
    background: #FFF;
}
.theme-color a[header-theme="theme3"] {
    background: #f5f5f5 ;
}
.theme-color a[header-theme="theme4"] {
    background: #354052;
}
.theme-color a[header-theme="theme5"] {
    background: #3c75b5;
}
.theme-color a[header-theme="theme6"] {
    background: #624e89;
}
.theme-color a[header-theme="theme7"] {
    background: #0aa699;
}
.theme-color a[header-theme="theme8"] {
    background: #4e6ca5;
}
.theme-color a[header-theme="theme9"] {
    background: #485bbd;
}
 
/*Header style selector CSS Close here*/
/*Navbar style selector Css start here*/
.theme-color a[navbar-theme="theme1"] {
    background: #364760 ;
}
.theme-color a[navbar-theme="theme2"] {
    background: #FFF;
}
.theme-color a[navbar-theme="theme3"] {
    background: #f5f5f5 ;
}
.theme-color a[navbar-theme="theme4"] {
    background: #354052;
}
.theme-color a[navbar-theme="theme5"] {
    background: #3c75b5;
}
.theme-color a[navbar-theme="theme6"] {
    background: #624e89;
}
.theme-color a[navbar-theme="theme7"] {
    background: #0aa699;
}
.theme-color a[navbar-theme="theme8"] {
    background: #3f5485;
}
.theme-color a[navbar-theme="theme9"] {
    background: #485bbd;
}
 
/*Navbar style selector CSS Close here*/

/*Active Item style selector Css start here*/
.theme-color a[active-item-theme="theme1"] {
    background: #ff5e3a;
}
.theme-color a[active-item-theme="theme2"] {
    background: #0096b1;
}
.theme-color a[active-item-theme="theme3"] {
    background: #e64056;
}
.theme-color a[active-item-theme="theme4"] {
    background: #f39e10;
}
.theme-color a[active-item-theme="theme5"] {
    background: #8eb021;
}
.theme-color a[active-item-theme="theme6"] {
    background: #354a5f;
}
.theme-color a[active-item-theme="theme7"] {
    background: #967adc;
}
.theme-color a[active-item-theme="theme8"] {
    background: #fe9375;
}
.theme-color a[active-item-theme="theme9"] {
    background: #565a90;
}
/*Active Item style selector CSS Close here*/
/*Subitem style selector Css start here*/
.theme-color a[sub-item-theme="theme1"] {
    background: #70ca63;
}
.theme-color a[sub-item-theme="theme2"] {
    background: #3498DB;
}
.theme-color a[sub-item-theme="theme3"] {
    background: #485BBD;
}
.theme-color a[sub-item-theme="theme4"] {
    background: #e7604a;
}
.theme-color a[sub-item-theme="theme5"] {
    background: #333843;
}
.theme-color a[sub-item-theme="theme6"] {
    background: #624e89;
}
.theme-color a[sub-item-theme="theme7"] {
    background: #FFF;
}
.theme-color a[sub-item-theme="theme8"] {
    background: #384b5f;
}
.theme-color a[sub-item-theme="theme9"] {
    background: #578ebe;
}
/*Subitem style selector CSS Close here*/












/*background Pattern style selector Css start here*/
.theme-color a[themebg-pattern="pattern1"] {
	background-image: url("../images/pattern1.png");
}
.theme-color a[themebg-pattern="pattern2"] {
    background-image: url("../images/pattern2.png");
}
.theme-color a[themebg-pattern="pattern3"] {
    background-image: url("../images/pattern3.png");
}
.theme-color a[themebg-pattern="pattern4"] {
    background-image: url("../images/pattern4.png");
}
.theme-color a[themebg-pattern="pattern5"] {
    background-image: url("../images/pattern5.png");
}
.theme-color a[themebg-pattern="pattern6"] {
    background-image: url("../images/pattern6.png");
}
.theme-color a[themebg-pattern="pattern7"] {
    background-image: url("../images/pattern7.png");
}
.theme-color a[themebg-pattern="pattern8"] {
    background-image: url("../images/pattern8.png");
}
.theme-color a[themebg-pattern="pattern9"] {
    background-image: url("../images/pattern9.png");
}
/*background Pattern style selector CSS Close here*/

/* Plugin Option Panel Csss Close Here */

 
.panel {
  background-color: #fff;
  border: 1px solid #D1D1D1;
  border-radius: 4px;
  box-shadow: 0 0px 0px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}
.panel-default > .panel-heading {
  background-color: #fff;
  border-color: #f1f1f1;
  color: #333;
}
.panel-footer {
  background-color: #fff;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top: 1px solid #f1f1f1;
  padding: 10px 15px;
}
.sparkline-chart{
  text-align: center;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"] .spark-chart {
	display:none;
}
.scoop[theme-layout="vertical"][scoop-device-type="tablet"] .spark-chart {
	display:none;
}
.spark-chart-title {
	text-align: center;
}
.content-title {
  margin-bottom: 10px;
}
.main-title {
  color: #424242;
  font-size: 22px;
  font-weight: 400;
  margin: 0;
}
.small-text {
  font-size: 14px;
}

.info-widget .info-box-stats {
    float: left;
    margin-top: 5px;
}
.info-widget .info-box-stats p {
    font-size: 28px;
    /* margin-bottom: 14px; */
    color: #666;
    font-weight: 300;
}
.info-widget .info-box-stats span.info-box-title {
    display: block;
    font-size: 13px;
    margin-bottom: 10px;
    color: #a4a4a4;
}

.info-widget .info-box-icon {
  position: absolute;
  right: 30px;
}
.info-widget .info-box-icon i {
    font-size: 45px;
    color: #FFF;
}
.info-widget .info-box-progress {
    clear: both;
}
.info-widget .progress {
    margin: 0;
}
.progress-xs {
    height: 5px;
}
.progress-squared {
    border-radius: 0;
}
.progress-bar {
    background-color: #7a6fbe;
}
.progress-bar-success {
    background-color: #22BAA0;
}
.progress-bar-info {
    background-color: #12AFCB;
}
.progress-bar-warning {
    background-color: #f6d433;
}
.progress-bar-danger {
    background-color: #f25656;
}
 
.info-widget .earnings-panel-theme .info-box-icon i {
    color: #6a7bdb;
}
.info-widget .balance-panel-theme .info-box-icon i {
    color: #f7786c;
}
.info-widget .total-earnings-panel-theme .info-box-icon i {
    color: #36bcf8;
}
.info-widget .item-sales-panel-theme .info-box-icon i {
    color: #65d5a4;
}

.breadcrumb-container{}
.breadcrumb {
  background-color: transparent;
  border-radius: 0px;
  padding: 8px 0px;
  margin-bottom: 10px;
}
.scoop[theme-layout="vertical"][scoop-device-type="phone"] .breadcrumb {
	display:none;
}
.top-country-panel .country-name {
  color: #7e7e7e;
  font-size: 14px;
  font-weight: 400;
}
.top-country-panel .earning {
  color: #7e7e7e;
  float: right;
  font-size: 15px;
}
.top-country-panel  .list-group{
	border:1px solid #d1d1d1;
	border-radius:4px;
}
.top-country-panel  .list-group-item {
  background-color: #fff;
  border: 0px solid #f1f1f1;
  border-bottom: 1px solid #f1f1f1;
  display: block;
  margin-bottom: 0px;
  padding: 7px 15px;
  position: relative;
}
.top-country-panel  .list-group-item:last-child{
  border-bottom: 0px solid #f1f1f1;
}
.top-country-panel .top-country-title {
  font-size: 16px;
  margin: 0;
  padding: 3px 0;
}
.top-country-panel .img-thumbnail {
  background-color: #fff;
  border: 0 solid #ddd;
  border-radius: 0;
  margin-right: 5px;
  padding: 6px 8px 8px;
  position: relative;
  top: 3px;
}

.table-container{}
.table-container .table {
  border: 0px solid #f1f1f1;
  margin-bottom: 0;
}
.table-container .table .table-striped{}
.table-container .table .table-condensed{}
.table-container .table .table-hover{}
.table-container .table tr {}
.table-container .table tr td{}
.table-container .table tr th {
  border-bottom: 1px solid #f1f1f1;
  font-size: 13px;
  font-weight: 600;
  background: #00bcd4;
  color:#FFF;
}
.table-container .table > tbody > tr > td, 
.table-container .table > tbody > tr > th, 
.table-container .table > tfoot > tr > td, 
.table-container .table > tfoot > tr > th, 
.table-container .table > thead > tr > td, 
.table-container .table > thead > tr > th {
  border-top: 1px solid #f1f1f1;
  line-height: 1.42857;
  padding: 8px;
  vertical-align: top;
}

.table-container .table > tbody > tr > td,  
.table-container .table > tfoot > tr > td,  
.table-container .table > thead > tr > td{
  font-family: arial;
  font-size: 13px;
  font-weight: 300;
}
.table-container .table > tbody > tr.total > td {
  border-bottom: 1px solid #DDD;
  font-size: 14px;
  font-weight: 600;
  background:#FFF;
}
.pageview-statistics-panel .statistics-name {
  font-size: 14px;
}
.pageview-statistics-panel .value {
  float: right;
  font-size: 15px;
}
.pageview-statistics-panel  .list-group{}
.pageview-statistics-panel .list-group-item {
  background-color: #00bcd4 ;
  border: 1px solid #1dcde4;
  display: block;
  margin-bottom: -1px;
  padding: 7px 15px;
  position: relative;
  color:#FFF;
}
.pageview-statistics-panel .pageview-statistics-title {
  font-size: 18px;
  margin: 0;
  padding: 3px 0;
}



.traffic-source-panel .traffic-source-name {
  font-size: 14px;
}
.traffic-source-panel .value {
  float: right;
  font-size: 18px;
  position: relative;
  top: -4px;
}
.traffic-source-panel .value small {
  font-size: 13px;
  padding-left:10px;
  color:#f1f1f1;
}
.traffic-source-panel  .list-group{}
.traffic-source-panel .list-group-item {
  background-color: #46be8a;
  border: 1px solid #65d5a4;
  color: #fff;
  display: block;
  margin-bottom: -1px;
  padding: 10px 15px;
  position: relative;
}
.traffic-source-panel .traffic-source-title {
  font-size: 18px;
  margin: 0;
  padding: 3px 0;
}
.review-panel .review-title {
  color: #666;
  font-size: 14px;
  font-weight: 700;
}
.review-panel .user-by {
  float: right;
  font-size: 14px;
  position: absolute;
  right: 12px;
  top: 6px;
}
.review-panel .user-by a {
  color:#4d5ec1;
}
.review-panel .user-by .time-ago {
  font-size: 13px;
  padding-left:2px;
  color:#999;
}
.review-panel  .list-group{
	border:1px solid #d1d1d1;
	border-radius:4px;
}
.review-panel .list-group-item {
  background-color: #FFF;
  border: 0px solid #f1f1f1;
   border-bottom: 1px solid #f1f1f1;
  color: #333;
  display: block;
  margin-bottom: 0px;
  padding: 10px 15px;
  position: relative;
}
.review-panel  .list-group-item:last-child{
  border-bottom: 0px solid #f1f1f1;
}
.review-panel .review-heading {
  font-size: 18px;
  margin: 0;
  padding: 3px 0;
}

.rate{
	color:#f9ab49;
}
.review-panel .review-text {
  color: #7e7e7e;
  font-size: 13px;
  font-weight: 400;
  padding-top: 2px;
}
.up-arrow{
  color:#46be8a;
}
.down-arrow{
  color:#eb6357;
}
.up-arrow i{
  color:#46be8a;
  padding-left: 5px;
}
.down-arrow i{
  color:#eb6357;
  padding-left: 5px;
}








.comment-panel .user-by {
  color: #666;
  font-size: 14px;
  font-weight: 700;
}
.comment-panel .time-ago {
  float: right;
  font-size: 13px;
  position: absolute;
  right: 12px;
  top: 6px;
  color:#999;
}
.comment-panel .time-ago a {
  color:#4d5ec1;
}
 
.comment-panel  .list-group{
	border:1px solid #d1d1d1;
	border-radius:4px;

}
.comment-panel .list-group-item {
  background-color: #FFF;
  border: 0px solid #f1f1f1;
   border-bottom: 1px solid #f1f1f1;
  color: #333;
  display: block;
  margin-bottom: 0px;
  padding: 10px 15px;
  position: relative;
}
.comment-panel  .list-group-item:last-child{
  border-bottom: 0px solid #f1f1f1;
}
.comment-panel .comment-heading {
  font-size: 18px;
  margin: 0;
  padding: 3px 0;
}

.comment-panel .comment-text {
  color: #7e7e7e;
  font-size: 13px;
  font-weight: 400;
  padding-top: 2px;
}
.comment-panel .user-img {}
.comment-panel .user-img img {
  border-radius: 50%;
  height: 35px;
  width: 35px;
}
.comment-panel .item-purchase-status {
  background-color: #00bcd4;
  border-radius: 10px;
  color: #fff;
  font-size: 10px;
  font-weight: 700;
  line-height: 1;
  min-width: 10px;
  opacity: 1;
  padding: 3px 7px;
  position: relative;
  text-align: center;
  transition: opacity 0.3s linear 0s;
  -webkit-transition:opacity 0.3s linear 0s;
	-ms-transition: opacity 0.3s linear 0s;
	-moz-transition:opacity 0.3s linear 0s;
	-o-transition: opacity 0.3s linear 0s;
  vertical-align: middle;
  visibility: visible;
  white-space: nowrap;
}

.comment-panel .comment-item-status {
  padding: 4px 0;
  text-align: right;
}
.comment-panel .item-purchase-status{}
.comment-panel .comment-reply{}
.comment-panel .comment-reply a {
  color: #4285f4;
  font-size: 13px;
  padding-left: 10px;
}
.comment-panel p {
    margin: 0 0 2px;
}

.item-download-container{}
.item-download-box{}

.item-download-box .progress {
  background-color: #f5f5f5;
  border-radius: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
  height: 8px;
  margin-bottom: 10px;
  overflow: hidden;
}

.progress-bar {
  box-shadow: 0 0 0 rgba(0, 0, 0, 0.1) inset;
  color: #fff;
  float: left;
  font-size: 9px;
  height: 100%;
  line-height: 10px;
  text-align: center;
}

.item-download-box .item-name {
  color: #767676;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 5px;
}
.item-download-box .item-name img {
  border-radius: 0;
  height: 25px;
  margin-right: 5px;
  width: 25px;
  display:none
}
.item-download-box .count {
  color: #666;
  float: right;
  font-size: 14px;
  font-weight: 500;
}
.item-download-box {
  margin-bottom: 10px;
}
 
.message-panel .user-by {
  color: #666;
  font-size: 14px;
  font-weight: 600;
}
.message-panel .time-ago {
  color: #999;
  float: right;
  font-size: 11px;
  position: absolute;
  right: 8px;
  top: 6px;
}
.message-panel .time-ago a {
  color:#4d5ec1;
}
 
.message-panel  .list-group{
	border:1px solid #d1d1d1;
	border-radius:4px;

}
.message-panel .list-group-item {
  background-color: #FFF;
  border: 0px solid #f1f1f1;
   border-bottom: 1px solid #f1f1f1;
  color: #333;
  display: block;
  margin-bottom: 0px;
  padding: 10px 15px 10px;
  position: relative;
}
.message-panel  .list-group-item:last-child{
  border-bottom: 0px solid #f1f1f1;
}
.message-panel .message-heading {
  font-size: 18px;
  margin: 0;
  padding: 3px 0;
}

.message-panel .message-text {
  color: #7e7e7e;
  font-size: 13px;
  font-weight: 400;
  padding-top: 2px;
}
.message-panel .user-img {}
.message-panel .user-img img {
  border-radius: 4px;
  height: 30px;
  width: 30px;
}
.message-panel .item-purchase-status {
  background-color: #00bcd4;
  border-radius: 10px;
  color: #fff;
  font-size: 10px;
  font-weight: 700;
  line-height: 1;
  min-width: 10px;
  opacity: 1;
  padding: 3px 7px;
  position: relative;
  text-align: center;
  transition: opacity 0.3s linear 0s;
   -webkit-transition:opacity 0.3s linear 0s;
	-ms-transition: opacity 0.3s linear 0s;
	-moz-transition:opacity 0.3s linear 0s;
	-o-transition: opacity 0.3s linear 0s;
  
  vertical-align: middle;
  visibility: visible;
  white-space: nowrap;
}

.message-panel .message-item-status {
  padding: 4px 0;
  text-align: right;
}
.message-panel .item-purchase-status{}
.message-panel .message-reply{}
.message-panel .message-reply a {
  color: #4285f4;
  font-size: 13px;
  padding-left: 10px;
}
.message-panel p {
    margin: 0 0 2px;
}
.panel-body{
	overflow-x:hidden;
}
.scoop-rr-header .btn {
  font-size: 12px;
  font-weight: 600;
}

/* fixed issue  20170319 */

.scoop[theme-layout="vertical"][vertical-placement="right"] .scoop-navbar .scoop-item > li > a .scoop-micon i {
	float: none;
	padding-right: 17px;
	position: relative;
	z-index: 1051;
	left: 93%;
	top: 1px;
}
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"][vertical-placement="right"] .scoop-navbar .scoop-item > li > a .scoop-micon i {
	left: 0px;
} 
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"][vertical-placement="right"] .scoop-navbar .scoop-item > li.scoop-hasmenu.scoop-trigger > a .scoop-micon i {
	left: 94%;
} 
.scoop[theme-layout="vertical"][vertical-nav-type="collapsed"][vertical-placement="right"] .scoop-navbar .scoop-item > li.scoop-trigger.active > a .scoop-micon i {
	left: 94%;
} 

.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="desktop"] .scoop-navbar .scoop-item > li, .scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="tablet"] .scoop-navbar .scoop-item > li {
	position: static;
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="desktop"] .scoop-navbar .scoop-item, .scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="tablet"] .scoop-navbar .scoop-item {
	position: static;
}
.scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="desktop"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li, .scoop[theme-layout="vertical"][vertical-nav-type="sub-expanded"][scoop-device-type="tablet"] .scoop-navbar .scoop-item .scoop-hasmenu .scoop-submenu li {
position: static;
}
.scoop.nocollapsed[theme-layout="vertical"][scoop-device-type="phone"] .scoop-main-container {
    position: static;
}
.scoop[theme-layout="vertical"][vertical-nav-type="fullpage"] .scoop-main-container {
	position: static;
}
.scoop[theme-layout="vertical"][vnavigation-view="view3"] .scoop-main-container {
position: static;
}

.scoop-mtext-top{
	margin-top: 0px;
}

.chat_header_overflow_text{
	  white-space: nowrap; 
  width: 135px; 
  overflow: hidden;
  text-overflow: ellipsis;
}