// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_action_manager {
    // bug with scrollable inside discuss mobile without this...
    min-height: 0;
}

.o-autogrow {
    flex: 1 1 auto;
}

.o_Discuss {
    display: flex;
    height: 100%;
    min-height: 0;

    &.o-mobile {
        flex-flow: column;
        align-items: center;
    }
}

.o_Discuss_chatWindowHeader {
    width: 100%;
    flex: 0 0 auto;
}

.o_Discuss_content {
    height: 100%;
    overflow: auto;
    flex: 1 1 auto;
    display: flex;
    flex-flow: column;
}

.o_Discuss_messagingNotInitialized {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.o_Discuss_messagingNotInitializedIcon {
    margin-right: 3px;
}

.o_Discuss_mobileAddItemHeader {
    display: flex;
    justify-content: center;
    width: 100%;
    padding: 0 10px;
}

.o_Discuss_mobileAddItemHeaderInput {
    flex: 1 1 auto;
    margin-bottom: 8px;
    padding: 8px;
}

.o_Discuss_mobileMailboxSelection {
    width: 100%;
}

.o_Discuss_mobileNavbar {
    width: 100%;
}

.o_Discuss_noThread {
    display: flex;
    flex: 1 1 auto;
    width: 100%;
    align-items: center;
    justify-content: center;
}

.o_Discuss_replyingToMessageComposer {
    width: 100%;
}

.o_Discuss_sidebar {
    height: 100%;
    overflow: auto;
    padding-top: 10px;
    flex: 0 0 auto;
}

.o_Discuss_thread {
    flex: 1 1 auto;
    padding: 12px;
    border-radius: 12px;

    &.o-mobile {
        width: 100%;
    }
}

.o_Discuss_notificationList {
    width: 100%;
    flex: 1 1 auto;
}
// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_Discuss.o-mobile {
    background-color: white;
}

.o_Discuss_mobileAddItemHeaderInput {
    appearance: none;
    border: 1px solid gray('400');
    border-radius: 5px;
    outline: none;
}

// ------------------------------------------------------------------
// Added css
// ------------------------------------------------------------------


.o_Message_content_wp *:not(li) {
    max-width: 100%;
    overflow-x: auto;
    margin: 0 35px;
    background-color: #f6f6f6;
    padding: 15px;
    border-radius: 12px;
}

.vl {
    border-left: 2px solid #71639e;
    height: 100%;
  }

.hr {
    border-left: 2px solid #71639e;
    width: 120px;
}


