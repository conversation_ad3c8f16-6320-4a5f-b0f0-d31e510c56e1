!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("jquery"));else if("function"==typeof define&&define.amd)define(["jquery"],t);else{var n="object"==typeof exports?t(require("jquery")):t(e.jquery);for(var r in n)("object"==typeof exports?exports:e)[r]=n[r]}}(window,function(e){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=n(1),a=n(2),i="undefined"!=typeof window?window:{};"undefined"==typeof FusionCharts&&(FusionCharts=i.FusionCharts),void 0===o&&(o=i.jQuery),FusionCharts.addDep(a);var s,u,l,d,f=i.document,c=o,h=i.Math.min,p=function(){if(Array.isArray)return Array.isArray;var e=Object.prototype.toString,t=e.call([]);return function(n){return e.call(n)===t}}(),b={feed:"feedData",setdata:"setData",setdataforid:"setDataForId",getdata:"getData",getdataforid:"getDataForId",clear:"clearChart",stop:"stopUpdate",start:"restartUpdate"},g={feedData:function(e){return"string"==typeof e?[e]:!("object"!==(void 0===e?"undefined":r(e))||!e.stream)&&[e.stream]},getData:function(e){return isNaN(e)?"object"===(void 0===e?"undefined":r(e))&&e.index?[e.index]:[]:[e]},getDataForId:function(e){return"string"==typeof e?[e]:"object"===(void 0===e?"undefined":r(e))&&e.id?[e.id]:[]},setData:function(e,t,n){var o=[];return"object"!==(void 0===e?"undefined":r(e))?o=[e,t,n]:(e.value&&o.push(e.value),e.label&&o.push(e.label)),o},setDataForId:function(e,t,n){var o=[];return"string"==typeof e||"string"==typeof t||"string"==typeof n?o=[e,t,n]:"object"===(void 0===e?"undefined":r(e))&&(e.value&&o.push(e.value),e.label&&o.push(e.label)),o},clearChart:function(e){return[e]},stopUpdate:function(e){return[e]},restartUpdate:function(e){return[e]}};c.FusionCharts=FusionCharts,s=function(e,t){var n,r,o,a,s;for(r=p(t)||t instanceof c?h(e.length,t.length):e.length,n=0;n<r;n+=1)o=p(t)||t instanceof c?t[n]:t,e[n].parentNode?FusionCharts.render(c.extend({},o,{renderAt:e[n]})):(a=new FusionCharts(c.extend({},o,{renderAt:e[n]})),c.FusionCharts.delayedRender||(c.FusionCharts.delayedRender={}),c.FusionCharts.delayedRender[a.id]=e[n],(s=f.createElement("script")).setAttribute("type","text/javascript"),/msie/i.test(i.navigator.userAgent)&&!i.opera?s.text="FusionCharts.items['"+a.id+"'].render();":s.appendChild(f.createTextNode("FusionCharts.items['"+a.id+"'].render()")),e[n].appendChild(s));return e},u=function(e,t){var n,o;o=c.extend({},e),c.extend(o,c.Event("fusioncharts"+e.eventType)),o.sender&&o.sender.options?"object"===(void 0===(n=o.sender.options.containerElement||o.sender.options.containerElementId)?"undefined":r(n))?c(n).trigger(o,t):c("#"+n).length?c("#"+n).trigger(o,t):c(f).trigger(o,t):c(f).trigger(o,t)},FusionCharts.addEventListener("*",u),l=function(e){return e.filter(":FusionCharts").add(e.find(":FusionCharts"))},d=function(e,t,n){"object"===(void 0===t?"undefined":r(t))&&e.each(function(){this.configureLink(t,n)})},c.fn.insertFusionCharts=function(e){return s(this,e)},c.fn.appendFusionCharts=function(e){return e.insertMode="append",s(this,e)},c.fn.prependFusionCharts=function(e){return e.insertMode="prepend",s(this,e)},c.fn.attrFusionCharts=function(e,t){var n=[],o=l(this);return t!==undefined?(o.each(function(){this.FusionCharts.setChartAttribute(e,t)}),this):"object"===(void 0===e?"undefined":r(e))?(o.each(function(){this.FusionCharts.setChartAttribute(e)}),this):(o.each(function(){n.push(this.FusionCharts.getChartAttribute(e))}),n)},c.fn.updateFusionCharts=function(e){var t,n,r,o,a,i={},s=l(this),u=[["swfUrl",!1],["type",!1],["height",!1],["width",!1],["containerBackgroundColor",!0],["containerBackgroundAlpha",!0],["dataFormat",!1],["dataSource",!1]];for(t=0,n=u.length;t<n;t+=1)a=u[t][0],i.type=i.type||i.swfUrl,e[a]&&(u[t][1]&&(o=!0),i[a]=e[a]);return s.each(function(){r=this.FusionCharts,o?r.clone(i).render():(i.dataSource===undefined&&i.dataFormat===undefined||(i.dataSource===undefined?r.setChartData(r.args.dataSource,i.dataFormat):i.dataFormat===undefined?r.setChartData(i.dataSource,r.args.dataFormat):r.setChartData(i.dataSource,i.dataFormat)),i.width===undefined&&i.height===undefined||r.resizeTo(i.width,i.height),i.type&&r.chartType(i.type))}),this},c.fn.getFusionCharts=function(){var e=[];return l(this).each(function(){e.push(this.FusionCharts)}),e},c.fn.cloneFusionCharts=function(e,t){var n,r;return"function"!=typeof e&&"function"==typeof t&&(r=e,e=t,t=r),n=[],l(this).each(function(){n.push(this.FusionCharts.clone(t,{},!0))}),e.call(c(n),n),this},c.fn.disposeFusionCharts=function(){return l(this).each(function(){this.FusionCharts.dispose(),delete this.FusionCharts,0===this._fcDrillDownLevel&&delete this._fcDrillDownLevel}),this},c.fn.convertToFusionCharts=function(e,t){var n=[];return"undefined"==typeof e.dataConfiguration&&(e.dataConfiguration={}),c.extend(!0,e.dataConfiguration,t),e.dataSource||(e.dataSource=this.get(0)),e.renderAt?"string"==typeof e.renderAt?n.push(c("#"+e.renderAt).insertFusionCharts(e).get(0)):"object"===r(e.renderAt)&&n.push(c(e.renderAt).insertFusionCharts(e).get(0)):this.each(function(){n.push(c("<div></div>").insertBefore(this).insertFusionCharts(e).get(0))}),c(n)},c.fn.drillDownFusionChartsTo=function(){var e,t,n,r,o,a=l(this);for("undefined"==typeof this._fcDrillDownLevel&&(this._fcDrillDownLevel=0),e=0,t=arguments.length;e<t;e+=1)if(o=arguments[e],p(o))for(n=0,r=o.length;n<r;n+=1)d(a,o[n],this._fcDrillDownLevel),this._fcDrillDownLevel+=1;else d(a,o,this._fcDrillDownLevel),this._fcDrillDownLevel+=1;return this},c.fn.streamFusionChartsData=function(e,t,n,r){var o,a,i,s=l(this),u=[];if((a=b[e&&e.toLowerCase()])===undefined){if(1!==arguments.length)return this;i=[e],a=b.feed}else i=1===arguments.length?[]:g[a](t,n,r);return"getData"===a||"getDataForId"===a?(s.each(function(){"function"==typeof(o=this.FusionCharts)[a]&&u.push(o[a].apply(o,i))}),u):(s.each(function(){"function"==typeof(o=this.FusionCharts)[a]&&o[a].apply(o,i)}),this)},c.extend(c.expr[":"],{FusionCharts:function(e){function t(t){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(e){return e.FusionCharts instanceof FusionCharts})})},function(t,n){t.exports=e},function(e,t,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=window,a=o.document,i=Object.prototype.toString,s=function(e,t,n,o){var a;if(n&&e.prototype&&(e=e.prototype),!0===o)!function s(e,t){var n,o;if(t instanceof Array)for(n=0;n<t.length;n+=1)"object"!==r(t[n])?e[n]=t[n]:("object"!==r(e[n])&&(e[n]=t[n]instanceof Array?[]:{}),s(e[n],t[n]));else for(n in t)"object"===r(t[n])?"[object Object]"===(o=i.call(t[n]))?("object"!==r(e[n])&&(e[n]={}),s(e[n],t[n])):"[object Array]"===o?(e[n]instanceof Array||(e[n]=[]),s(e[n],t[n])):e[n]=t[n]:e[n]=t[n];return e}(e,t);else for(a in t)e[a]=t[a];return e},u=function(e){var t,n,r=[];for(n=0,t=e.length;n<t;n+=1)3!==e[n].nodeType&&r.push(e[n]);return r},l=function(e){return e.innerText!==undefined?e.innerText:e.textContent},d=function(e){var t,n,r,o,a,i,s,l=1,d={},f=[];for(t=0,r=e.length;t<r;t+=1)for(l=1,i=0,n=0,o=(a=u(e[t].childNodes)).length;n<o;n+=1){for(d[s=n+l+i-1]&&t-d[s].rowNum<d[s].row&&(i+=d[s].col,s+=d[s].col),parseInt(a[n].getAttribute("rowspan"),10)>1&&(d[s]||(d[s]={}),d[s].rowNum=t,d[s].row=parseInt(a[n].getAttribute("rowspan"),10),parseInt(a[n].getAttribute("colspan"),10)>1?d[s].col=parseInt(a[n].getAttribute("colspan"),10):d[s].col=1);f.length<=s;)f.push({childNodes:[]});f[s].childNodes.push(a[n]),parseInt(a[n].getAttribute("colspan"),10)>1&&(l+=parseInt(a[n].getAttribute("colspan"),10)-1)}return f},f=function(e,t){for(var n=e.length;n;)if(e[n-=1]===t)return!0;return!1},c=function(e,t,n){var r,o,a,i=u(e[t].childNodes);for(r=0,o=i.length;r<o;r+=1)if(r!==n&&(a=l(i[r]),parseFloat(a)===a))return!0;return!1},h=0,p=function v(e,t,n,r){var o,a,i,s,p,b,g,y,C,m,F,w=null,j=[],x=[],S=0,_={},D=0,A=0;if(void 0===n){for(s=0,o=(p=u(e[0].childNodes)).length;s<o;s+=1)if(j[g=s+D]="__fcBLANK__"+(g+1),(b=(b=parseInt(p[s].colSpan,10))>1?b:parseInt(p[s].rowSpan,10))>1){for(a=1;a<b;a+=1)j[g+a]="__fcBLANK__"+(g+a+1);D+=b-1}for(i=0,a=s+D,o=t.length;i<o;i+=1)t[i]>0?delete j[t[i]-1]:delete j[a+t[i]];return{index:-1,labelObj:j}}if(0===n){for(i=0,a=e.length;i<a;i+=1){if(p=u(e[i].childNodes),x[i]=0,S=0,r&&r._extractByHeaderTag){for(s=0,o=p.length;s<o;s+=1)if("th"==p[s].nodeName.toLowerCase())return delete(F=v(e,t,i+1)).labelObj[r._rowLabelIndex],F}else for(s=0,o=p.length;s<o;s+=1)if(!f(t,s+1)&&!f(t,s-o))if(""!==(b=l(p[s])).replace(/^\s*/,"").replace(/\s*$/,"")){if(parseFloat(b)!=b&&(S+=1)>1)return v(e,t,i+1)}else x[i]+=1;i>0&&(x[i-1]>x[i]?w=i-1:x[i-1]<x[i]&&(w=i))}return null!==w?v(e,t,w+1):v(e,t)}for(n<0?n+=e.length:n>0&&(n-=1),p=u(e[n].childNodes),y=e[0].nodeType!==undefined,s=0,o=p.length;s<o;s+=1)if(m=0,y?"1"!==p[s].colSpan&&(m=parseInt(p[s].colSpan,10)):"1"!==p[s].rowSpan&&(m=parseInt(p[s].rowSpan,10)),m=m>1?m:0,""!==(b=l(p[s])).replace(/^\s*/,"").replace(/\s*$/,"")?_[s+A]=b:c(d(e),s,n)&&(_[s+A]="__fcBLANK__"+h,h+=1),m>1){for(b=_[s+A],i=1;i<m;i+=1)_[s+A+i]=b+" ("+i+")";A+=m-1}for(C=o+A,i=0,o=t.length;i<o;i+=1)t[i]>0?delete _[t[i]-1]:delete _[C+t[i]];return{labelObj:_,index:n}},b=function(e,t){if("string"==typeof e&&(e=a.getElementById(e)),"undefined"!=typeof o.jQuery&&e instanceof o.jQuery&&(e=e.get(0)),!e)return{data:null};t.hideTable&&(e.style.display="none");var n,r,i,s,f,c,h,b,g,y,v,C,m,F,w={},j={},x={},S=u(function(e){var t=u(e.childNodes);return t.length&&"THEAD"===t[0].nodeName&&t[1]&&"TBODY"===t[1].nodeName?t[0].childNodes:[]}(e)).concat(u(function(e){var t=u(e.childNodes);if(t.length){if("TBODY"===t[0].nodeName)return t[0];if("THEAD"===t[0].nodeName&&t[1]&&"TBODY"===t[1].nodeName)return t[1]}return e}(e).childNodes)),_=S.length,D=0,A=0,L=0,T=0,O=!1,N=t.chartType;if(-1!==["column2d","column3d","pie3d","pie2d","line","bar2d","area2d","doughnut2d","doughnut3d","pareto2d","pareto3d"].indexOf(N)&&(O=!0),t.rowLabelSource=parseInt(t.labelSource,10),t.colLabelSource=parseInt(t.legendSource,10),"column"===t.major?(v=t.useLabels?p(S,t.ignoreCols,t.rowLabelSource):p(S,t.ignoreCols),m=t.useLegend?p(d(S),t.ignoreRows,t.colLabelSource):p(d(S),t.ignoreRows)):(F=p(d(S),t.ignoreRows,t.rowLabelSource),v=t.useLabels?F:p(d(S),t.ignoreRows),t._rowLabelIndex=F.index,t._extractByHeaderTag=!0,m=t.useLegend?p(S,t.ignoreCols,t.colLabelSource,t):p(S,t.ignoreCols),delete t._extractByHeaderTag,F=v,v=m,m=F),delete v.labelObj[m.index],delete m.labelObj[v.index],"row"===t.major)for(b in m.labelObj)w[b]={};else for(b in v.labelObj)w[b]={};for(n=0;n<_;n+=1)if(v.index!==n&&m.labelObj[n]!==undefined){for(D+=1,i=u(S[n].childNodes),j[n]=0,x[n]={},r=0,h=i.length;r<h;r+=1){for(y=i[r],c=parseInt(y.getAttribute("colspan"),10),g=parseInt(y.getAttribute("rowspan"),10),f=r+j[n];T<n;){if(x[T])for(C in x[T]){if(C>f)break;n-T<=x[T][C].row&&(f+=x[T][C].col)}T+=1}if(c>1&&(j[n]+=c-1),g>1&&(x[n][f]=c>1?{row:g-1,col:c}:{row:g-1,col:1}),m.index!==f&&v.labelObj[f]!==undefined){if(L+=1,""===(s=l(y)).replace(/^\s*/,"").replace(/\s*$/,"")){if(!t.convertBlankTo)continue;s=t.convertBlankTo}if(c=c>1?c:1,g=g>1?g:1,"row"===t.major)for(T=0;T<c;){for(C=0;C<g;)w[n+C][f+T]=parseFloat(s),C+=1;T+=1}else for(T=0;T<c;){for(C=0;C<g;)w[f+T][n+C]=parseFloat(s),C+=1;T+=1}}}L>A&&(A=L)}return{data:w,chartType:N?O?"single":"multi":D>1&&A>1?"multi":"single",labelMap:m,legendMap:v}},g=function(e,t){return function(e,t){var n,r,o,a,i,u,l,d,f,c={chartAttributes:{},major:"row",useLabels:!0,useLegend:!0,labelSource:0,legendSource:0,ignoreCols:[],ignoreRows:[],showLabels:!0,showLegend:!0,seriesColors:[],convertBlankTo:"0",hideTable:!1,chartType:t.chartType&&t.chartType(),labels:[],legend:[],data:[]},h=t.args.dataConfiguration||{},p={},g={};if(s(c,h),l=(u=b(e,c)).data,"row"!==c.major?(d=u.legendMap,f=u.labelMap):(d=u.labelMap,f=u.legendMap),p.chart=s({},c.chartAttributes),"multi"===u.chartType){for(r in p.categories=[{category:[]}],p.dataset=[],a=p.categories[0].category,i=p.dataset,n=0,l)for(o in!0===c.showLabels?a.push(s({label:-1!=d.labelObj[r].indexOf("__fcBLANK__")?"":d.labelObj[r]},c.labels[n])):a.push({label:""}),n+=1,l[r])"undefined"==typeof g[o]&&(g[o]=[]),g[o].push({value:l[r][o]});for(r in n=0,g)!0===c.showLegend?i.push(s({seriesname:-1!==f.labelObj[r].indexOf("__fcBLANK__")?"":f.labelObj[r],data:g[r]},c.legend[n])):i.push({seriesname:"",data:g[r]}),n+=1}else if("single"===u.chartType)if(p.data=[],i=p.data,n=0,c.showLabels)for(r in l)for(o in l[r])i.push(s({label:-1!==d.labelObj[r].indexOf("__fcBLANK__")?"":d.labelObj[r],value:l[r][o]},c.labels[n])),n+=1;else for(r in l)for(o in l[r])i.push({value:l[r][o]});return{data:p,error:undefined}}(e,t)};function y(e){this.setChartData(e,"htmltable")}e.exports={extension:function(e){return e&&(e.prototype.setHTMLTableData=y),{format:"htmltable",toJSON:g}},name:"HTMLTable",type:"transcoder",requiresFusionCharts:!0}}])});