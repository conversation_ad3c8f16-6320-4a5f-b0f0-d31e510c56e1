# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_ldap
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# Lux <PERSON>k <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2023-07-13 13:02+0000\n"
"Last-Translator: Sengtha Chay <<EMAIL>>, 2023\n"
"Language-Team: Khmer (https://app.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_filter
msgid ""
"    Filter used to look up user accounts in the LDAP database. It is an    arbitrary LDAP filter in string representation. Any `%s` placeholder    will be replaced by the login (identifier) provided by the user, the filter    should contain at least one such placeholder.\n"
"\n"
"    The filter must result in exactly one (1) result, otherwise the login will    be considered invalid.\n"
"\n"
"    Example (actual attributes depend on LDAP server and setup):\n"
"\n"
"        (&(objectCategory=person)(objectClass=user)(sAMAccountName=%s))\n"
"\n"
"    or\n"
"\n"
"        (|(mail=%s)(uid=%s))\n"
"    "
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__create_user
msgid ""
"Automatically create local user accounts for new users authenticating via "
"LDAP"
msgstr ""
"បង្កើតគណនីអ្នកប្រើមូលដ្ឋានដោយស្វ័យប្រវត្តិសម្រាប់អ្នកប្រើថ្មីដែលផ្ទៀងផ្ទាត់តាមរយៈ"
" LDAP"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company
msgid "Companies"
msgstr "ក្រុមហ៊ុន"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__company
msgid "Company"
msgstr "ក្រុមហ៊ុន"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company_ldap
msgid "Company LDAP configuration"
msgstr "ការកំណត់រចនាសម្ព័ន្ធ LDAP របស់ក្រុមហ៊ុន"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_config_settings
msgid "Config Settings"
msgstr "កំណត់រចនាសម្ព័ន្ធ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_user
msgid "Create User"
msgstr "ការបង្កើតអ្នកប្រើប្រាស់"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_base
msgid ""
"DN of the user search scope: all descendants of this base will be searched "
"for users."
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__id
msgid "ID"
msgstr "អត្តសញ្ញាណ"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_company_ldap_view_tree
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "LDAP Configuration"
msgstr "ការកំណត់រចនាសម្ព័ន្ធ LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company__ldaps
#: model:ir.model.fields,field_description:auth_ldap.field_res_config_settings__ldaps
msgid "LDAP Parameters"
msgstr "ប៉ារ៉ាម៉ែត្រ LDAP"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_config_settings_view_form
msgid "LDAP Server"
msgstr "ម៉ាស៊ីនបម្រើ LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_server
msgid "LDAP Server address"
msgstr "LDAP អាសយដ្ឋានម៉ាស៊ីនបម្រើ LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_server_port
msgid "LDAP Server port"
msgstr "ច្រកម៉ាស៊ីនបម្រើ LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_base
msgid "LDAP base"
msgstr "LDAP មូលដ្ឋាន"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_binddn
msgid "LDAP binddn"
msgstr "LDAP binddn"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_filter
msgid "LDAP filter"
msgstr "LDAP ការបំពេញ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_password
msgid "LDAP password"
msgstr "LDAP លេខសំងាត់"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Login Information"
msgstr "ការចូលរប្រើប្រាស់ព៌ត៌មាន"

#. module: auth_ldap
#. odoo-python
#: code:addons/auth_ldap/models/res_company_ldap.py:0
#, python-format
msgid "No local user found for LDAP login and not configured to create one"
msgstr ""
"មិនមានអ្នកប្រើមូលដ្ឋានបានរកឃើញសម្រាប់ចូល LDAP "
"និងមិនត្រូវបានកំណត់រចនាសម្ព័ន្ធដើម្បីបង្កើតវា"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Process Parameter"
msgstr "ប៉ារ៉ាម៉ែត្រដំណើរការ"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_tls
msgid ""
"Request secure TLS/SSL encryption when connecting to the LDAP server. This "
"option requires a server with STARTTLS enabled, otherwise all authentication"
" attempts will fail."
msgstr ""
"ស្នើសុំការអ៊ីនគ្រីប TLS / SSL សុវត្ថិភាពពេលភ្ជាប់ទៅម៉ាស៊ីនមេ LDAP ។ "
"ជម្រើសនេះទាមទារម៉ាស៊ីនមេដែលមាន STARTTLS "
"បើមិនដូច្នេះទេការព្យាយាមផ្ទៀងផ្ទាត់នឹងបរាជ័យ។"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__sequence
msgid "Sequence"
msgstr "លំដាប់"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Server Information"
msgstr "ព័ត៌មានម៉ាស៊ីនបម្រើ"

#. module: auth_ldap
#: model:ir.actions.act_window,name:auth_ldap.action_ldap_installer
msgid "Setup your LDAP Server"
msgstr "រៀបចំម៉ាស៊ីនបម្រើ LDAP របស់អ្នក"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__user
msgid "Template User"
msgstr "គំរូការប្រើប្រាស់"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_password
msgid ""
"The password of the user account on the LDAP server that is used to query "
"the directory."
msgstr ""
"ពាក្យសម្ងាត់របស់គណនីអ្នកប្រើនៅលើម៉ាស៊ីនបម្រើ LDAP "
"ដែលត្រូវបានប្រើដើម្បីសួរថត។"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_binddn
msgid ""
"The user account on the LDAP server that is used to query the directory. "
"Leave empty to connect anonymously."
msgstr ""
"គណនីអ្នកប្រើនៅលើម៉ាស៊ីនបម្រើ LDAP ដែលត្រូវបានប្រើដើម្បីសួរថត។ "
"ទុកឱ្យទទេដើម្បីភ្ជាប់អនាមិក។"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_tls
msgid "Use TLS"
msgstr "ប្រើប្រាស់TLS"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_users
msgid "User"
msgstr "អ្នកប្រើប្រាស់"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "User Information"
msgstr "ការប្រើប្រាស់ព៌តមាន"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__user
msgid "User to copy when creating new users"
msgstr "អ្នកប្រើដើម្បីចម្លងនៅពេលបង្កើតអ្នកប្រើថ្មី"
