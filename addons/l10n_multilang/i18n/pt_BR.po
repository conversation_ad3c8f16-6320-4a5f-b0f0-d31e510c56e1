# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_multilang
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account
msgid "Account"
msgstr "Conta"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modelo de Plano da Conta"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_group
msgid "Account Group"
msgstr "Grupo de Contas"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account__name
msgid "Account Name"
msgstr "Nome da Conta"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account_tag
msgid "Account Tag"
msgstr "Etiqueta de Conta"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_analytic_account
#: model:ir.model.fields,field_description:l10n_multilang.field_account_analytic_account__name
msgid "Analytic Account"
msgstr "Conta Analítica"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_template__description
msgid "Display on Invoices"
msgstr "Exibir em Faturas"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_fiscal_position
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position__name
msgid "Fiscal Position"
msgstr "Posição Fiscal"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position_template__name
msgid "Fiscal Position Template"
msgstr "Modelo de Posição Fiscal"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_base_language_install
msgid "Install Language"
msgstr "Instalar idioma"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_journal
msgid "Journal"
msgstr "Diário"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_journal__name
msgid "Journal Name"
msgstr "Nome do Diário"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax__description
msgid "Label on Invoices"
msgstr "Etiqueta em Faturas"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_account_fiscal_position__note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "Menções legais que devem ser impressas nas faturas."

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account_template__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_chart_template__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_group__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_group_template__name
msgid "Name"
msgstr "Nome"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position__note
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position_template__note
msgid "Notes"
msgstr "Observações"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_chart_template__spoken_languages
msgid "Spoken Languages"
msgstr "Idiomas falados"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account_tag__name
msgid "Tag Name"
msgstr "Nome do Marcador"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax
msgid "Tax"
msgstr "Imposto"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_template__name
msgid "Tax Name"
msgstr "Nome da taxa"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_group_template
msgid "Template for Account Groups"
msgstr "Modelo para Grupos de Conta"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Modelo para Posição Fiscal"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account_template
msgid "Templates for Accounts"
msgstr "Modelos para Contas"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Modelos para Impostos"
