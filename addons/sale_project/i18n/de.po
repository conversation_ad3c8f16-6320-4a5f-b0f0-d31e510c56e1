# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <PERSON>, 2023
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 18:36+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Keine Meilensteine gefunden. Erstellen Sie einen!\n"
"                </p><p>\n"
"                    Verfolgen Sie die wichtigsten Fortschrittspunkte, die erreicht werden müssen, um erfolgreich zu sein.\n"
"                </p>\n"
"            "

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "%(name)s's Sales Order"
msgstr "Verkaufsaufträge von %(name)s"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Je nach Produktkonfiguration kann die gelieferte Menge automatisch durch einen Mechanismus berechnet werden:\n"
"  - Manuell: Die Menge wird manuell auf der Position eingestellt\n"
"  - Kostenrechnung aus Auslagen: Die Menge ist die Mengensumme aus gebuchten Auslagen\n"
"  - Zeiterfassung: Die Menge ist die Summe der Stunden, die für Aufgaben erfasst wurden, die mit dieser Verkaufsposition verbunden sind.\n"
"  - Lagerbuchungen: Die Menge stammt aus bestätigten Kommissionierungen\n"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "All items have been loaded"
msgstr "Alle Elemente wurden geladen"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Based on Delivered Quantity (Manual)"
msgstr "Basierend auf Gelieferter Menge (Manuell)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Based on Milestones"
msgstr "Basierend auf Meilensteinen"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_project__allow_billable
msgid "Billable"
msgstr "Abrechenbar"

#. module: sale_project
#: model:ir.model,name:sale_project.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Create Invoice"
msgstr "Rechnung erstellen"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Bei Auftrag erstellen"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__project_partner_id
msgid "Customer"
msgstr "Kunde"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Delivered"
msgstr "Geliefert"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_name
msgid "Description"
msgstr "Beschreibung"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__display_sale_order_button
msgid "Display Sales Order"
msgstr "Verkaufsauftrag anzeigen"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr "Projekt anzeigen"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr "Generiertes Projekt"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr "Generierte Aufgabe"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr "Hat abzurechnenden Verkaufsauftrag"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_with_nothing_to_invoice
msgid "Has a SO with an invoice status of No"
msgstr "Hat ein Verkaufsauftrag mit Rechnungstatus „Nichts“"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__invoice_count
msgid "Invoice Count"
msgstr "Rechnungsanzahl"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr ""
"Stellen Sie die bestellten Mengen in Rechnung, sobald diese Dienstleistung "
"verkauft ist."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create a project"
" for the order with a task for each sales order line to track the time "
"spent."
msgstr ""
"Stellen Sie die bestellten Mengen in Rechnung, sobald diese Dienstleistung "
"verkauft wurde. Erstellen Sie ein Projekt für den Auftrag mit einer Aufgabe "
"für jede Verkaufsauftragszeile, um den Zeitaufwand zu verfolgen."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create a task in"
" an existing project to track the time spent."
msgstr ""
"Stellen Sie die bestellten Mengen in Rechnung, sobald diese Dienstleistung "
"verkauft wurde. Erstellen Sie eine Aufgabe in einem bestehenden Projekt, um "
"den Zeitaufwand zu verfolgen."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create an empty "
"project for the order to track the time spent."
msgstr ""
"Stellen Sie die bestellten Mengen in Rechnung, sobald diese Dienstleistung "
"verkauft wurde. Erstellen Sie ein leeres Projekt für den Auftrag, um den "
"Zeitaufwand zu verfolgen."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). "
msgstr ""
"Stellen Sie diese Dienstleistung in Rechnung, sobald sie erbracht wurde "
"(legen Sie die Menge in den Verkaufsauftragszeilen manuell fest)."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create a project for the order with a task for each "
"sales order line to track the time spent."
msgstr ""
"Stellen Sie diese Dienstleistung in Rechnung, sobald sie erbracht wurde "
"(legen Sie die Menge in den Verkaufsauftragszeilen manuell fest). Erstellen "
"Sie ein Projekt für den Auftrag mit einer Aufgabe für jede "
"Verkaufsauftragszeile, um den Zeitaufwand zu verfolgen."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create a task in an existing project to track the time "
"spent."
msgstr ""
"Stellen Sie diese Dienstleistung in Rechnung, sobald sie erbracht wurde "
"(legen Sie die Menge in den Verkaufsauftragszeilen manuell fest). Erstellen "
"Sie eine Aufgabe in einem bestehenden Projekt, um den Zeitaufwand zu "
"verfolgen."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create an empty project for the order to track the time "
"spent."
msgstr ""
"Stellen Sie diese Dienstleistung in Rechnung, sobald sie erbracht wurde "
"(legen Sie die Menge in den Verkaufsauftragszeilen manuell fest). Erstellen "
"Sie ein leeres Projekt für den Auftrag, um den Zeitaufwand zu verfolgen."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice your milestones when they are reached."
msgstr "Stellen Sie Ihre Meilensteine in Rechnung, sobald sie erreicht sind."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create a project for the "
"order with a task for each sales order line to track the time spent."
msgstr ""
"Stellen Sie Ihre Meilensteine in Rechnung, sobald sie erreicht sind. "
"Erstellen Sie ein Projekt für den Auftrag mit einer Aufgabe für jede "
"Verkaufsauftragszeile, um den Zeitaufwand zu verfolgen."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create a task in an existing "
"project to track the time spent."
msgstr ""
"Stellen Sie Ihre Meilensteine in Rechnung, sobald sie erreicht sind. "
"Erstellen Sie eine Aufgabe in einem bestehenden Projekt, um den Zeitaufwand "
"zu verfolgen."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create an empty project for "
"the order to track the time spent."
msgstr ""
"Stellen Sie Ihre Meilensteine in Rechnung, sobald sie erreicht sind. "
"Erstellen Sie ein leeres Projekt für den Auftrag, um den Zeitaufwand zu "
"verfolgen."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Invoice your time and material to customers"
msgstr "Berechnen Sie Arbeitszeit und Material an Ihre Kunden."

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Invoiced"
msgstr "Abgerechnet"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Invoices"
msgstr "Rechnungen"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Invoicing Policy"
msgstr "Abrechnungspolitik"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__is_product_milestone
msgid "Is Product Milestone"
msgstr "Ist Produktmeilenstein"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__is_service
msgid "Is a Service"
msgstr "Ist eine Dienstleistung"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move_line
msgid "Journal Item"
msgstr "Buchungszeile"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Load more"
msgstr "Mehr laden"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_type
#: model:ir.model.fields,help:sale_project.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Mengen für den Auftrag manuell festlegen: Basierend auf der manuell eingegebenen Menge abrechnen, ohne eine Kostenstelle zu erstellen.\n"
"Zeiterfassungen auf dem Vertrag: Basierend auf den aufgezeichneten Stunden auf dem dazugehörigen Zeiterfassungsbogen berechnen.\n"
"Eine Aufgabe erstellen und Stunden aufzeichnen: Eine Aufgabe bei der Verkaufsauftragsvalidierung erstellen und Arbeitsstunden aufzeichnen."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Materials"
msgstr "Materialien"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Methode zum Aktualisieren der gelieferten Menge"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__milestone_count
msgid "Milestone Count"
msgstr "Anzahl Meilensteine"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_project.selection__sale_order_line__qty_delivered_method__milestones
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Milestones"
msgstr "Meilensteine"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "New"
msgstr "Neu"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "No Sales Order"
msgstr "Kein Verkaufsauftrag"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "No Sales Order Item"
msgstr "Keine Verkaufsauftragsposition"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "Non-billable"
msgstr "Nicht abrechenbar"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "Nichts"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr "Anzahl der Projekte"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""
"Bei der Auftragsbestätigung kann dieses Produkt ein Projekt und/oder eine Aufgabe erzeugen. Anhand dieser können Sie die von Ihnen verkaufte Dienstleistung verfolgen. \n"
"„Im Projekt des Verkaufsauftrags“: Verwendet das konfigurierte Projekt des Verkaufsauftrags, falls definiert, oder erstellt ein neues Projekt auf der Grundlage der ausgewählten Vorlage."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid "Operation not supported"
msgstr "Vorgang nicht unterstützt"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Other Revenues"
msgstr "Sonstige Einnahmen"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Other Services"
msgstr "Andere Dienstleistungen"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__quantity_percentage
msgid ""
"Percentage of the ordered quantity that will automatically be delivered once"
" the milestone is reached."
msgstr ""
"Prozentsatz der bestellten Menge, der bei Erreichen des Meilensteins "
"automatisch geliefert wird."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Portal Sale Order"
msgstr "Verkaufsauftrag im Portal"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Prepaid/Fixed Price"
msgstr "Vorkasse/Festpreis"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product Variant"
msgstr "Produktvariante"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "Projekt"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "Projekt & Aufgabe"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_milestone
msgid "Project Milestone"
msgstr "Projektmeilenstein"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_type__milestones
msgid "Project Milestones"
msgstr "Projektmeilensteine"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "Projektvorlage"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "Verkaufsauftrag des Projekts"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Projects"
msgstr "Projekte"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "Projekte in diesem Verkaufsauftrag."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__quantity_percentage
msgid "Quantity"
msgstr "Menge"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Angebotsvorlagenzeile"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__reached_milestones_ids
msgid "Reached Milestones"
msgstr "Erreichte Meilensteine"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
msgid "Sale Order"
msgstr "Verkaufsauftrag"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_count
msgid "Sale Order Count"
msgstr "Anzahl Verkaufsaufträge"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sales"
msgstr "Verkauf"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Sales & Invoicing"
msgstr "Verkauf & Rechnungsstellung"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order"
msgstr "Verkaufsauftrag"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
msgid "Sales Order Id"
msgstr "Auftrags-ID"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_task_project_user_search_inherited
#, python-format
msgid "Sales Order Item"
msgstr "Auftragsposition"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__sale_line_id
msgid "Sales Order Item that will be updated once the milestone is reached."
msgstr ""
"Verkaufsauftragsposition, die aktualisiert wird, sobald der Meilenstein "
"erreicht wird."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added in order to be invoiced to your customer.\n"
"By default the sales order item set on the project will be selected. In the absence of one, the last prepaid sales order item that has time remaining will be used.\n"
"Remove the sales order item in order to make this task non billable. You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"Verkaufsauftragsposition, zu der die für diese Aufgabe aufgewendete Zeit hinzugefügt wird, um sie Ihrem Kunden in Rechnung zu stellen.\n"
"Standardmäßig wird die für das Projekt festgelegte Verkaufsauftragsposition ausgewählt. Ist dies nicht der Fall, wird die letzte vorausbezahlte Verkaufsauftragsposition verwendet, die noch Zeit übrig hat.\n"
"Entfernen Sie die Verkaufsauftragsposition, um diese Aufgabe nichtabrechenbar zu machen. Sie können die Verkaufsauftragsposition auch für jeden Zeiterfassungseintrag einzeln ändern oder entfernen."

#. module: sale_project
#. odoo-javascript
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sales Order Items"
msgstr "Auftragspositionen"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkaufsauftragszeile"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Sales Orders"
msgstr "Verkaufsaufträge"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"Verkaufsauftragsposition, die standardmäßig auf den Aufgaben und Zeiterfassungsbögen dieses Projekts ausgewählt wird, es sei denn, der auf den Zeiterfassungsbögen eingestellte Mitarbeiter ist ausdrücklich mit einer anderen Verkaufsauftragsposition des Projekts verknüpft.\n"
"Er kann bei Bedarf für jede Aufgabe und jeden Zeiterfassungseintrag einzeln geändert werden."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "Verkaufsauftrag, mit dem das Projekt verknüpft ist."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Verkaufsauftrag, mit dem die Aufgabe verknüpft ist."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "In Rechnung suchen"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "In Verkaufsauftrag suchen"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr ""
"Wählen Sie ein nichtabrechenbares Projekt aus, um Aufgaben darin zu "
"erstellen."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "Abrechnungspolitik für Dienstleistungen"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sold"
msgstr "Verkauft"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "Aufgabe"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "Task Created (%s): %s"
msgstr "Aufgabe erstellt (%s): %s"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Aufgabenwiederholung"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "Aufgaben"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Aufgabenanalyse"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr "Aufgaben für diesen Auftrag"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr ""
"Das Produkt %s sollte kein globales Projekt haben, da es ein Projekt "
"erzeugen wird."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""
"Das Produkt %s sollte weder ein Projekt noch eine Projektvorlage haben, da "
"es kein Projekt erzeugen wird."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""
"Das Produkt %s sollte keine Projektvorlage haben, da es eine Aufgabe in "
"einem globalen Projekt erzeugen wird."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "This task has been created from: %s (%s)"
msgstr "Diese Aufgabe wurde erstellt aus: %s (%s)"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "Abzurechnen"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_type
msgid "Track Service"
msgstr "Dienstleistung verfolgen"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__vendor_bill_count
msgid "Vendor Bill Count"
msgstr "Anzahl Lieferantenrechnungen"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Vendor Bills"
msgstr "Lieferantenrechnungen"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""
"Sie können die Auftragsposition %(order_id)s - %(product_id)s nicht mit "
"dieser Aufgabe verknüpfen, da es sich um eine weiterberechnete Auslage "
"handelt."
