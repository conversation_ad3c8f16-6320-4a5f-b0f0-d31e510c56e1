<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.ListRenderer" owl="1">
        <div
            class="o_list_renderer o_renderer table-responsive"
            t-attf-class="{{ props.archInfo.className or '' }}"
            tabindex="-1"
            t-ref="root"
        >
            <t t-if="showNoContentHelper" t-call="web.ActionHelper">
                <t t-set="noContentHelp" t-value="props.noContentHelp"/>
            </t>
            <table t-attf-class="o_list_table table table-sm table-hover position-relative mb-0 {{props.list.isGrouped ? 'o_list_table_grouped' : 'o_list_table_ungrouped table-striped'}}" t-ref="table">
                <thead>
                    <tr>
                        <th t-if="hasSelectors" class="o_list_record_selector o_list_controller align-middle pe-1 cursor-pointer" tabindex="-1" t-on-keydown="(ev) => this.onCellKeydown(ev)" t-on-click.stop="toggleSelection">
                            <CheckBox disabled="!canSelectRecord" value="selectAll" className="'d-flex'" onChange.bind="toggleSelection"/>
                        </th>
                        <t t-foreach="state.columns" t-as="column" t-key="column.id">
                            <th t-if="column.type === 'field'"
                                t-att-data-name="column.name"
                                t-att-class="getColumnClass(column) + ' opacity-trigger-hover'"
                                t-on-mouseenter="ev => this.onHoverSortColumn(ev, column)"
                                t-on-mouseleave="ev => this.onHoverSortColumn(ev, column)"
                                t-on-mouseup="onColumnTitleMouseUp"
                                t-on-click="() => this.onClickSortColumn(column)"
                                t-on-keydown="(ev) => this.onCellKeydown(ev)"
                                t-att-data-tooltip-template="isDebugMode ? 'web.FieldTooltip' : 'web.ListHeaderTooltip'"
                                t-att-data-tooltip-info="makeTooltip(column)"
                                data-tooltip-delay="1000"
                                tabindex="-1">
                                <t t-if="column.hasLabel and column.widget !== 'handle'">
                                <t t-set="isNumeric" t-value="isNumericColumn(column)"/>
                                    <div t-att-class="{'d-flex': true, 'flex-row-reverse': shouldReverseHeader(column)}">
                                        <span class="d-block min-w-0 text-truncate flex-grow-1" t-att-class="isNumeric ? 'o_list_number_th' : ''"
                                              t-esc="column.label"/>
                                        <i t-att-class="getSortableIconClass(column)"/>
                                    </div>
                                    <span t-if="!isEmpty"
                                          class="o_resize position-absolute top-0 end-0 bottom-0 ps-1 bg-black-25 opacity-0 opacity-50-hover z-index-1"
                                          t-on-mousedown.stop.prevent="onStartResize"/>
                                </t>
                            </th>
                            <th t-else="" t-on-keydown="(ev) => this.onCellKeydown(ev)" t-att-class="{o_list_button: column.type === 'button_group'}"/>
                        </t>
                        <th t-if="displayOptionalFields or activeActions.onDelete" t-on-keydown="(ev) => this.onCellKeydown(ev)" class="o_list_controller o_list_actions_header position-static" style="width: 32px; min-width: 32px">
                            <Dropdown t-if="displayOptionalFields"
                                class="'o_optional_columns_dropdown border-top-0 text-center'"
                                togglerClass="'btn p-0'"
                                skipTogglerTabbing="true"
                                position="'bottom-end'">
                                <t t-set-slot="toggler">
                                    <i class="o_optional_columns_dropdown_toggle oi oi-fw oi-settings-adjust"/>
                                </t>

                                <t t-foreach="getOptionalFields" t-as="field" t-key="field_index">
                                    <DropdownItem parentClosingMode="'none'" onSelected="() => this.toggleOptionalField(field.name)">
                                        <CheckBox
                                            onChange="() => this.toggleOptionalField(field.name)"
                                            value="field.value"
                                            name="field.name"
                                        >
                                            <t t-esc="field.label"/> <t t-if="env.debug" t-esc="' (' + field.name + ')'" />
                                        </CheckBox>
                                    </DropdownItem>
                                </t>
                            </Dropdown>
                        </th>
                    </tr>
                </thead>
                <tbody class="ui-sortable">
                    <t t-call="{{ constructor.rowsTemplate }}">
                        <t t-set="list" t-value="props.list"/>
                    </t>
                </tbody>
                <tfoot t-on-click="() => props.list.unselectRecord(true)" class="o_list_footer cursor-default" t-att-class="{o_sample_data_disabled: props.list.model.useSampleModel}">
                    <tr>
                        <td t-if="hasSelectors"/>
                        <t t-foreach="state.columns" t-as="column" t-key="column.id">
                            <t t-set="aggregate" t-value="aggregates[column.name]"/>
                            <td t-if="aggregate" class="o_list_number" t-att-data-tooltip="aggregate.help">
                                <t t-esc="aggregate.value"/>
                            </td>
                            <td t-else=""/>
                        </t>
                        <td t-if="displayOptionalFields or activeActions.onDelete"/>
                    </tr>
                </tfoot>
            </table>
        </div>
    </t>

    <t t-name="web.ListRenderer.Rows" owl="1">
        <t t-if="!list.isGrouped">
            <t t-foreach="list.records" t-as="record" t-key="record.id">
                <t t-call="{{ constructor.recordRowTemplate }}"/>
            </t>
            <tr t-if="displayRowCreates">
                <td t-if="withHandleColumn"/>
                <td t-att-colspan="withHandleColumn ? nbCols - 1 : nbCols"
                    class="o_field_x2many_list_row_add"
                    t-on-keydown="(ev) => this.onCellKeydown(ev, null)"
                >
                    <t t-foreach="creates" t-as="create" t-key="create_index">
                        <a
                            t-if="create.type === 'create'"
                            href="#"
                            role="button"
                            t-att-class="create_index !== 0 ? 'ml16' : ''"
                            t-att-tabindex="props.list.editedRecord ? '-1' : '0'"
                            t-on-click.stop.prevent="() => this.add({ context: create.context })"
                        >
                            <t t-esc="create.string"/>
                        </a>
                        <ViewButton
                            t-if="create.type === 'button'"
                            className="`${create.className} ${create_index !== 0 ? 'ml16' : ''}`"
                            clickParams="create.clickParams"
                            icon="create.icon"
                            record="props.list"
                            string="create.string"
                            title="create.title"
                            tabindex="props.list.editedRecord ? '-1' : '0'"
                        />
                    </t>
                </td>
            </tr>
            <t t-if="!props.list.isGrouped">
                <tr t-foreach="getEmptyRowIds" t-as="emptyRowId" t-key="emptyRowId">
                    <td t-att-colspan="nbCols">&#8203;</td>
                </tr>
            </t>
        </t>
        <t t-else="">
            <t t-foreach="list.groups" t-as="group" t-key="group.id">
                <t t-call="{{ constructor.groupRowTemplate }}"/>
                <t t-if="!group.isFolded">
                    <t t-call="{{ constructor.rowsTemplate }}">
                        <t t-set="list" t-value="group.list"/>
                    </t>
                    <tr t-if="!group.list.isGrouped and props.editable and canCreate">
                        <td t-if="hasSelectors"/>
                        <td
                            t-att-colspan="hasSelector ? nbCols - 1 : nbCols"
                            class="o_group_field_row_add"
                        >
                            <a href="#"
                                role="button"
                                t-on-click.stop.prevent="() => group.createRecord({}, props.editable === 'top')"
                                t-on-keydown="(ev) => this.onCellKeydown(ev)"
                            >
                                Add a line
                            </a>
                        </td>
                    </tr>
                </t>
            </t>
        </t>
    </t>

    <t t-name="web.ListRenderer.GroupRow" owl="1">
        <tr t-attf-class="{{group.count > 0 ? 'o_group_has_content' : ''}} o_group_header {{!group.isFolded ? 'o_group_open' : ''}} cursor-pointer"
            t-on-click="() => this.toggleGroup(group)"
        >
            <th t-on-keydown="(ev) => this.onCellKeydown(ev, group)"
                tabindex="-1"
                t-attf-class="o_group_name fs-6 fw-bold {{!group.isFolded ? 'text-900' : 'text-700'}}"
                t-att-colspan="getGroupNameCellColSpan(group)">
                <div class="d-flex">
                    <span t-attf-class="o_group_caret fa fa-fw {{group.isFolded ? 'fa-caret-right' : 'fa-caret-down' }} me-1"
                        t-attf-style="--o-list-group-level: {{getGroupLevel(group)}}"/>
                    <t t-esc="getGroupDisplayName(group)"/> (<t t-esc="group.count"/>)
                    <div t-if="(groupByButtons[group.groupByField.name] and !group.isFolded)" class="o_group_buttons">
                        <t t-foreach="groupByButtons[group.groupByField.name]" t-as="button" t-key="button.id">
                            <t t-if="!evalModifier(button.modifiers.invisible, group.record)">
                                <t t-if="button.clickParams.type === 'edit'">
                                    <button t-att-title="button.title" class="btn" t-on-click="() => this.editGroupRecord(group)" tabindex="-1">
                                        <i t-attf-class="fa fa-fw {{button.icon}} o_button_icon"/>
                                    </button>
                                </t>
                                <t t-else="">
                                    <ViewButton
                                        className="button.className"
                                        clickParams="button.clickParams"
                                        icon="button.icon"
                                        record="group.record"
                                        string="button.string"
                                        title="button.title"
                                        tabindex="'-1'"
                                    />
                                </t>
                            </t>
                        </t>
                    </div>
                    <div t-if="showGroupPager(group)" t-on-click.stop="" class="ms-auto">
                        <Pager t-props="getGroupPagerProps(group)"/>
                    </div>
                </div>
            </th>
            <td t-on-keydown="(ev) => this.onCellKeydown(ev, group)" t-foreach="getAggregateColumns(group)" t-as="column" t-key="column.id" t-att-class="{'o_list_number': column.type === 'field'}">
                <t t-if="column.type === 'field'" t-esc="formatAggregateValue(group, column)"/>
            </td>
            <t t-set="groupPagerColspan" t-value="getGroupPagerCellColspan(group)"/>
            <th t-on-keydown="(ev) => this.onCellKeydown(ev, group)" t-if="groupPagerColspan > 0" t-att-colspan="groupPagerColspan"/>
        </tr>
    </t>

    <t t-name="web.ListRenderer.RecordRow" owl="1">
        <tr class="o_data_row"
            t-att-class="getRowClass(record)"
            t-att-data-id="record.id"
            t-on-click.capture="(ev) => this.onClickCapture(record, ev)"
            t-on-mouseover.capture="(ev) => this.ignoreEventInSelectionMode(ev)"
            t-on-mouseout.capture="(ev) => this.ignoreEventInSelectionMode(ev)"
            t-on-mouseenter.capture="(ev) => this.ignoreEventInSelectionMode(ev)"
            t-on-touchstart="(ev) => this.onRowTouchStart(record, ev)"
            t-on-touchend="() => this.onRowTouchEnd(record)"
            t-on-touchmove="() => this.onRowTouchMove(record)"
        >
            <td t-on-keydown="(ev) => this.onCellKeydown(ev, group, record)" t-if="hasSelectors" class="o_list_record_selector" t-on-click.stop="() => this.toggleRecordSelection(record)" tabindex="-1">
                <CheckBox disabled="!canSelectRecord" value="record.selected" onChange.alike="() => this.toggleRecordSelection(record)"/>
            </td>
            <t t-foreach="getColumns(record)" t-as="column" t-key="column.id">
                <t t-if="column.type === 'field'">
                    <t t-set="isInvisible" t-value="evalModifier(column.modifiers.invisible, record)"/>
                    <td t-on-keydown="(ev) => this.onCellKeydown(ev, group, record)"
                        class="o_data_cell cursor-pointer"
                        t-att-class="getCellClass(column, record)"
                        t-att-name="column.name"
                        t-att-colspan="column.colspan"
                        t-att-data-tooltip="!isInvisible ? getCellTitle(column, record) : false"
                        data-tooltip-delay="1000"
                        t-on-click="(ev) => this.onCellClicked(record, column, ev)" tabindex="-1">
                        <t t-if="!isInvisible">
                            <t t-if="canUseFormatter(column, record)" t-out="getFormattedValue(column, record)"/>
                            <Field t-else="" name="column.name" record="record" type="column.widget" class="getFieldClass(column)" fieldInfo="props.archInfo.fieldNodes[column.name]" setDirty="(isDirty) => this.setDirty(isDirty)" readonly="props.activeActions?.edit === false and !record.isNew"/>
                        </t>
                    </td>
                </t>
                <t t-if="column.type === 'button_group'">
                    <td t-on-keydown="(ev) => this.onCellKeydown(ev, group, record)" class="o_data_cell cursor-pointer" t-att-class="getCellClass(column, record)" t-on-click="(ev) => this.onButtonCellClicked(record, column, ev)" tabindex="-1">
                        <t t-foreach="column.buttons" t-as="button" t-key="button.id">
                            <ViewButton t-if="!evalModifier(button.modifiers.invisible, record)"
                                className="button.className"
                                clickParams="button.clickParams"
                                defaultRank="button.defaultRank"
                                disabled="button.disabled"
                                icon="button.icon"
                                record="record"
                                string="button.string"
                                title="button.title"
                                tabindex="props.list.editedRecord ? '-1' : '0'"
                                onClick="isX2Many and record.isVirtual ? displaySaveNotification.bind(this) : ''"
                            />
                        </t>
                    </td>
                </t>
                <t t-if="column.type === 'widget'">
                    <td class="o_data_cell" t-att-class="getCellClass(column, record)">
                        <Widget t-props="column.props" record="record"/>
                    </td>
                </t>
            </t>

            <t t-set="useUnlink" t-value="'unlink' in activeActions" />
            <t t-set="hasX2ManyAction" t-value="isX2Many and (useUnlink ? activeActions.unlink : activeActions.delete)" />
            <t t-if="displayOptionalFields or hasX2ManyAction">
                <t t-if="hasX2ManyAction">
                    <td class="o_list_record_remove text-center"
                        t-on-keydown="(ev) => this.onCellKeydown(ev, group, record)"
                        t-on-click.stop="() => this.onDeleteRecord(record)"
                        tabindex="-1"
                    >
                        <button class="fa"
                            t-att-class="{
                                'fa-trash-o': !useUnlink and activeActions.delete,
                                'fa-times': useUnlink and activeActions.unlink,
                            }"
                            name="delete"
                            aria-label="Delete row"
                            tabindex="-1"
                        />
                    </td>
                </t>
                <td t-else="" tabindex="-1" />
            </t>
        </tr>
    </t>

    <t t-name="web.ListHeaderTooltip" owl="1">
        <t t-esc="field.label"/>
        <div t-if="field.help" class="mt-2" t-esc="field.help"/>
    </t>

</templates>
