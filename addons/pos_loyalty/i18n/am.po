# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_loyalty
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Language-Team: Amharic (https://app.transifex.com/odoo/teams/41243/am/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: am\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__reward_identifier_code
msgid ""
"\n"
"        Technical field used to link multiple reward lines from the same reward together.\n"
"    "
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/PartnerLine.js:0
#, python-format
msgid "%s Points"
msgstr ""

#. module: pos_loyalty
#: model:loyalty.program,name:pos_loyalty.15_pc_on_next_order
msgid "15% on next order"
msgstr ""

#. module: pos_loyalty
#: model:loyalty.reward,description:pos_loyalty.15_pc_on_next_order_reward
msgid "15% on your order"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "A better global discount is already applied."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "A reward could not be loaded"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__promo_barcode
msgid ""
"A technical field used as an alternative to the promo code. This is "
"automatically generated when the promo code is changed."
msgstr ""

#. module: pos_loyalty
#: model_terms:ir.ui.view,arch_db:pos_loyalty.loyalty_program_view_form_inherit_pos_loyalty
msgid "All PoS"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__any_product
msgid "Any Product"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid ""
"Are you sure you want to remove %s from this order?\n"
" You will still be able to claim it through the reward button."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Balance"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__promo_barcode
#, python-format
msgid "Barcode"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_barcode_rule
msgid "Barcode Rule"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__coupon_id
#: model:ir.model.fields.selection,name:pos_loyalty.selection__barcode_rule__type__coupon
msgid "Coupon"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Coupon Codes"
msgstr ""

#. module: pos_loyalty
#: model:loyalty.program,portal_point_name:pos_loyalty.15_pc_on_next_order
msgid "Coupon point(s)"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/Orderline.xml:0
#, python-format
msgid "Current Balance:"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Customer"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Customer needed"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Deactivating reward"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_config__gift_card_settings
#: model:ir.model.fields,help:pos_loyalty.field_res_config_settings__pos_gift_card_settings
msgid "Defines the way you want to set your gift cards."
msgstr ""

#. module: pos_loyalty
#: model:ir.ui.menu,name:pos_loyalty.menu_discount_loyalty_type_config
#: model_terms:ir.ui.view,arch_db:pos_loyalty.res_config_view_form_inherit_pos_loyalty
msgid "Discount & Loyalty"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/PromoCodeButton.js:0
#: code:addons/pos_loyalty/static/src/xml/ControlButtons/PromoCodeButton.xml:0
#, python-format
msgid "Enter Code"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Enter the gift card code"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "Error"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Error validating rewards"
msgstr ""

#. module: pos_loyalty
#: model:loyalty.reward,description:pos_loyalty.loyalty_program_reward
msgid "Free Product - Simple Pen"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields.selection,name:pos_loyalty.selection__pos_config__gift_card_settings__create_set
msgid "Generate PDF cards"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Generate a Gift Card"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_config__gift_card_settings
#: model:ir.model.fields,field_description:pos_loyalty.field_res_config_settings__pos_gift_card_settings
msgid "Gift Cards settings"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/PromoCodeButton.js:0
#, python-format
msgid "Gift card or Discount code"
msgstr ""

#. module: pos_loyalty
#: model:ir.ui.menu,name:pos_loyalty.menu_gift_ewallet_type_config
#: model_terms:ir.ui.view,arch_db:pos_loyalty.res_config_view_form_inherit_pos_loyalty
msgid "Gift cards & eWallet"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__points_cost
msgid "How many point this reward cost on the coupon."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program reward. Use 1 currency per point discount."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program rule. Use 1 point per currency spent."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program. More than one reward."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program. More than one rule."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__is_reward_line
msgid "Is Reward Line"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_mail
msgid "Loyalty Communication"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr ""

#. module: pos_loyalty
#: model:loyalty.program,portal_point_name:pos_loyalty.loyalty_program
msgid "Loyalty Points"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_program
#: model:loyalty.program,name:pos_loyalty.loyalty_program
msgid "Loyalty Program"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_reward
msgid "Loyalty Reward"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "No"
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "No reward can be claimed with this coupon."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/RewardButton.js:0
#, python-format
msgid "No rewards available."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "No valid eWallet found"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/RewardButton.js:0
#, python-format
msgid "Please select a product for this reward"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/RewardButton.js:0
#, python-format
msgid "Please select a reward"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_order_count
msgid "PoS Order Count"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_card__source_pos_order_id
msgid "PoS Order Reference"
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/loyalty_program.py:0
#, python-format
msgid "PoS Orders"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_card__source_pos_order_id
msgid "PoS order where this coupon was generated."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_ok
#: model_terms:ir.ui.view,arch_db:pos_loyalty.loyalty_program_view_form_inherit_pos_loyalty
msgid "Point of Sale"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_config
msgid "Point of Sale Configuration"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_order
msgid "Point of Sale Orders"
msgstr ""

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_session
msgid "Point of Sale Session"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_config_ids
msgid "Point of Sales"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "Points"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__points_cost
msgid "Points Cost"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_mail__pos_report_print_id
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_report_print_id
msgid "Print Report"
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Program: %(name)s, Reward Product: `%(reward_product)s`"
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Program: %(name)s, Rule Product: `%(rule_product)s`"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "Refund with eWallet"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/TicketScreen.js:0
#, python-format
msgid ""
"Refunding a top up or reward product for an eWallet or gift card program is "
"not allowed."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/ControlButtons/ResetProgramsButton.xml:0
#, python-format
msgid "Reset Programs"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_program__pos_config_ids
msgid "Restrict publishing to those shops."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/ControlButtons/RewardButton.xml:0
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__reward_id
#, python-format
msgid "Reward"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__reward_identifier_code
msgid "Reward Identifier Code"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields.selection,name:pos_loyalty.selection__pos_config__gift_card_settings__scan_use
msgid "Scan existing cards"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Select program"
msgstr ""

#. module: pos_loyalty
#: model:product.template,name:pos_loyalty.simple_pen_product_template
msgid "Simple Pen"
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
#, python-format
msgid ""
"Some coupons are invalid. The applied coupons have been updated. Please "
"check the order."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Spent:"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__any_product
msgid "Technical field, whether all product match"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "That coupon code has already been scanned and activated."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "That promo code program has already been activated."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__coupon_id
msgid "The coupon used to claim that reward."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
#, python-format
msgid ""
"The following codes already exist in the database, perhaps they were already sold?\n"
"%s"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_mail__pos_report_print_id
msgid ""
"The report action to be executed when creating a coupon/gift card/loyalty "
"card in the PoS."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid ""
"The reward \"%s\" contain an error in its domain, your domain must be "
"compatible with the PoS client"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__reward_id
msgid "The reward associated with this line."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "The reward could not be applied."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/RewardButton.js:0
#, python-format
msgid "There are no rewards claimable for this customer."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
#, python-format
msgid "There are not enough points for the coupon: %s."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "There are not enough points on the coupon to claim this reward."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "There are not enough products in the basket to claim this reward."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid ""
"There is no email template on the gift card program and your pos is set to "
"print them."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid ""
"There is no print report on the gift card program and your pos is set to "
"print them."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__valid_product_ids
msgid "These are the products that are valid for this rule."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "This coupon is expired (%s)."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "This coupon is invalid (%s)."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "This gift card has already been sold"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid ""
"This gift card is not linked to any order. Do you really want to apply its "
"reward?"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_program__pos_report_print_id
msgid "This is used to print the generated gift cards from PoS."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid ""
"To continue, make the following reward products available in Point of Sale."
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_barcode_rule__type
msgid "Type"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "Unknown discount type"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "Unpaid gift card"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "Unpaid gift card rejected."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "Use eWallet to pay"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__valid_product_ids
msgid "Valid Product"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Valid until:"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__is_reward_line
msgid "Whether this line is part of a reward or not."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Won:"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Yes"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "You cannot sell a gift card that has already been sold."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "You cannot set negative quantity or price to gift card or ewallet."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid ""
"You either have not created an eWallet or all your eWallets have expired."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/loyalty_program.py:0
#, python-format
msgid "You must set '%s' before setting '%s'."
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "eWallet"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "eWallet Pay"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "eWallet Refund"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "eWallet requires a customer to be selected"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "no expiration"
msgstr ""
