# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* loyalty_delivery
# 
# Translators:
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: loyalty_delivery
#. odoo-python
#: code:addons/loyalty_delivery/models/loyalty_reward.py:0
#, python-format
msgid " (Max %s)"
msgstr "（最大%s）"

#. module: loyalty_delivery
#: model_terms:ir.ui.view,arch_db:loyalty_delivery.loyalty_reward_view_kanban_inherit_loyalty_delivery
msgid "( Max"
msgstr "( 最大"

#. module: loyalty_delivery
#. odoo-python
#: code:addons/loyalty_delivery/models/loyalty_program.py:0
#, python-format
msgid "Automatic promotion: free shipping on orders higher than $50"
msgstr "自动促销：订单高于$50可以免费送货"

#. module: loyalty_delivery
#: model:ir.model.fields.selection,name:loyalty_delivery.selection__loyalty_reward__reward_type__shipping
msgid "Free Shipping"
msgstr "免费送货"

#. module: loyalty_delivery
#. odoo-python
#: code:addons/loyalty_delivery/models/loyalty_reward.py:0
#: model_terms:ir.ui.view,arch_db:loyalty_delivery.loyalty_reward_view_form_inherit_loyalty_delivery
#: model_terms:ir.ui.view,arch_db:loyalty_delivery.loyalty_reward_view_kanban_inherit_loyalty_delivery
#, python-format
msgid "Free shipping"
msgstr "免费送货"

#. module: loyalty_delivery
#: model:ir.model,name:loyalty_delivery.model_loyalty_program
msgid "Loyalty Program"
msgstr "会员方案"

#. module: loyalty_delivery
#: model:ir.model,name:loyalty_delivery.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "会员奖励"

#. module: loyalty_delivery
#: model:ir.model.fields,field_description:loyalty_delivery.field_loyalty_reward__reward_type
msgid "Reward Type"
msgstr "奖励类型"

#. module: loyalty_delivery
#: model:ir.model,name:loyalty_delivery.model_sale_order
msgid "Sales Order"
msgstr "销售订单"
