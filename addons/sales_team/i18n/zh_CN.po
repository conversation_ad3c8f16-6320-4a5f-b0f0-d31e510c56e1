# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sales_team
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-04 09:25+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "<i class=\"fa fa-envelope me-1\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope me-1\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
msgid "<span class=\"bg-odoo\">Archived</span>"
msgstr "<span class=\"bg-odoo\">归档</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>New</span>"
msgstr "<span>新建</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>Reporting</span>"
msgstr "<span>报告</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>View</span>"
msgstr "<span>查看</span>"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_needaction
msgid "Action Needed"
msgstr "需要动作"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__active
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__active
msgid "Active"
msgstr "启用"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.mail_activity_type_action_config_sales
msgid "Activity Types"
msgstr "活动类型"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_member_action
msgid "Add a Team Member"
msgstr "添加团队成员"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__crm_team_member_ids
msgid ""
"Add members to automatically assign their documents to this sales team."
msgstr "添加成员，自动将他们的文件分配给这个销售团队。"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid ""
"Adding %(user_name)s in this team would remove him/her from its current team"
" %(team_name)s."
msgstr "在这个团队中添加%(user_name)s，将把他/她从其目前的团队%(team_name)s中移除。"

#. module: sales_team
#: code:addons/sales_team/models/crm_team_member.py:0
#, python-format
msgid ""
"Adding %(user_name)s in this team would remove him/her from its current "
"teams %(team_names)s."
msgstr "在这个团队中添加%(user_name)s，将把他/她从其目前的团队%(team_names)s中移除。"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid ""
"Adding %(user_names)s in this team would remove them from their current "
"teams (%(team_names)s)."
msgstr "在这个团队中添加%(user_names)s将把他们从当前的团队（%(team_names)s）中移除。"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_manager
msgid "Administrator"
msgstr "管理员"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Archived"
msgstr "已归档"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_attachment_count
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_attachment_count
msgid "Attachment Count"
msgstr "附件计数"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Avatar"
msgstr "形象"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Big Pretty Button :)"
msgstr "大漂亮按钮：）"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_tag
msgid "CRM Tag"
msgstr "CRM标签"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Cannot delete default team \"%s\""
msgstr "无法删除默认团队 \"%s\""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__color
msgid "Color"
msgstr "颜色"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__color
msgid "Color Index"
msgstr "颜色索引"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__company_id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__company_id
msgid "Company"
msgstr "公司"

#. module: sales_team
#: model:ir.ui.menu,name:sales_team.menu_sale_config
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "Configuration"
msgstr "配置"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor7
msgid "Consulting"
msgstr "咨询"

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_crm_tag_action
msgid "Create CRM Tags"
msgstr "创建CRM标签"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_config
msgid "Create a Sales Team"
msgstr "创建一个销售团队"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.mail_activity_type_action_config_sales
msgid "Create an Activity Type"
msgstr "创建一个活动类型"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__create_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__create_uid
msgid "Created by"
msgstr "创建人"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__create_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__create_date
msgid "Created on"
msgstr "创建时间"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__currency_id
msgid "Currency"
msgstr "币种"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_button_name
msgid "Dashboard Button"
msgstr "仪表板按钮"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_data
msgid "Dashboard Graph Data"
msgstr "数据仪表图"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_pipeline
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_sales
msgid "Define a new sales team"
msgstr "定义一个新的销售团队"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor5
msgid "Design"
msgstr "设计"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__display_name
#: model:ir.model.fields,field_description:sales_team.field_crm_team__display_name
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__email
msgid "Email"
msgstr "Email"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__favorite_user_ids
msgid "Favorite Members"
msgstr "最喜欢的成员"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_favorite
msgid ""
"Favorite teams to display them in the dashboard and access them easily."
msgstr "最喜欢的团队在仪表板中显示它们并轻松访问它们。"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid ""
"Follow this salesteam to automatically track the events associated to users "
"of this team."
msgstr "跟踪此销售团队，自动跟踪此团队中用户关联的事件。"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_follower_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_partner_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
msgid "Group By"
msgstr "分组"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Group By..."
msgstr "分组"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__has_message
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__has_message
msgid "Has Message"
msgstr "有信息"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__id
#: model:ir.model.fields,field_description:sales_team.field_crm_team__id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__id
msgid "ID"
msgstr "ID"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_membership_multi
#: model:ir.model.fields,help:sales_team.field_crm_team_member__is_membership_multi
msgid ""
"If True, users may belong to several sales teams. Otherwise membership is "
"limited to a single sales team."
msgstr "如果是真的，用户可以属于几个销售团队。否则，成员资格只限于一个销售团队。"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_needaction
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_partner__team_id
#: model:ir.model.fields,help:sales_team.field_res_users__team_id
msgid ""
"If set, this Sales Team will be used for sales and assignments related to "
"this partner"
msgstr "如果设置，此销售团队将用于与此合作伙伴相关的销售和分配"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__active
msgid ""
"If the active field is set to false, it will allow you to hide the Sales "
"Team without removing it."
msgstr "如果启用字段设置为false，它将允许您隐藏销售团队而不删除它。"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__image_1920
msgid "Image"
msgstr "图像"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__image_128
msgid "Image (128)"
msgstr "图像 (128)"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor4
msgid "Information"
msgstr "信息"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_is_follower
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag____last_update
#: model:ir.model.fields,field_description:sales_team.field_crm_team____last_update
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__write_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__write_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_main_attachment_id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_users__sale_team_id
msgid ""
"Main user sales team. Used notably for pipeline, or to set sales team in "
"invoicing or subscription."
msgstr "主要用户销售团队。主要用于管道，或在开票或订阅中设置销售团队。"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_company_ids
msgid "Member Company"
msgstr "会员公司"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__member_warning
msgid "Member Warning"
msgstr "会员警告"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Members"
msgstr "成员"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_warning
msgid "Membership Issue Warning"
msgstr "会员问题警告"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_ids
msgid "Messages"
msgstr "消息"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__mobile
msgid "Mobile"
msgstr "手机"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_membership_multi
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__is_membership_multi
msgid "Multiple Memberships Allowed"
msgstr "允许多个会员资格"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__name
msgid "Name"
msgstr "名称"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_needaction_counter
msgid "Number of Actions"
msgstr "动作数量"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要采取行动的消息数量"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息数量"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor8
msgid "Other"
msgstr "其他"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__phone
msgid "Phone"
msgstr "电话"

#. module: sales_team
#: model:crm.team,name:sales_team.pos_sales_team
msgid "Point of Sale"
msgstr "销售点"

#. module: sales_team
#: model:crm.team,name:sales_team.crm_team_1
msgid "Pre-Sales"
msgstr "售前服务"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor1
msgid "Product"
msgstr "产品"

#. module: sales_team
#: model:crm.team,name:sales_team.team_sales_department
msgid "Sales"
msgstr "销售"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_tree
msgid "Sales Men"
msgstr "销售"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
msgid "Sales Person"
msgstr "销售员"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__name
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__crm_team_id
#: model:ir.model.fields,field_description:sales_team.field_res_partner__team_id
#: model:ir.model.fields,field_description:sales_team.field_res_users__team_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_tree
msgid "Sales Team"
msgstr "销售团队"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team_member
msgid "Sales Team Member"
msgstr "销售团队成员"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__crm_team_member_ids
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_member_ids
msgid "Sales Team Members"
msgstr "销售团队成员"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__crm_team_member_all_ids
msgid "Sales Team Members (incl. inactive)"
msgstr "销售团队成员（包括不活跃的）。"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_config
#: model:ir.actions.act_window,name:sales_team.crm_team_action_sales
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_ids
msgid "Sales Teams"
msgstr "销售团队"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_id
msgid "Salesperson"
msgstr "销售员"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_ids
msgid "Salespersons"
msgstr "销售员"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Salesteams Search"
msgstr "销售团队搜索"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Sample data"
msgstr "示例数据"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__sequence
msgid "Sequence"
msgstr "序号"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor3
msgid "Services"
msgstr "服务"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_favorite
msgid "Show on dashboard"
msgstr "显示仪表"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor2
msgid "Software"
msgstr "软件"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__name
msgid "Tag Name"
msgstr "标签名"

#. module: sales_team
#: model:ir.model.constraint,message:sales_team.constraint_crm_tag_name_uniq
msgid "Tag name already exists !"
msgstr "标签名称已存在!"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.sales_team_crm_tag_action
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_tree
msgid "Tags"
msgstr "标签"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Team Details"
msgstr "团队详情"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__user_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Team Leader"
msgstr "团队负责人"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_member_action
msgid "Team Members"
msgstr "团队成员"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_member_action
msgid "Team Members are salespersons assigned to specific teams."
msgstr "团队成员是分配给特定团队的销售人员"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_pipeline
msgid "Teams"
msgstr "团队"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__color
msgid "The color of the channel"
msgstr "渠道的颜色"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_member__company_id
msgid "The default company for this user."
msgstr "这个用户的默认公司。"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.mail_activity_type_action_config_sales
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Prepare meeting\")."
msgstr "这些代表了您必须做的不同类别的事情（例如 \"打电话 \"或 \"准备会议\"）。"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid ""
"To add a Salesperson into multiple Teams, activate the Multi-Team option in "
"settings."
msgstr "要将一个销售人员加入多个团队，请在设置中激活多团队选项。"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor6
msgid "Training"
msgstr "培训"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_member__user_in_teams_ids
msgid ""
"UX: Give users not to add in the currently chosen team to avoid duplicates"
msgstr "用户体验：让用户不要在当前选择的团队中添加，以避免重复。"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__member_company_ids
#: model:ir.model.fields,help:sales_team.field_crm_team_member__user_company_ids
msgid "UX: Limit to team company or all if no company"
msgstr "用户体验：仅限于团队公司，如果没有公司，则仅限于所有公司"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Undefined graph model for Sales Team: %s"
msgstr "销售团队的不确定图模型：%s"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_config
msgid ""
"Use Sales Teams to organize your sales departments and draw up reports."
msgstr "使用销售团队来组织您的销售部门并起草报告。"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_pipeline
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_sales
msgid ""
"Use Sales Teams to organize your sales departments.\n"
"                Each team will work with a separate pipeline."
msgstr ""
"使用销售团队来组织您的销售部门。\n"
"                每个团队将用一个单独的漏斗工作。"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_crm_tag_action
msgid ""
"Use Tags to manage and track your Opportunities (product structure, sales "
"type, ...)"
msgstr "使用标签来管理和跟踪您的机会（产品结构，销售类型，...）。"

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_users
msgid "User"
msgstr "用户"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_company_ids
msgid "User Company"
msgstr "用户公司"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_in_teams_ids
msgid "User In Teams"
msgstr "团队中的用户"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_res_users__sale_team_id
msgid "User Sales Team"
msgstr "用户销售团队"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman_all_leads
msgid "User: All Documents"
msgstr "用户: 所有单据"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman
msgid "User: Own Documents Only"
msgstr "用户: 仅自己的单据"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__member_ids
msgid "Users assigned to this team."
msgstr "分配给这个团队的用户。"

#. module: sales_team
#: model:crm.team,name:sales_team.salesteam_website_sales
msgid "Website"
msgstr "网站"

#. module: sales_team
#: code:addons/sales_team/models/crm_team_member.py:0
#, python-format
msgid ""
"You are trying to create duplicate membership(s). We found that "
"%(duplicates)s already exist(s)."
msgstr "您试图创建重复的会员资格。我们发现%(duplicates)s已经存在。"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "e.g. North America"
msgstr "例如：北美洲"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_form
msgid "e.g. Services"
msgstr "例如，服务"

#. module: sales_team
#: model:crm.team,name:sales_team.ebay_sales_team
msgid "eBay"
msgstr "eBay"

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman_all_leads
msgid ""
"the user will have access to all records of everyone in the sales "
"application."
msgstr "用户可访问销售系统中每个人的所有记录。"

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman
msgid "the user will have access to his own data in the sales application."
msgstr "在销售应用，用户只能访问自己的数据。"

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_manager
msgid ""
"the user will have an access to the sales configuration as well as statistic"
" reports."
msgstr "用户将有权访问销售配置及统计报告。"
