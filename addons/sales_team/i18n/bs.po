# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sales_team
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "<span>New</span>"
msgstr "<span>Novi</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Izvještavanje</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "<span>View</span>"
msgstr "<span>Pogledaj</span>"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__active
msgid "Active"
msgstr "Aktivan"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.mail_activity_type_action_config_sales
msgid "Activity Types"
msgstr "Tipovi aktivnosti"

#. module: sales_team
#: model:crm.team,name:sales_team.crm_team_1
msgid "America"
msgstr ""

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Archived"
msgstr "Arhivirano"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Avatar"
msgstr ""

#. module: sales_team
#: selection:crm.team,dashboard_graph_type:0
msgid "Bar"
msgstr ""

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:274
#, python-format
msgid "Big Pretty Button :)"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_ids
msgid "Channel Members"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__color
msgid "Color Index"
msgstr "Indeks boje"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__company_id
msgid "Company"
msgstr "Kompanija"

#. module: sales_team
#: model:ir.ui.menu,name:sales_team.menu_sale_config
msgid "Configuration"
msgstr "Konfiguracija"

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_model
msgid "Content"
msgstr "Sadržaj"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Dashboard"
msgstr "Kontrolna ploča"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_button_name
msgid "Dashboard Button"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_data
msgid "Dashboard Graph Data"
msgstr ""

#. module: sales_team
#: selection:crm.team,dashboard_graph_group:0
msgid "Day"
msgstr "Dan"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_salesteams_act
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_config_action
msgid "Define a new Sales Team"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: sales_team
#: model:crm.team,name:sales_team.team_sales_department
msgid "Europe"
msgstr "Europa"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__favorite_user_ids
msgid "Favorite Members"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_favorite
msgid ""
"Favorite teams to display them in the dashboard and access them easily."
msgstr ""

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid ""
"Follow this salesteam to automatically track the events associated to users "
"of this team."
msgstr ""
"Prati ovaj prodajni tim kako bih automatski pratili događaje povezane sa "
"članovima tima."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Graph"
msgstr "Dijagram"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Group By..."
msgstr "Grupiši po..."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_group
msgid "Group by"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__dashboard_graph_group
msgid "How this channel's dashboard graph will group the results."
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__id
msgid "ID"
msgstr "ID"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_partner__team_id
msgid ""
"If set, this Sales Team will be used for sales and assignations related to "
"this partner"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__active
msgid ""
"If the active field is set to false, it will allow you to hide the Sales "
"Team without removing it."
msgstr ""

#. module: sales_team
#: selection:crm.team,dashboard_graph_model:0
msgid "Invoices"
msgstr "Fakture"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: sales_team
#: selection:crm.team,dashboard_graph_period:0
msgid "Last Month"
msgstr "Zadnji mjesec"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: sales_team
#: selection:crm.team,dashboard_graph_period:0
msgid "Last Week"
msgstr "Zadnja sedmica"

#. module: sales_team
#: selection:crm.team,dashboard_graph_period:0
msgid "Last Year"
msgstr ""

#. module: sales_team
#: selection:crm.team,dashboard_graph_type:0
msgid "Line"
msgstr "Stavka"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_manager
msgid "Manager"
msgstr "Upravitelj"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: sales_team
#: selection:crm.team,dashboard_graph_group:0
msgid "Month"
msgstr "Mjesec"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "My Favorites"
msgstr ""

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:260
#, python-format
msgid "Not Defined"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: sales_team
#: selection:crm.team,dashboard_graph_model:0
msgid "Pipeline"
msgstr "Prodajni ljevak"

#. module: sales_team
#: selection:crm.team,team_type:0
msgid "Point of Sale"
msgstr "POS Kasa"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__reply_to
msgid "Reply-To"
msgstr "Odgovori na"

#. module: sales_team
#: selection:crm.team,dashboard_graph_model:0 selection:crm.team,team_type:0
msgid "Sales"
msgstr "Prodaja"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team
msgid "Sales Channels"
msgstr "Prodajni kanali"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__name
#: model:ir.model.fields,field_description:sales_team.field_res_partner__team_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_tree
msgid "Sales Team"
msgstr "Prodajni tim"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_users__sale_team_id
msgid ""
"Sales Team the user is member of. Used to compute the members of a Sales "
"Team through the inverse one2many"
msgstr ""

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.sales_team_config_action
msgid "Sales Teams"
msgstr "Prodajni timovi"

#. module: sales_team
#: selection:crm.team,dashboard_graph_group:0
msgid "Salesperson"
msgstr "Prodavač(ica)"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Salesteam name..."
msgstr "Ime prodajnog tima..."

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Salesteams Search"
msgstr "Pretraga prodajnih timova"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_period
msgid "Scale"
msgstr "Vaga"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "Settings"
msgstr "Postavke"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_favorite
msgid "Show on dashboard"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__user_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Team Leader"
msgstr "Vođa tima"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Team Members"
msgstr "Članovi tima"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_salesteams_act
msgid "Team Pipelines"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__team_type
msgid "Team Type"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__color
msgid "The color of the channel"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__reply_to
msgid ""
"The email address put in the 'Reply-To' of all emails sent by Odoo about "
"cases in this Sales Team"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__dashboard_graph_model
msgid "The graph this channel will display in the Dashboard.\n"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__dashboard_graph_period
msgid "The time period this channel's dashboard graph will consider."
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__dashboard_graph_type
msgid "The type of graph this channel will display in the dashboard."
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__team_type
msgid ""
"The type of this channel, it will define the resources this channel uses."
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_type
msgid "Type"
msgstr "Tip"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:151
#: code:addons/sales_team/models/crm_team.py:178
#, python-format
msgid "Undefined graph model for Sales Team: %s"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_salesteams_act
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_config_action
msgid ""
"Use Sales Teams to organize your sales departments.\n"
"                    Each channel will work with a separate pipeline."
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_res_users__sale_team_id
msgid "User's Sales Team"
msgstr ""

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman_all_leads
msgid "User: All Documents"
msgstr "Korisnik: Svi dokumenti"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman
msgid "User: Own Documents Only"
msgstr "Korisnik: Samo svoji dokumenti"

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: sales_team
#: model:crm.team,name:sales_team.salesteam_website_sales
#: selection:crm.team,team_type:0
msgid "Website"
msgstr "Web stranica"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: sales_team
#: selection:crm.team,dashboard_graph_group:0
msgid "Week"
msgstr "Sedmica"

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman_all_leads
msgid ""
"the user will have access to all records of everyone in the sales "
"application."
msgstr "korisnik će imati pristup svim zapisima o svima u aplikaciji prodaje."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman
msgid "the user will have access to his own data in the sales application."
msgstr "korisnik će imati pristup samo do svojih podataka u prodaji."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_manager
msgid ""
"the user will have an access to the sales configuration as well as statistic"
" reports."
msgstr ""
"korisnik će imati pristup postavkama prodaje kao i statističkim "
"izvještajima."
