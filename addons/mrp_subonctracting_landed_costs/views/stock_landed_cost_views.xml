<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id='view_mrp_landed_costs_form' model='ir.ui.view'>
        <field name="name">mrp.subcontracting.landed.cost.form</field>
        <field name="model">stock.landed.cost</field>
        <field name="inherit_id" ref="mrp_landed_costs.view_mrp_landed_costs_form"/>
        <field name="arch" type="xml">
            <field name="mrp_production_ids" position="attributes">
                <attribute name="context">{'search_view_ref': 'mrp_subcontracting.mrp_production_subcontracting_filter', 'tree_view_ref': 'mrp_subcontracting.mrp_production_subcontracting_tree_view'}</attribute>
            </field>
        </field>
    </record>
</odoo>
