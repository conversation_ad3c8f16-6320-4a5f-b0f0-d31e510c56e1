# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_mercury
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Buy a card reader"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>購買讀卡器"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"<i>Vantiv Configurations</i> define what Vantiv account will be used when\n"
"                                processing credit card transactions in the Point Of Sale. Setting up a Vantiv\n"
"                                configuration will enable you to allow payments with various credit cards\n"
"                                (eg. Visa, MasterCard, Discovery, American Express, ...). After setting up this\n"
"                                configuration you should associate it with a Point Of Sale payment method."
msgstr ""
"<i>Vantiv 設定</i>定義了在以下情況下將使用的 Vantiv 帳號\n"
"                                 在營業點處理信用卡交易. 設定 Vantiv\n"
"                                 設定將使您能夠使用各種信用卡付款\n"
"                                 (例如 Visa、MasterCard、Discovery、美國運通...). 設定好之後\n"
"                                 您應該將其與營業點付款方式相關聯的設定."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "ABOVE AMOUNT PURSUANT"
msgstr "上述數額根據"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "APPROVAL CODE:"
msgstr "批准代碼:"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_barcode_rule
msgid "Barcode Rule"
msgstr "條碼規則"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "CARDHOLDER WILL PAY CARD ISSUER"
msgstr "持卡人將支付發卡行"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_brand
msgid "Card Brand"
msgstr "信用卡品牌"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_number
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_order
msgid "Card Number"
msgstr "信用卡號"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "Card Number Prefix"
msgstr "卡號碼字首"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_owner_name
msgid "Card Owner Name"
msgstr "信用卡所有者姓名"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_tree
msgid "Card Reader"
msgstr "讀卡器"

#. module: pos_mercury
#: model_terms:ir.actions.act_window,help:pos_mercury.action_configuration_form
msgid "Configure your card reader"
msgstr "設定讀卡機"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Could not read card"
msgstr "無法讀取該卡"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_uid
msgid "Created by"
msgstr "建立者"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_date
msgid "Created on"
msgstr "建立於"

#. module: pos_mercury
#: model:ir.model.fields.selection,name:pos_mercury.selection__barcode_rule__type__credit
msgid "Credit Card"
msgstr "信用卡"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid ""
"Credit card refunds are not supported. Instead select your credit card "
"payment method, click 'Validate' and refund the original charge manually "
"through the Vantiv backend."
msgstr "不支援信用卡退款。相反, 選擇您的信用卡付款條件, 點擊 '驗證' 通過 Vantiv 後端人工退還原始費用。"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__display_name
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"For quickly handling orders: just swiping a credit card when on the payment screen\n"
"                                (without having pressed anything else) will charge the full amount of the order to\n"
"                                the card."
msgstr ""
"快速處理訂單:在支付螢幕只刷信用卡\n"
"(不按下其他任何東西)就會用卡支付訂單的總額."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/ProductScreen.js:0
#, python-format
msgid "Go to payment screen to use cards"
msgstr "前往支付螢幕以使用卡"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Handling transaction..."
msgstr "處理交易..."

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__id
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__id
msgid "ID"
msgstr "ID"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "ID of the merchant to authenticate him on the payment provider server"
msgstr "用於在支付供應商伺服器上對其進行身份驗證的商家 ID"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"If you don't already have a Vantiv account, contact Vantiv at +1 (800) 846-4472\n"
"                                to create one."
msgstr "如果您還沒有 Vantiv 帳號，請致電 +1 (800) 846-4472 聯繫 Vantiv 建立一個."

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Invoice number from Vantiv Pay"
msgstr "Vantiv Pay 的發票編號"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration____last_update
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "Merchant ID"
msgstr "商家ID"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid "Merchant Password"
msgstr "商家密碼"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__name
msgid "Name"
msgstr "名稱"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__name
msgid "Name of this Vantiv configuration"
msgstr "此 Vantiv 設定的名稱"

#. module: pos_mercury
#. odoo-python
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No Vantiv configuration associated with the payment method."
msgstr "沒有與付款方式相關的 Vantiv 設定."

#. module: pos_mercury
#. odoo-python
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No opened point of sale session for user %s found."
msgstr "找不到使用者 %s 已打開POS營業點。"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "No response from Vantiv (Vantiv down?)"
msgstr "Vantiv 沒有回應 (Vantin關機?)"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "No response from server (connected to network?)"
msgstr "伺服器沒有回應(網路已連接?)"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Odoo error while processing transaction."
msgstr "處理交易時出現Odoo錯誤。"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Ok"
msgstr "確定"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Online Payment"
msgstr "線上支付"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Partially approved"
msgstr "部分已批准"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid ""
"Password of the merchant to authenticate him on the payment provider server"
msgstr "用於在支付提供商伺服器上驗證身份的商家密碼"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Pay with: "
msgstr "支付方式: "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_record_no
msgid "Payment record number from Vantiv Pay"
msgstr "Vantiv Pay 的付款記錄編號"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Payment reference number from Vantiv Pay"
msgstr "Vantiv Pay 的付款參考號"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Please setup your Vantiv merchant account."
msgstr "請設定您的 Vantiv 商家帳號."

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_order
msgid "Point of Sale Orders"
msgstr "POS訂單"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "POS付款條件"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment
msgid "Point of Sale Payments"
msgstr "POS付款"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_session
msgid "Point of Sale Session"
msgstr "POS營業點"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_configuration
msgid "Point of Sale Vantiv Configuration"
msgstr "營業點 Vantiv 設定"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_mercury_transaction
msgid "Point of Sale Vantiv Transaction"
msgstr "營業點 Vantiv 交易"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Refunds not supported"
msgstr "不支援退款"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Reversal failed, sending VoidSale..."
msgstr "逆轉失敗，發送無效銷售..."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Reversal succeeded"
msgstr "逆轉成功"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Sending reversal..."
msgstr "逆轉發送..."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "TO CARDHOLDER AGREEMENT"
msgstr "持卡人協議"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_brand
msgid "The brand of the payment card (e.g. Visa, AMEX, ...)"
msgstr "付款卡品牌（例：Visa、美國運通⋯）"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "The card number used for the payment."
msgstr "用來付款的卡號。"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "The configuration of Vantiv used for this journal"
msgstr "此日記帳使用Vantiv的設定"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_number
msgid "The last 4 numbers of the card used to pay"
msgstr "用來付款的卡之最後4位號碼"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid ""
"This can be caused by a badly executed swipe or by not having your keyboard "
"layout set to US QWERTY (not US International)."
msgstr "可能是由於嚴重的刷卡錯誤或者您的鍵盤布局沒有被設定為美國標準US QWERTY(非美國國際標準)。"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Transaction approved"
msgstr "交易已批准"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_barcode_rule__type
msgid "Type"
msgstr "類型"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"Using the Vantiv integration in the Point Of Sale is easy: just press the\n"
"                                associated payment method. After that the amount can be adjusted (eg. for cashback)\n"
"                                just like on any other payment line. Whenever the payment line is set up, a card\n"
"                                can be swiped through the card reader device."
msgstr ""
"在營業點中使用 Vantiv 整合很容易:\n"
"                                 只需按相關支付方式。之後可以調整金額(例如現金返還)\n"
"                                 就像在任何其他付款線上一樣。每當設定支付明細時，\n"
"                                 一張卡可以通過讀卡器設備刷卡."

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "Vantiv Accounts"
msgstr "Vantiv 帳號"

#. module: pos_mercury
#: model:ir.actions.act_window,name:pos_mercury.action_configuration_form
#: model:ir.ui.menu,name:pos_mercury.menu_pos_pos_mercury_config
msgid "Vantiv Configurations"
msgstr "Vantiv 設定"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "Vantiv Credentials"
msgstr "Vantiv 憑證"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Vantiv invoice number"
msgstr "Vantiv 發票編號"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_record_no
msgid "Vantiv record number"
msgstr "Vantiv 紀錄編號"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Vantiv reference number"
msgstr "Vantiv 參考編號"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "VoidSale succeeded"
msgstr "無效銷售成功"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/xml/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "WAITING FOR SWIPE"
msgstr "等待刷卡"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"We currently support the MagTek Dynamag card reader device. It can be connected\n"
"                                directly to the Point Of Sale device or it can be connected to the IoTBox."
msgstr ""
"我們目前支援 MagTek Dynamag 讀卡器設備。它可以\n"
"                                 直接連接到POS營業點設備，也可以連接到 IoTBox."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "X______________________________"
msgstr "X______________________________"
