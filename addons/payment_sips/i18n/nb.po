# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# <PERSON>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON>, 2019\n"
"Language-Team: Norwegian <PERSON> (https://www.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; flere ordrer funnet"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr "; ingen ordre funnet"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Currency not supported by Wordline"
msgstr "Valuta ikke støttet av Wordline"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Incorrect payment acquirer provider"
msgstr "Tilbyder av betalingsløsning er ikke korrekt"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_version
msgid "Interface Version"
msgstr "Grensesnittversjon"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Merchant ID"
msgstr "Merchant ID"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Betalingsløsning"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalingstransaksjon"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_prod_url
msgid "Production url"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__provider
msgid "Provider"
msgstr "Tilbyder"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_secret
msgid "Secret Key"
msgstr "Hemmelig nøkkel"

#. module: payment_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_acquirer__provider__sips
msgid "Sips"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Sips: received data for reference %s"
msgstr "Sips: Mottatt data for referanse %s"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_test_url
msgid "Test url"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Used for production only"
msgstr "Bare brukt i produksjon"
