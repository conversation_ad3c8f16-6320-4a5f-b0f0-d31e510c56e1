# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# <PERSON>, 2019
# se<PERSON> huang <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2019\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; 找到多個訂單"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr "; 沒有訂單"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Currency not supported by Wordline"
msgstr "Wordline不支援此貨幣"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Incorrect payment acquirer provider"
msgstr "不正確的付款收單服務商"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_version
msgid "Interface Version"
msgstr "接口版本"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Merchant ID"
msgstr "商戶ID"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "付款收單方"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_prod_url
msgid "Production url"
msgstr "製作網址"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__provider
msgid "Provider"
msgstr "服務商"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_secret
msgid "Secret Key"
msgstr "密鑰"

#. module: payment_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_acquirer__provider__sips
msgid "Sips"
msgstr "Sips"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Sips: received data for reference %s"
msgstr "Sips: 供參考的接收數據 %s"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_test_url
msgid "Test url"
msgstr "測試網址"

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Used for production only"
msgstr "僅適用於生產"
