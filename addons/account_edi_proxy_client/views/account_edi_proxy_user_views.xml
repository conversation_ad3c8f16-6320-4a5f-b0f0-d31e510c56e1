<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_form_account_edi_proxy_client_user" model="ir.ui.view">
            <field name="name">EDI Proxy User</field>
            <field name="model">account_edi_proxy_client.user</field>
            <field name="arch" type="xml">
                <form string="Account Journal">
                    <sheet>
                        <group>
                            <group>
                                <field name="company_id" groups="base.group_multi_company" invisible="1"/>
                                <field name="edi_format_id" readonly="1" options="{'no_open': True}"/>
                                <field name="id_client" readonly="1"/>
                                <field name="edi_identification" readonly="1"/>
                                <field name="private_key_filename" invisible="1"/>
                                <field name="private_key" widget="binary" filename="private_key_filename" readonly="1"/>
                                <field name="refresh_token" readonly="1"/>
                            </group>
                            <group/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_tree_account_edi_proxy_client_user" model="ir.ui.view">
            <field name="name">EDI Proxy Users</field>
            <field name="model">account_edi_proxy_client.user</field>
            <field name="arch" type="xml">
                <tree create="false" delete="false" edit="false">
                    <field name="company_id" groups="base.group_multi_company" invisible="1"/>
                    <field name="edi_format_id" readonly="1"/>
                    <field name="id_client" readonly="1"/>
                    <field name="edi_identification" readonly="1"/>
                    <field name="private_key_filename" invisible="1"/>
                    <field name="private_key" widget="binary" filename="private_key_filename" readonly="1"/>
                    <field name="refresh_token" readonly="1"/>
                </tree>
            </field>
        </record>

        <record id="action_tree_account_edi_proxy_client_user" model="ir.actions.act_window">
            <field name="name">EDI Proxy User</field>
            <field name="res_model">account_edi_proxy_client.user</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_tree_account_edi_proxy_client_user"/>
        </record>

        <menuitem name="EDI Proxy Users"
                  sequence="2"
                  parent="account.menu_finance_configuration"
                  id="menu_account_proxy_client_user"
                  action="action_tree_account_edi_proxy_client_user"
                  groups="base.group_no_one"/>

    </data>
</odoo>
