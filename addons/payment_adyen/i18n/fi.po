# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_adyen
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>arm<PERSON>tjärvi <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-08-02 09:56+0000\n"
"PO-Revision-Date: 2018-08-02 09:56+0000\n"
"Last-Translator: <PERSON>arm<PERSON> Ko<PERSON>tjärvi <<EMAIL>>, 2018\n"
"Language-Team: Finnish (https://www.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:187
#, python-format
msgid "; multiple order found"
msgstr "; useita tilauksia löytyi"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:185
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:229
#, python-format
msgid "Adyen: feedback error"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:197
#, python-format
msgid "Adyen: invalid merchantSig, received %s, computed %s"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:183
#, python-format
msgid "Adyen: received data for reference %s"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:176
#, python-format
msgid ""
"Adyen: received data with missing reference (%s) or missing pspReference "
"(%s)"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.acquirer_form_adyen
msgid "How to configure your Adyen account?"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_merchant_account
msgid "Merchant Account"
msgstr "Kauppiaan tili"

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Maksun vastaanottaja"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr "Maksutapahtuma"

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__provider
msgid "Provider"
msgstr "Palveluntarjoaja"

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_skin_code
msgid "Skin Code"
msgstr "Käyttöliittymäpohjan koodi"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_skin_hmac_key
msgid "Skin HMAC Key"
msgstr "Käyttöliittymäpohjan HMAC-avain"

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr "Tilisiirto"
