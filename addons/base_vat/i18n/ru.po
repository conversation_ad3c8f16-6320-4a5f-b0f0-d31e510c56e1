# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
# 
# Translators:
# Сергей <PERSON>е<PERSON>н<PERSON>н <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:27+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"10XXXXXXXXY or 20XXXXXXXXY or 15XXXXXXXXY or 16XXXXXXXXY or 17XXXXXXXXY"
msgstr ""
"10XXXXXXXXY или 20XXXXXXXXY или 15XXXXXXXXY или 16XXXXXXXXY или 17XXXXXXXXY"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "17291716060 (NIN) or 1729171602 (VKN)"
msgstr "17291716060 (ИНН) или 1729171602 (КПП)"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "1792060346001 or 1792060346"
msgstr "1792060346001 или 1792060346"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "3101012009"
msgstr "3101012009"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "310175397400003 [Fifteen digits, first and last digits should be \"3\"]"
msgstr ""
"310175397400003 [Пятнадцать цифр, первая и последняя цифры должны быть «3»]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "49-098-576 or 49098576"
msgstr "49-098-576 или 49098576"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Значения настраиваются "
"отдельно для каждой компании.\" aria-label=\"Значения настраиваются отдельно"
" для каждой компании.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "AR200-5536168-2 or 20055361682"
msgstr "AR200-5536168-2 или 20055361682"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "CHE-123.456.788 TVA or CHE-123.456.788 MWST or CHE-123.456.788 IVA"
msgstr "CHE-123.456.788 TVA или CHE-123.456.788 MWST или CHE-123.456.788 IVA"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "CO213123432-1 or CO213.123.432-1"
msgstr "CO213123432-1 или CO213.123.432-1"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "DE********8 or 12/345/67890"
msgstr "DE********8 или 12/345/67890"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "DO1-01-85004-3 or *********"
msgstr "DO1-01-85004-3 или *********"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"Example: '************' (format: 12 digits, all numbers, valid check digit)"
msgstr "Пример: «************» (12 цифр, только числа, с проверочной цифрой)"

#. module: base_vat
#: model:ir.model,name:base_vat.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Система налогов"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "GB********2 or XI********2"
msgstr "GB********2 или XI********2"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "HU12345676 or ********-1-11 or **********"
msgstr "HU12345676 или ********-1-11 или **********"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, you will not be able to save a contact if its "
"VAT number cannot be verified by the European VIES service."
msgstr ""
"Если вы отметили этот пункт, вы не сможете сохранить данные о контакте, если"
" ИНН не может быть проверен с помощью Европейского сервиса VES."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "MXGODE561231GR8 or GODE561231GR8"
msgstr "MXGODE561231GR8 или GODE561231GR8"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_failed_message
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_failed_message
msgid "Technical field display a message to the user if the VIES check fails."
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"Число %(vat_label)s [%(wrong_vat)s], по-видимому, не является действительным.\n"
"Примечание: ожидаемый формат - %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(record_label)s does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"Число %(vat_label)s [%(wrong_vat)s] для %(record_label)s, похоже, не подходит.\n"
"Примечание: ожидаемый формат - %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "The VAT number %s failed the VIES VAT validation check."
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid ""
"The country detected for this foreign VAT number does not match any of the "
"countries composing the country group set on this fiscal position."
msgstr ""
"Страна, связанная с этим иностранным номером НДС, не входит в группу стран, "
"указанную в данной налоговой позиции."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid ""
"The country of the foreign VAT number could not be detected. Please assign a"
" country to the fiscal position or set a country group"
msgstr ""
"Не удалось определить страну иностранного номера НДС. Укажите страну или "
"группу стран в налоговой позиции."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "VAT"
msgstr "НДС"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "Проверка ИНН"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "Проверка ИНН с помощью Европейского сервиса VES"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "XXXXXXXXX [9 digits] and it should respect the Luhn algorithm checksum"
msgstr ""
"XXXXXXXXX [9 цифр], должен соответствовать контрольной сумме по алгоритму "
"Луна."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "either 11 digits for CPF or 14 digits for CNPJ"
msgstr "Либо 11 цифр для CPF, либо 14 цифр для CNPJ"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid "fiscal position [%s]"
msgstr "налоговая позиция [%s]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "partner [%s]"
msgstr "партнер [%s]"
