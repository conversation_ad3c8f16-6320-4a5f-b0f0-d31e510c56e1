.s_showcase[data-vcss='001'] {
    @include media-breakpoint-up(lg) {
        // Left-right separator
        .container, .container-fluid {
            position: relative;

            &:before {
                content: " ";
                display: block;
                @include o-position-absolute($left: 50%);
                height: 100%;
                border-right: 1px solid map-get($grays, '200');
            }
        }
        // Features
        .row > div {
            // Items on left
            &:nth-child(odd) {
                text-align: right;

                .s_showcase_icon, p {
                    float: right;
                }

                .s_showcase_icon {
                    margin-right: 0;
                    margin-left: 15px;
                }
            }
            // Items on right
            &:nth-child(even) {
                text-align: left;

                .s_showcase_icon, p {
                    float: left;
                }
            }
        }
    }

    .s_showcase_icon {
        // Make the default margin the one for the left aligned icon, as it's what we want on mobile
        margin-right: 15px;
        font-size: 36px;
    }
}
