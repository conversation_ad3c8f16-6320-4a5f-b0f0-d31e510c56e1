# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_mrp
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.purchase_order_form_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">Fertigung</span>"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.mrp_production_form_view_purchase
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">Einkäufe</span>"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Stücklistenübersichtsbericht"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_bom
msgid "Bill of Material"
msgstr "Stückliste"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Stücklistenposition"

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/mrp_bom.py:0
#, python-format
msgid "Components cost share have to be positive or equals to zero."
msgstr "Der Kostenanteil der Komponenten muss positiv oder gleich Null sein."

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_bom_line__cost_share
msgid "Cost Share (%)"
msgstr "Kostenanteil (%)"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_purchase_order__mrp_production_count
msgid "Count of MO Source"
msgstr "Anzahl der FA-Referenzen"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_production__purchase_order_count
msgid "Count of generated PO"
msgstr "Anzahl generierter Bestellungen"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_account_move_line
msgid "Journal Item"
msgstr "Buchungszeile"

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/purchase.py:0
#, python-format
msgid "Manufacturing Source of %s"
msgstr "Fertigungsquelle von %s"

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/stock_move.py:0
#, python-format
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr ""
"Odoo ist nicht in der Lage, die angelsächsischen Einträge zu generieren. Die"
" Gesamtbewertung von %s ist Null."

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_production
msgid "Production Order"
msgstr "Fertigungsauftrag"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order
msgid "Purchase Order"
msgstr "Bestellung"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Bestellzeile"

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/mrp_production.py:0
#, python-format
msgid "Purchase Order generated from %s"
msgstr "Bestellung generiert aus %s"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_stock_move
msgid "Stock Move"
msgstr "Lagerbuchung"

#. module: purchase_mrp
#: model:ir.model.fields,help:purchase_mrp.field_mrp_bom_line__cost_share
msgid ""
"The percentage of the component repartition cost when purchasing a kit.The "
"total of all components' cost have to be equal to 100."
msgstr ""
"Der prozentuale Anteil der Kosten für die Komponenten beim Kauf eines "
"Bausatzes, wobei die Gesamtkosten für alle Komponenten gleich 100 sein "
"müssen."

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/mrp_bom.py:0
#, python-format
msgid "The total cost share for a BoM's component have to be 100"
msgstr ""
"Der Gesamtkostenanteil für die Komponenten einer Stückliste muss 100 sein."
