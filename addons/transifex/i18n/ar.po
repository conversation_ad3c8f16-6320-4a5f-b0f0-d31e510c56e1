# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* transifex
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-23 14:34+0000\n"
"PO-Revision-Date: 2023-04-14 06:18+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: transifex
#: model:ir.model,name:transifex.model_base
msgid "Base"
msgstr "قاعدة "

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__source
msgid "Code"
msgstr "رمز "

#. module: transifex
#: model:ir.model,name:transifex.model_transifex_code_translation
msgid "Code Translation"
msgstr "ترجمة الكود "

#. module: transifex
#. odoo-javascript
#: code:addons/transifex/static/src/views/fields/translation_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_tree_view
#, python-format
msgid "Contribute"
msgstr "المساهمة "

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__id
msgid "ID"
msgstr "المُعرف"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__lang
msgid "Language"
msgstr "اللغة"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__module
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Module"
msgstr "التطبيق "

#. module: transifex
#: model:ir.model.fields,help:transifex.field_transifex_code_translation__module
msgid "Module this term belongs to"
msgstr "التطبيق الذي ينتمي إليه هذا الشرط "

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Not Translated"
msgstr "غير مترجم "

#. module: transifex
#: model:ir.model.fields,help:transifex.field_transifex_code_translation__transifex_url
msgid "Propose a modification in the official version of Odoo"
msgstr "اقتراح تعديل في النسخة الرسمية لأودو "

#. module: transifex
#. odoo-javascript
#: code:addons/transifex/static/src/views/reload_code_translations_views.xml:0
#, python-format
msgid "Reload"
msgstr "إعادة تحميل"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Search Code Translations"
msgstr "البحث في ترجمات الكود "

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_tree_view
msgid "Transifex"
msgstr "Transifex"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_tree_view
msgid "Transifex Code Translation"
msgstr "ترجمات كود Transifex "

#. module: transifex
#: model:ir.actions.server,name:transifex.action_code_translations
#: model:ir.ui.menu,name:transifex.menu_transifex_code_translations
msgid "Transifex Code Translations"
msgstr "ترجمات كود Transifex "

#. module: transifex
#: model:ir.model,name:transifex.model_transifex_translation
msgid "Transifex Translation"
msgstr "ترجمة Transifex "

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__transifex_url
msgid "Transifex URL"
msgstr "رابط Transifex"

#. module: transifex
#: model:ir.actions.server,name:transifex.transifex_code_translation_reload_ir_actions_server
#: model:ir.cron,cron_name:transifex.transifex_code_translation_reload
msgid "Transifex: Reload code translations"
msgstr "Transifex: إعادة تحميل ترجمات الكود "

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__value
msgid "Translation Value"
msgstr "قيمة الترجمة"
