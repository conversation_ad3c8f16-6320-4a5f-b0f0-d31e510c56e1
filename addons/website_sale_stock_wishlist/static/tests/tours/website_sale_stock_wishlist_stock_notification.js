/** @odoo-module **/

import tour from 'web_tour.tour';

tour.register('stock_notification_wishlist', {
        test: true,
        url: '/shop/wishlist',
    },
    [
        {
            content: "Click on 'Be notified when back in stock'",
            trigger: '#wishlist_stock_notification_message',
        },
        {
            content: "Fill email form",
            trigger: 'div[id="stock_notification_form"] input[name="email"]',
            run: 'text <EMAIL>',
        },
        {
            content: "Click on the button",
            trigger: '#wishlist_stock_notification_form_submit_button',
        },
        {
            content: "Success Message",
            trigger: '#stock_notification_success_message',
        },
    ],
);
