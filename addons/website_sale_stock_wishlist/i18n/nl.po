# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock_wishlist
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<i class=\"fa fa-bell\"/>\n"
"                        We'll notify you once the product is back in stock."
msgstr ""
"<i class=\"fa fa-bell\"/>\n"
"                        We laten het je weten zodra het product weer op voorraad is."

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<i class=\"fa fa-times text-danger\"/>\n"
"                                Invalid email"
msgstr ""
"<i class=\"fa fa-times text-danger\"/>\n"
"                                Ongeldig e-mailadres"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<small>\n"
"                            <i class=\"fa fa-envelope-o\"/>\n"
"                            Get notified when back in stock\n"
"                        </small>"
msgstr ""
"<small>\n"
"                            <i class=\"fa fa-envelope-o\"/>\n"
"                            Krijg een melding zodra het product weer op voorraad is\n"
"                        </small>"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"Add\n"
"                <span class=\"d-none d-md-inline\">to Cart</span>"
msgstr ""
"Toevoegen\n"
"                <span class=\"d-none d-md-inline\">aan winkelmandje</span>"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
#, python-format
msgid "Add to wishlist"
msgstr "Toevoegen aan verlanglijst"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
#, python-format
msgid "Added to your wishlist"
msgstr "Toegevoegd aan je verlanglijst"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_template
msgid "Product"
msgstr "Product"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_wishlist
msgid "Product Wishlist"
msgstr "Product verlanglijst"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
#, python-format
msgid "Save for later"
msgstr "Bewaren voor later"

#. module: website_sale_stock_wishlist
#: model:ir.model.fields,field_description:website_sale_stock_wishlist.field_product_wishlist__stock_notification
msgid "Stock Notification"
msgstr "Voorraadmelding:"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "Temporarily out of stock"
msgstr "Tijdelijk niet op voorraad"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "<EMAIL>"
msgstr "<EMAIL>"
