# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_oauth
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# Lux <PERSON>k <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:26+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Lux Sok <<EMAIL>>, 2023\n"
"Language-Team: Khmer (https://app.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.providers
msgid "- or -"
msgstr "- ឬ -"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Tutorial"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>ការបង្រៀន"

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid "Access Denied"
msgstr "មិនមានសិទ្ធិចូលប្រើ"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__auth_oauth_google_enabled
msgid "Allow users to sign in with Google"
msgstr "អនុញ្ញាតឱ្យអ្នកប្រើចូលជាមួយ Google"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Allow users to sign in with their Google account"
msgstr "អនុញ្ញាតឱ្យអ្នកប្រើចូលជាមួយគណនី Google របស់ពួកគេ"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__enabled
msgid "Allowed"
msgstr "ការអនុញាត"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__auth_endpoint
msgid "Authorization URL"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__css_class
msgid "CSS class"
msgstr "ថ្នាក់ CSS "

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__client_id
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__auth_oauth_google_client_id
msgid "Client ID"
msgstr "អត្តលេខរបស់អតិថិជន"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Client ID:"
msgstr "អត្តលេខរបស់អតិថិជន"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_config_settings
msgid "Config Settings"
msgstr "កំណត់រចនាសម្ព័ន្ធ"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__data_endpoint
msgid "Data Endpoint"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Documentation"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Google Authentication"
msgstr "ការសម្គាល់ Google"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__id
msgid "ID"
msgstr "អត្តសញ្ញាណ"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_auth_oauth_provider__body
msgid "Link text in Login Dialog"
msgstr "ភ្ជាប់អត្ថបទនៅក្នុងប្រអប់ចូលសន្ទនា "

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_facebook
msgid "Log in with Facebook"
msgstr "ការចូលទៅក្នុងបណ្តាញសង្គម"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_google
msgid "Log in with Google"
msgstr "ចូលជាមួយ Google"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_openerp
msgid "Log in with Odoo.com"
msgstr "ចូលជាមួយ Odoo.com"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__body
msgid "Login button label"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_access_token
msgid "OAuth Access Token"
msgstr "OAuth ដំណើរការរបស់ Token"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_provider_id
msgid "OAuth Provider"
msgstr "ដំណើរការរបស់ OAuth "

#. module: auth_oauth
#: model:ir.ui.menu,name:auth_oauth.menu_oauth_providers
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "OAuth Providers"
msgstr "ការផ្តល់ជូន OAuth "

#. module: auth_oauth
#: model:ir.model.constraint,message:auth_oauth.constraint_res_users_uniq_users_oauth_provider_oauth_uid
msgid "OAuth UID must be unique per provider"
msgstr "OAuth UID ត្រូវតែមានតែមួយសម្រាប់អ្នកផ្តល់សេវាកម្ម"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_uid
msgid "OAuth User ID"
msgstr "លេខសម្គាល់អ្នកប្រើ OAuth"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr "ក្រុមហ៊ុន OAuth2"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_users_form
msgid "Oauth"
msgstr "Oauth"

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users__oauth_uid
msgid "Oauth Provider user_id"
msgstr "អ្នកផ្ដល់ Oauth អ្នកប្រើ _id"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__name
msgid "Provider name"
msgstr "ឈ្មោះក្រុមហ៊ុនផ្ដល់"

#. module: auth_oauth
#: model:ir.actions.act_window,name:auth_oauth.action_oauth_provider
msgid "Providers"
msgstr "ក្រុមហ៊ុនផ្តល់ជូន"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__scope
msgid "Scope"
msgstr "វិសាលភាព"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__sequence
msgid "Sequence"
msgstr "លំដាប់"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__server_uri_google
msgid "Server uri"
msgstr "ម៉ាស៊ីនបម្រើ uri"

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid "Sign up is not allowed on this database."
msgstr "ចុះឈ្មោះមិនត្រូវបានអនុញ្ញាតនៅលើមូលដ្ឋានទិន្នន័យនេះទេ។"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_ir_config_parameter
msgid "System Parameter"
msgstr "ប៉ារ៉ាម៉ែត្រប្រព័ន្ធ"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_users
msgid "User"
msgstr "អ្នកប្រើប្រាស់"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__validation_endpoint
msgid "UserInfo URL"
msgstr ""

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid ""
"You do not have access to this database or your invitation has expired. "
"Please ask for an invitation and be sure to follow the link in your "
"invitation email."
msgstr ""
"អ្នកមិនមានសិទ្ធិចូលក្នុងមូលដ្ឋានទិន្នន័យនេះទេឬការអញ្ជើញរបស់អ្នកបានផុតកំណត់។ "
"សូមស្នើសុំការអញ្ជើញហើយត្រូវប្រាកដថាតំណតាមអ៊ីម៉ែលអញ្ជើញរបស់អ្នក"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_form
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_tree
msgid "arch"
msgstr "មូល"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "e.g. 1234-xyz.apps.googleusercontent.com"
msgstr "ឧ។ 1234-xyz.apps.googleusercontent.com"
