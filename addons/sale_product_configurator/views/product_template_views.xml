<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="product_template_view_form" model="ir.ui.view">
        <field name="name">product.template.form.inherit.sale.product.configurator</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <group name="upsell" position="attributes">
                <attribute name="invisible">0</attribute>
            </group>
            <group name="upsell" position="inside">
                <field name="optional_product_ids"
                    widget="many2many_tags"
                    options="{'color_field': 'color'}"
                    domain="[('id', '!=', active_id), '|', ('company_id', '=', company_id), ('company_id', '=', False)]"
                    placeholder="Recommend when 'Adding to Cart' or quotation"/>
            </group>
        </field>
    </record>

</odoo>
