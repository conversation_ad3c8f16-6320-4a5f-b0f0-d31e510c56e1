# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>n Erdoğ<PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 20:35+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Tugay Hatıl <<EMAIL>>, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "%d records successfully imported"
msgstr "%d kayıt başarıyla içe aktarıldı"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect"
msgstr ""
"Dosyada tek bir sütun bulunmuştur, bu ayırıcının yanlış olduğu anlamına "
"gelir"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Additional Fields"
msgstr "Ek Alanlar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Advanced"
msgstr "Gelişmiş"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Allow matching with subfields"
msgstr "Alt alanlarla eşleştirmeye izin ver"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""
"İçe aktarım sırasında bilinmeyen bir durum meydana geldi ( muhtemelen "
"bağlantı kayboldu, veri veya hafıza sınırları aşıldı). Lütfen sorunun geçici"
" olması durumunda tekrar deneyin. Sorun devam ederse, dosyayı bir kerede içe"
" aktarmak yerine bölmeyi deneyin."

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "Temel"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "İçe Aktarım"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "İçe Aktarım Haritalaması"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Batch Import"
msgstr "Toplu İçe Aktarma"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Batch limit"
msgstr "Parti limiti"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Cancel"
msgstr "İptal"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Click 'Resume' to proceed with the import, resuming at line"
msgstr ""
"Satırda devam ederek, içe aktarımı sürdürmek için 'Devam Et' e tıklayın."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values (value: %s)"
msgstr "%s.sütun yanlış değerler içeriyor (değer: %s)"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values. Error in line %d: %s"
msgstr "%s sütunu yanlış değerler içeriyor. %d satırında hata: %s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "Sütun Adı"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Comma"
msgstr "Virgül"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Comments"
msgstr "Yorumlar"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr "URL alınamadı: %(url)s [%(field_name)s: L%(line_number)d]: %(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Create new values"
msgstr "Yeni değerler oluştur"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__currency_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__currency_id
msgid "Currency"
msgstr "Para Birimi"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Database ID"
msgstr "Veritabanı ID"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Date Format:"
msgstr "Tarih Biçimi:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Datetime Format:"
msgstr "Tarih Saat Biçimi:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Decimal Separator:"
msgstr "Ondalık Ayırıcı:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Dot"
msgstr "Nokta"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Download"
msgstr "İndir"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Download Template"
msgstr "Şablonu İndir"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__dt
msgid "Dt"
msgstr "Dt"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Encoding:"
msgstr "Kodlama:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Error Parsing Date [%s:L%d]: %s"
msgstr "Tarihi ayrıştırma hatası [%s: L %d]: %s"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Error while importing records: Text Delimiter should be a single character."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Error while importing records: all rows should be of the same size, but the "
"title row has %d entries while the first row has %d. You may need to change "
"the separator character."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Estimated time left:"
msgstr "Tahmini kalan süre:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Everything seems valid."
msgstr "Her şey geçerli görünüyor"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Excel files are recommended as formatting is automatic."
msgstr "Biçimlendirme otomatik olduğu için Excel dosyaları önerilir."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "External ID"
msgstr "Harici Kimlik"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "Alan Adı"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "Dosya"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "File Column"
msgstr "Dosya Sütunu"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "Dosya Adı"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "Dosya Tipi"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "Dosya boyutu maksimum miktarı aşıyor (%s byte)"

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr ""
"Denetlenecek ve/veya içeaktarılacak dosya, ikili sıralı (base64 değil)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Finalizing current batch before interrupting..."
msgstr "Kesintiye uğramadan önce mevcut parti sonlandırılıyor..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "For CSV files, you may need to select the correct separator."
msgstr "CSV dosyaları için, doğru ayırıcıyı seçmeniz gerekebilir."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Formatting"
msgstr "Biçimlendirme"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""
"Geçersiz resim verileri bulundu, resimler URL veya base64 kodlu veri olarak "
"içe aktarılmalıdır."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Go to Import FAQ"
msgstr "İçe Aktarma SSS'sine gidin"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Help"
msgstr "Yardım"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Here is the start of the file we could not import:"
msgstr "İçe aktaramadığımız dosyanın başlangıcı:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""
"Dosya sütun adlarını içeriyorsa, Odoo sütuna karşılık gelen alanı otomatik "
"olarak algılamayı deneyebilir. Bu, özellikle dosyada çok sayıda sütun "
"olduğunda içe aktarmayı kolaylaştırır."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""
"Model openchatter kullanıyorsa, geçmiş izleme abonelikler kuracak ve içe "
"aktarma sırasında bildirim gönderecek, ancak daha yavaş bir içe aktarmaya "
"yol açacaktır."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""
"Görüntü boyutu çok yüksek, içe aktarılan görüntüler 42 milyon pikselden "
"küçük olmalıdır"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Import"
msgstr "İçe Aktar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Import FAQ"
msgstr "SSS İçe Aktar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Import a File"
msgstr "Bir Dosya İçe Aktar"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Import file has no content or is corrupt"
msgstr "İçe aktarma dosyasında içerik yok veya bozuk"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Import preview failed due to:"
msgstr "İçe aktarma hatasının nedeni:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_records/import_records.xml:0
#, python-format
msgid "Import records"
msgstr "Kayıtları İçe Aktar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Imported file"
msgstr "İçe aktarılan dosya"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Importing"
msgstr "içe aktarılıyor"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Invalid cell format at row %(row)s, column %(col)s: %(cell_value)s, with "
"format: %(cell_format)s, as (%(format_type)s) formats are not supported."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr ""
"%(col)s.sütun %(row)s.satırdaki hücre değeri geçersiz : %(cell_value)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Label"
msgstr "Etiket"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Load File"
msgstr "Dosya Yükle"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Loading file..."
msgstr "Dosya yükleniyor..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
#, python-format
msgid "Model"
msgstr "Model"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Multiple errors occurred"
msgstr "Birden çok hata oluştu"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__name
#, python-format
msgid "Name"
msgstr "Adı"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Need Help?"
msgstr "Yardıma mı ihtiyacınız var?"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "No Separator"
msgstr "Ayraç Yok"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "No matching records found for"
msgstr "Eşleşen kayıt bulunamadı"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "No matching records found for the following"
msgstr "Aşağıdakiler için eşleşen kayıt bulunamadı"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Odoo Field"
msgstr "Odoo Alanı"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__othervalue
msgid "Other Variable"
msgstr "Diğer Değişken"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__parent_id
msgid "Parent"
msgstr "Üst"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Prevent import"
msgstr "Aktarmayı engelle"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Preview"
msgstr "Önizle"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Relation Fields"
msgstr "İlişkili Alanlar"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "Res Model"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Resume"
msgstr "Devam Et"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Search for a field..."
msgstr "Bir alan arayın..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "See possible values"
msgstr "Olası değerleri görün"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Selected Sheet:"
msgstr "Seçilen Sayfa:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Semicolon"
msgstr "Noktalı Virgül"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Separator:"
msgstr "Ayraç:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set to:"
msgstr "Ayarla:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set to: False"
msgstr "Ayarla: Yanlış"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set to: True"
msgstr "Ayarla: Doğru"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set value as empty"
msgstr "Değeri boş ayarla"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Sheet:"
msgstr "Çizelge"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Skip record"
msgstr "Kaydı atla"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__somevalue
msgid "Some Value"
msgstr "Birkaç Değer"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Space"
msgstr "Boşluk"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Standard Fields"
msgstr "Standart Alanlar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Start at line"
msgstr "Satırda çalıştır."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Stop Import"
msgstr "İçe Aktarmayı Durdur"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Suggested Fields"
msgstr "Önerilen Alanlar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Tab"
msgstr "Sekme"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Test"
msgstr "Test"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Testing"
msgstr "Test"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_preview
msgid "Tests : Base Import Model Preview"
msgstr "Testler: Temel İçe Aktarım Modeli Önizlemesi"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char
msgid "Tests : Base Import Model, Character"
msgstr "Testler: İçe Aktarım Modeli, Karakter"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_noreadonly
msgid "Tests : Base Import Model, Character No readonly"
msgstr "Testler: Temel İçe Aktarım Testi, Salt Okunuru Olmayan Karakter"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_readonly
msgid "Tests : Base Import Model, Character readonly"
msgstr "Testler: Temel Aktarım Modeli, Salt okunur karakter"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_required
msgid "Tests : Base Import Model, Character required"
msgstr "Testler: Temel Aktarım Modeli, Zorunlu karakter"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_states
msgid "Tests : Base Import Model, Character states"
msgstr "Testler: Temel Aktarım Modeli, Karakter durumları"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_stillreadonly
msgid "Tests : Base Import Model, Character still readonly"
msgstr "Testler: Temel Aktarım Modeli, Karakter hala salt okunur"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o
msgid "Tests : Base Import Model, Many to One"
msgstr "Testler: Temel İçe Aktarım Modeli, One to Many"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_related
msgid "Tests : Base Import Model, Many to One related"
msgstr "Testler: Temel İçe Aktarım Modeli, One to Many ile ilişkili"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_required
msgid "Tests : Base Import Model, Many to One required"
msgstr "Testler: Temel İçe Aktarım Modeli, zorunlu One to Many"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_required_related
msgid "Tests : Base Import Model, Many to One required related"
msgstr "Testler: Temel İçe Aktarım Modeli, zorunlu One to Many ile ilişkili"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_o2m
msgid "Tests : Base Import Model, One to Many"
msgstr "Testler: Temel İçe Aktarım Modeli, One to Many"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_o2m_child
msgid "Tests : Base Import Model, One to Many child"
msgstr "Testler: Temel İçe Aktarım Modeli, One to Many"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_complex
msgid "Tests: Base Import Model Complex"
msgstr "Testler: Temel İçe Aktarım Modeli Kompleks"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_float
msgid "Tests: Base Import Model Float"
msgstr "Testler: Temel İçe Aktarım Modeli Float"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Text Delimiter:"
msgstr "Metin Sınırlayıcı:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "The file contains blocking errors (see below)"
msgstr "Dosya engelleme hataları içeriyor (aşağıya bakın)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "The file contains non-blocking warnings (see below)"
msgstr "Dosya, engellemeyen uyarılar içeriyor (aşağıya bakın)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "The file will be imported by batches"
msgstr "Dosya toplu olarak içe aktarılacak"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "This column will be concatenated in field"
msgstr "Bu sütun alanda birleştirilecek"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "This file has been successfully imported up to line %d."
msgstr "%dsatır başarılı olarak içeri aktarıldı ."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Thousands Separator:"
msgstr "Binlik Ayracı:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "To import multiple values, separate them by a comma"
msgstr "Birden çok değeri içe aktarmak için bunları virgülle ayırın"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "To import, select a field..."
msgstr "İçe aktarmak için bir alan seçin..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Track history during import"
msgstr "İçe aktarma sırasında geçmişi izle"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Type"
msgstr "Tür"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""
"\"{extension}\" dosyası yüklenemiyor: \"{modname}\" Python modülü "
"gerektiriyor"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""
"Desteklenmeyen dosya biçimi \"{}\", Sadece CSV, ODS, XLS ve XLSX dosyaları "
"aktarılabilir."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Untitled"
msgstr "Başlıksız"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Upload File"
msgstr "Dosya Yükleme"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Upload an Excel or CSV file to import"
msgstr "İçe aktarmak için bir Excel veya CSV dosyası yükleyin"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Use first row as header"
msgstr "İlk satırı başlık olarak kullan"

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "User"
msgstr "Kullanıcı"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__value
msgid "Value"
msgstr "Değer"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__value2
msgid "Value2"
msgstr "Değer2"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr ""
"Uyarı: etiket satırını, boş satırları ve yalnızca boş hücrelerden oluşan "
"satırları yok sayar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "When a value cannot be matched:"
msgstr "Bir değer eşleştirilemediğinde:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""
"URL yolu ile görüntüleri içeri aktaramazsınız, nedeni için yöneticinizle ya "
"da destek ekibi ile kontrol edin. (Erişim Hakları yetkisini almalısınız)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "You can test or reload your file before resuming the import."
msgstr ""
"İçe aktarmaya devam etmeden önce dosyanızı test edebilir veya yeniden "
"yükleyebilirsiniz."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "You must configure at least one field to import"
msgstr "İçeri aktarım yapabilmek için en az bir alanı yapılandırmalısınız."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "at multiple rows"
msgstr "birden fazla satırda"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "at row"
msgstr "satır"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "batch"
msgstr "batch"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "in field"
msgstr "alanda"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "minutes"
msgstr "dakika"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "more)"
msgstr "daha)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "out of"
msgstr "out of"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "unknown error code %s"
msgstr "bilinmeyen hata kodu %s"
