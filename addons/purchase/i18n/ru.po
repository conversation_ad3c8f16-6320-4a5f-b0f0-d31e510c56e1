# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# Ye <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# Андр<PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>erina <<EMAIL>>, 2022
# Роман <PERSON>к<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# Константин <PERSON>н <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON> <yeli<PERSON><EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Диляр<PERSON>ьтаева <<EMAIL>>, 2022
# Martin Trigaux, 2022
# valmasone, 2022
# alenafairy, 2023
# <AUTHOR> <EMAIL>, 2024
# Смородин Даниил, 2024
# Ilya Rozhkov, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-06 20:35+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.action_report_purchase_order
msgid ""
"\n"
"                (object.state in ('draft', 'sent') and 'Request for Quotation - %s' % (object.name) or\n"
"                'Purchase Order - %s' % (object.name))"
msgstr ""
"\n"
"                (object.state in ('draft', 'sent') and 'Request for Quotation - %s' % (object.name) or\n"
"                'Заказ на поставку - %s' % (object.name))"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__nbr_lines
msgid "# of Lines"
msgstr "Кол-во строк"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "%(amount)s due %(date)s"
msgstr "%(amount)s должен %(date)s"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "%(product)s from %(original_receipt_date)s to %(new_receipt_date)s"
msgstr "%(product)s от %(original_receipt_date)s до %(new_receipt_date)s"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "%s confirmed the receipt will take place on %s."
msgstr "%s подтвердил, что получение произойдет на %s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "%s modified receipt dates for the following products:"
msgstr "%s изменены даты поступления следующих продуктов:"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.report_purchase_quotation
msgid "'Request for Quotation - %s' % (object.name)"
msgstr "'Запрос коммерческого предложения - %s' % (object.name)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "-&gt;"
msgstr "-&gt;"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "3-way matching"
msgstr "Трехсторонняя сверка"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_account_3way_match
msgid "3-way matching: purchases, receptions and bills"
msgstr "Трехсторонняя сверка: закупки, приходы и счета"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_reminder
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br><br>\n"
"        Here is a reminder that the delivery of the purchase order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            <span style=\"font-weight:bold;\">(<t t-out=\"object.partner_ref or ''\">REF_XXX</t>)</span>\n"
"        </t>\n"
"        is expected for \n"
"        <t t-if=\"object.date_planned\">\n"
"            <span style=\"font-weight:bold;\" t-out=\"format_date(object.get_localized_date_planned()) or ''\">05/05/2021</span>.\n"
"        </t>\n"
"         <t t-else=\"\">\n"
"            <span style=\"font-weight:bold;\">undefined</span>.\n"
"        </t>\n"
"        Could you please confirm it will be delivered on time?\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_done
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br><br>\n"
"        Here is in attachment a purchase order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>. \n"
"        <br><br>\n"
"        <t t-if=\"object.date_planned\">\n"
"            The receipt is expected for <span style=\"font-weight:bold;\" t-out=\"format_date(object.get_localized_date_planned()) or ''\">05/05/2021</span>.\n"
"            <br><br>\n"
"            Could you please acknowledge the receipt of this order?\n"
"        </t>\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br><br>\n"
"        Here is in attachment a request for quotation <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <br><br>\n"
"        If you have any questions, please do not hesitate to contact us.\n"
"        <br><br>\n"
"        Best regards,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Дорогой <t t-out=\"object.partner_id.name or ''\">Брэндон Фримен</t>\n"
"       <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Лазурный интерьер</t>)\n"
"       </t>\n"
"       <br><br>\n"
"        Во вложении запрос на коммерческое предложение <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">P00015</span>\n"
"       <t t-if=\"object.partner_ref\">\n"
"            со ссылкой: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"       </t>\n"
"        от <t t-out=\"object.company_id.name or ''\">компании YourCompany</t>.\n"
"       <br><br>\n"
"        Если у вас возникнут вопросы, пожалуйста, не стесняйтесь обращаться к нам.\n"
"       <br><br>\n"
"        С наилучшими пожеланиями,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-comment\"/> Send message"
msgstr "<i class=\"fa fa-comment\"/> Отправить сообщение"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Скачать"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/><span class=\"d-none d-md-inline\"> Done</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/><span class=\"d-none d-md-inline\"> Готово</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Paid</b>"
msgstr "<i class=\"fa fa-fw fa-check\"/><b>Оплачено</b></i>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Reversed</b>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Waiting Payment</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/><b>Ожидания платежа</b></i>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-credit-card\" role=\"img\" aria-label=\"Purchases\" "
"title=\"Purchases\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-credit-card\" role=\"img\" aria-label=\"Purchases\" "
"title=\"Purchases\"/>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Waiting for "
"Bill\" title=\"Waiting for Bill\"/><span class=\"d-none d-md-inline\"> "
"Waiting for Bill</span>"
msgstr ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Ожидание "
"счета\" title=\"Ожидание счета\"/><span class=\"d-none d-md-inline\"> "
"Ожидание счета</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-remove\" role=\"img\" aria-label=\"Cancelled\" "
"title=\"Cancelled\"/><span class=\"d-none d-md-inline\"> Cancelled</span>"
msgstr ""
"<i class=\"fa fa-fw fa-remove\" role=\"img\" aria-label=\"Отменено\" "
"title=\"Отменено\"/><span class=\"d-none d-md-inline\"> Отменено</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Печать"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Confirmation Date</span>\n"
"                          <span class=\"d-block d-md-none\">Confirmation</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Дата подтверждения</span>\n"
"                          <span class=\"d-block d-md-none\">Подтверждение</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Purchase Order #</span>\n"
"                          <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Заказ на закупку #</span>\n"
"                          <span class=\"d-block d-md-none\">Ссылка</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid ""
"<span class=\"d-none d-md-inline\">Request for Quotation #</span>\n"
"                        <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Запрос цены #</span>\n"
"                        <span class=\"d-block d-md-none\">Ссылка</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Значения настраиваются "
"отдельно для каждой компании.\" aria-label=\"Значения настраиваются отдельно"
" для каждой компании.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('state','not in',('draft','sent'))]}\">Request for Quotation </span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': [('state','in',('draft','sent'))]}\">Purchase Order </span>"
msgstr ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('state','not in',('draft','sent'))]}\">Запрос цены </span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': [('state','in',('draft','sent'))]}\">Заказ на поставку </span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "<span class=\"o_stat_text\">Purchased</span>"
msgstr "<span class=\"o_stat_text\">Закуплено</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': "
"[('mail_reception_confirmed','=', False)]}\">(confirmed by vendor)</span>"
msgstr ""
"<span class=\"text-muted\" attrs=\"{'invisible': "
"[('mail_reception_confirmed','=', False)]}\">(подтверждено "
"поставщиком)</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': "
"[('mail_reminder_confirmed', '=', False)]}\">(confirmed by vendor)</span>"
msgstr ""
"<span class=\"text-muted\" attrs=\"{'invisible': "
"[('mail_reminder_confirmed', '=', False)]}\">(подтверждено "
"поставщиком)</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                                <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Сумма</span>\n"
" <span groups=\"account.group_show_line_subtotals_tax_included\">Общая сумма</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_partner_property_form
msgid "<span> day(s) before</span>"
msgstr "<span> днем(ями) ранее</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "<span>Ask confirmation</span>"
msgstr "<span>Запросить подтверждение</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>Налоги</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">From:</strong>"
msgstr "<strong class=\"d-block mb-1\">От:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr "<strong class=\"d-block mb-1\">Счета</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Итог</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong class=\"text-muted\">Purchase Representative</strong>"
msgstr "<strong class=\"text-muted\">Ответственный за закупку</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Amount</strong>"
msgstr "<strong>Сумма</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Confirmation Date:</strong>"
msgstr "<strong>Дата подтверждения:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Date Req.</strong>"
msgstr "<strong>Дата Запроса</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Description</strong>"
msgstr "<strong>Описание</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Expected Date</strong>"
msgstr "<strong>Ожидаемая дата</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Date:</strong>"
msgstr "<strong>Дата заказа:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Deadline:</strong>"
msgstr "<strong>Крайний срок:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Purchase Representative:</strong>"
msgstr "<strong>Ответственный за закупку:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Qty</strong>"
msgstr "<strong>Кол-во</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Receipt Date:</strong>"
msgstr "<strong>Дата поставки:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Request For Quotation Date:</strong>"
msgstr "<strong>Дата запроса цен:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Адрес доставки:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Taxes</strong>"
msgstr "<strong>Налоги</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "<strong>The ordered quantity has been updated.</strong>"
msgstr "<strong>Заказанное количество было обновлено.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
msgid "<strong>The received quantity has been updated.</strong>"
msgstr "<strong>Полученное количество было обновлено.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This purchase has been canceled.</strong>"
msgstr "<strong>Эта закупка была отменена.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Цена за единицу</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Update Here</strong>"
msgstr "<strong>Обновить здесь</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Your Order Reference:</strong>"
msgstr "<strong>Ваш заказ:</strong>"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "A sample email has been sent to %s."
msgstr "Образец письма был отправлен на адрес %s."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Складируемый - это продукт, для которого вы отслеживаете остатки на складе. "
"Необходимо установить приложение Склад. Потребляемый - это продукт, для "
"которого остатки не учитываются. Услуга - это нематериальный продукт, "
"который вы предоставляете."

#. module: purchase
#: model:res.groups,name:purchase.group_warning_purchase
msgid "A warning can be set on a product or a customer (Purchase)"
msgstr ""
"Предупреждение может быть установлено на продукт или клиента (Закупка)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Ability to select a package type in purchase orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"Возможность выбора типа упаковки в заказах на поставку и принудительного "
"указания количества, кратного количеству единиц в упаковке."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Accept"
msgstr "Принять"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_warning
msgid "Access warning"
msgstr "Предупреждение доступа"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_accrued_expense_entry
msgid "Accrued Expense Entry"
msgstr "Ввод начисленных расходов"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_ids
msgid "Activities"
msgstr "Действия"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_state
msgid "Activity State"
msgstr "Статус действия"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Иконка типа действия"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a note"
msgstr "Добавить заметку"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a product"
msgstr "Добавить продукт"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a section"
msgstr "Добавить раздел"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Add several variants to the purchase order from a grid"
msgstr "Добавить несколько вариантов в заказ на поставку из графика платежей"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Add some products or services to your quotation."
msgstr "Добавьте несколько продуктов или услуг к вашему предложению."

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_manager
msgid "Administrator"
msgstr "Администратор"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Все"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "All Draft RFQs"
msgstr "Все черновики запросов цен"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "All Late RFQs"
msgstr "Все просроченные запросы цен"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "All RFQs"
msgstr "Все запросы цен"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "All Waiting RFQs"
msgstr "Все запросы цен без ответа"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__group_send_reminder
msgid "Allow automatically send email to remind your vendor the receipt date"
msgstr ""
"Позволяет автоматически отправлять электронное письмо, чтобы напомнить "
"поставщику о дате получения"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__edit
msgid "Allow to edit purchase orders"
msgstr "Позволить редактировать заказы на поставку"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__amount
msgid "Amount"
msgstr "Сумма"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay_pass
msgid ""
"Amount of time between date planned and order by date for each purchase "
"order line."
msgstr ""
"Количество времени между запланированной датой и датой заказа для каждой "
"строки заказа на поставку."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__avg_days_to_purchase
msgid ""
"Amount of time between purchase approval and document creation date. Due to "
"a hack needed to calculate this,               every record will show the "
"same average value, therefore only use this as an aggregated value with "
"group_operator=avg"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay
msgid "Amount of time between purchase approval and order by date."
msgstr "Количество времени между утверждением покупки и датой заказа."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_distribution
msgid "Analytic"
msgstr "Аналитический"

#. module: purchase
#: model:ir.model,name:purchase.model_account_analytic_account
msgid "Analytic Account"
msgstr "Аналитический счёт"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "Поиск по аналитическому распределению"

#. module: purchase
#: model:ir.model,name:purchase.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Применимость аналитических раделов"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_precision
msgid "Analytic Precision"
msgstr "Аналитическая точность"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Approve Order"
msgstr "Утвердить заказ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Attributes"
msgstr "Атрибуты"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Auto-Complete"
msgstr "Заполнить автоматически"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_vendor_bill_id
#: model:ir.model.fields,field_description:purchase.field_account_payment__purchase_vendor_bill_id
msgid "Auto-complete"
msgstr "Автозаполнение"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_vendor_bill_id
#: model:ir.model.fields,help:purchase.field_account_payment__purchase_vendor_bill_id
msgid "Auto-complete from a past bill / purchase order."
msgstr "Добавить несколько вариантов в заказ на поставку из графика платежей"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_id
#: model:ir.model.fields,help:purchase.field_account_payment__purchase_id
msgid "Auto-complete from a past purchase order."
msgstr "Автозаполнение из предыдущего заказа на поставку."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically lock confirmed orders to prevent editing"
msgstr ""
"Автоматически блокировать подтвержденные заказы, чтобы предотвратить их "
"редактирование"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically remind the receipt date to your vendors"
msgstr "Автоматически напоминать дату доставки вашим поставщикам"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,help:purchase.field_res_users__receipt_reminder_email
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"Automatically send a confirmation email to the vendor X days before the "
"expected receipt date, asking him to confirm the exact date."
msgstr ""
"Автоматически отправить поставщику электронное письмо с подтверждением за X "
"дней до ожидаемой даты получения с просьбой подтвердить точную дату."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_average
msgid "Average Cost"
msgstr "Средняя стоимость"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__avg_days_to_purchase
msgid "Average Days to Purchase"
msgstr ""

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "Avg Order Value"
msgstr "Средняя стоимость заказа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__default_purchase_method
msgid "Bill Control"
msgstr "Контроль счетов"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_count
msgid "Bill Count"
msgstr "подсчет счета"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__invoice_lines
msgid "Bill Lines"
msgstr "Cтроки счёта"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed"
msgstr "Выставлен счет"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_invoiced
msgid "Billed Qty"
msgstr "Кол-во по счёту"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed Quantity"
msgstr "Выставленное количество"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Billed Quantity:"
msgstr "Отфактурированное количество:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_status
msgid "Billing Status"
msgstr "Статус счёта"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_ids
msgid "Bills"
msgstr "Счета"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Bills Received"
msgstr "Счетов получено"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__block
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__block
msgid "Blocking Message"
msgstr "Блокирующее сообщение"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__user_id
msgid "Buyer"
msgstr "Покупатель"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_calendar
msgid "Calendar View"
msgstr "Вид календаря"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Calls for tenders are when you want to generate requests for quotations with"
" several vendors for a given set of products to compare offers."
msgstr ""
"Тендеры - это когда вы хотите провести запрос котировок у нескольких "
"поставщиков по определенному набору товаров, чтобы сравнить предложения."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Cancel"
msgstr "Отмена"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__cancel
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__cancel
#, python-format
msgid "Cancelled"
msgstr "Отменено"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Cancelled Purchase Order #"
msgstr "Отменены заказ на покупку #"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Cannot delete a purchase order line which is in state '%s'."
msgstr "Нельзя удалить строку заказа на поставку, которая в состоянии '%s'."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_category_id
msgid "Category"
msgstr "Категория"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__commercial_partner_id
msgid "Commercial Entity"
msgstr "Коммерческая организация"

#. module: purchase
#: model:ir.model,name:purchase.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__company_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Company"
msgstr "Компания"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__company_currency_id
msgid "Company Currency"
msgstr "Валюта компании"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Compose Email"
msgstr "Написать письмо"

#. module: purchase
#: model:ir.model,name:purchase.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config
msgid "Configuration"
msgstr "Конфигурация"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Confirm"
msgstr "Подтвердить"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Order"
msgstr "Подтвердить заказ"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Receipt Date"
msgstr "Подтвердить дату поступления"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__one_step
msgid "Confirm purchase orders in one step"
msgstr "Подтвердите заказы на поставку в один шаг"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Confirm your purchase."
msgstr "Подтвердите покупку."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_approve
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_approve
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date"
msgstr "Дата подтверждения"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date Last Year"
msgstr "Дата подтверждения Последний год"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__lock
msgid "Confirmed purchase orders are not editable"
msgstr "Подтвержденные заказы на поставку нельзя редактировать"

#. module: purchase
#: model:ir.model,name:purchase.model_res_partner
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Contact"
msgstr "Контакт"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_method
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_method
msgid "Control Policy"
msgstr "Политика контроля"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Преобразование между единицами измерения возможно, если они принадлежат "
"одной категории. Преобразование будет сделано на основе коэффициентов."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__country_code
msgid "Country code"
msgstr "Код страны"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Create Bill"
msgstr "Добавить счет"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Create Bills"
msgstr "Создать счета"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_batch_bills
msgid "Create Vendor Bills"
msgstr "Создание счетов поставщикам"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid "Create a new product variant"
msgstr "Создать новый вариант товара"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_uid
msgid "Created by"
msgstr "Создал"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_date
msgid "Created on"
msgstr "Дата создания"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_rate
msgid "Currency Rate"
msgstr "Курс валюты"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__access_url
msgid "Customer Portal URL"
msgstr "Ссылка на портал клиента"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__date
msgid "Date"
msgstr "Дата"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_calendar_start
msgid "Date Calendar Start"
msgstr "Дата Начало календаря"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Date Updated"
msgstr "Дата обновления"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Date:"
msgstr "Дата:"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "Days"
msgstr "Дни"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_users__reminder_date_before_receipt
msgid "Days Before Receipt"
msgstr "Дни до получения"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay
msgid "Days to Confirm"
msgstr "Дней до подтверждения"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay_pass
msgid "Days to Receive"
msgstr "Дней на получение"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Define your terms and conditions ..."
msgstr "Определите свои правила и условия  ..."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_planned
msgid ""
"Delivery date expected from vendor. This date respectively defaults to "
"vendor pricelist lead time then today's date."
msgstr ""
"Дата поставки, ожидаемая от поставщика. Эта дата, соответственно, по "
"умолчанию соответствует времени выполнения заказа по прейскуранту "
"поставщика, а затем сегодняшней дате."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_planned
msgid ""
"Delivery date promised by vendor. This date is used to determine expected "
"arrival of products."
msgstr ""
"Дата поставки, обещанная продавцом. Эта дата используется для определения "
"ожидаемого прибытия товаров."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_order
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_order
msgid ""
"Depicts the date within which the Quotation should be confirmed and "
"converted into a purchase order."
msgstr ""
"Указывает дату, в течение которой коммерческое предложение должно быть "
"подтверждено и преобразовано в заказ на поставку."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__name
msgid "Description"
msgstr "Описание"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_report__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_type
msgid "Display Type"
msgstr "Способ отображения"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Documentation"
msgstr "Документация"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Домен"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__done
msgid "Done"
msgstr "Выполнено"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation_amount
msgid "Double validation amount"
msgstr "Двойное подтверждение суммы"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Download"
msgstr "Скачать"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__draft
msgid "Draft RFQ"
msgstr "Черновик запроса цены"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Draft RFQs"
msgstr "Черновики запросов цен"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__dest_address_id
msgid "Dropship Address"
msgstr "Адрес Dropship"

#. module: purchase
#: model:ir.model,name:purchase.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Мастер составления эл. писем"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_planned
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_planned
msgid "Expected Arrival"
msgstr "Ожидаемое поступление"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Extended Filters"
msgstr "Расширенные фильтры"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Extra line with %s "
msgstr "Добавлена позиция %s "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__fiscal_position_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__fiscal_position_id
msgid "Fiscal Position"
msgstr "Система налогов"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Иконка со шрифтом Font awesome, например. fa-tasks"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable purchase order line"
msgstr "Запрещенные значения в неподотчетной строке заказа на поставку"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
#, python-format
msgid "From %s document"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__invoiced
msgid "Fully Billed"
msgstr "Счет полностью оплачен"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Future Activities"
msgstr "Будущие действия"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__two_step
msgid "Get 2 levels of approvals to confirm a purchase order"
msgstr "Получите 2 уровня разрешений, чтобы подтвердить заказ на поставку"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Get warnings in orders for products or vendors"
msgstr "Предупреждения в заказах по продуктам и поставщикам"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__weight
msgid "Gross Weight"
msgstr "Вес брутто"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Group By"
msgstr "Группировка"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Hide cancelled lines"
msgstr "Скрыть отмененные строки"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "History"
msgstr "История"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__id
msgid "ID"
msgstr "Идентификатор"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_icon
msgid "Icon"
msgstr "Иконка"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий действие исключения."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено - некоторые сообщения имеют ошибку доставки."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If enabled, activates 3-way matching on vendor bills : the items must be "
"received in order to pay the invoice."
msgstr ""
"Если разрешено, активируется трехсторонняя сверка счетов от поставщиков: "
"чтобы выставить счет, товары должны быть оприходованы в заказе."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If installed, the product variants will be added to purchase orders through "
"a grid entry."
msgstr ""
"Если установлено, варианты продукта будут добавлены в заказы на поставку "
"через график платежей."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_packaging__purchase
msgid "If true, the packaging can be used for purchase orders"
msgstr "Если true, упаковка может быть использована для заказов на поставку"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
#, python-format
msgid "Import Template for Products"
msgstr "Импортировать шаблон для товаров"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "In order to delete a purchase order, you must cancel it first."
msgstr ""
"Для того, чтобы удалить заказ на поставку, сначала вы должны отменить его."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__incoterm_id
msgid "Incoterm"
msgstr "Инкотерм"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Indicate the product quantity you want to order."
msgstr "Укажите количество товара, которое вы хотите заказать."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Международные правила по толкованию наиболее широко используемых торговых "
"терминов в области внешней торговли."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices and Incoming Shipments"
msgstr "Акты и полученные товары"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Invoicing"
msgstr "Выставление счетов"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move
msgid "Journal Entry"
msgstr "Запись журнала"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move_line
msgid "Journal Item"
msgstr "Элемент журнала"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union____last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order____last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line____last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_report____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "Late"
msgstr "Просрочено"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late Activities"
msgstr "Просроченные действия"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late RFQs"
msgstr "Просроченные запросы цен"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "Lead Time to Purchase"
msgstr "Средний срок заказа"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Let's create your first request for quotation."
msgstr "Давайте создадим ваш первый запрос на коммерческое предложение."

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid ""
"Let's try the Purchase app to manage the flow from purchase to reception and"
" invoice control."
msgstr ""
"Давайте попробуем приложение \"Закупки\" для управления потоком от покупки "
"до получения и контроля актов."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation
msgid "Levels of Approvals"
msgstr "Уровни Утверждения"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation
msgid "Levels of Approvals *"
msgstr "Уровни утверждений *"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Lock"
msgstr "Блокировать"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__lock_confirmed_po
msgid "Lock Confirmed Orders"
msgstr "Защитить подтвержденные заказы от изменений"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__done
#, python-format
msgid "Locked"
msgstr "Заблокировано"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Make sure you only pay bills for which you received the goods you ordered"
msgstr ""
"Будьте уверены, что платите по счетам только после получения заказанного "
"товара"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Manage your purchase agreements (call for tenders, blanket orders)"
msgstr "Управляйте вашими закупками, используя тендеры и стандартные заказы"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__qty_received_method__manual
msgid "Manual"
msgstr "Вручную"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Manual Invoices"
msgstr "Акты вручную"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_manual
msgid "Manual Received Qty"
msgstr "Количество полученных вручную"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lead
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for procuring products, they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Предел погрешности для времени выполнения поставщиком. Когда система "
"генерирует Заказы на Поставку для закупки продуктов, они будут запланированы"
" на несколько дней раньше, чтобы справиться с непредвиденными задержками "
"поставщиков."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__use_po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn_msg
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn_msg
msgid "Message for Purchase Order"
msgstr "Сообщение для заказа на поставку"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn_msg
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr "Сообщение для позиции заказа на поставку"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum Amount"
msgstr "Минимальное количество"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation_amount
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum amount for which a double validation is required"
msgstr "Минимальная сумма, на которую требуется двойная проверка"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_accountable_required_fields
msgid "Missing required fields on accountable purchase order line."
msgstr ""
"Отсутствуют обязательные поля в строке подотчетного заказа на поставку."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моего действия"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "My Draft RFQs"
msgstr "Мои черновики запросов цен"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "My Late RFQs"
msgstr "Мои просроченные запросы цен"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "My Orders"
msgstr "Мои заказы"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "My Purchases"
msgstr "Мои покупки"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "My RFQs"
msgstr "Мои запросы цен"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "My Waiting RFQs"
msgstr "Мои запросы цен без ответа"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "Имя"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Name, TIN, Email, or Reference"
msgstr "Имя, TIN, Email или ссылки"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Последние"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Встреча в календаре для следующего действия"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Крайний срок следующего действия"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_summary
msgid "Next Activity Summary"
msgstr "Краткое описание следующего действия"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_id
msgid "Next Activity Type"
msgstr "Тип следующего действия"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__no-message
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__no-message
msgid "No Message"
msgstr "Не предупреждать"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid "No Purchase Analysis"
msgstr "Отсутствует анализ закупок"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid "No product found. Let's create one!"
msgstr "Нет продуктов. Можете создать."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid "No purchase order found. Let's create one!"
msgstr "Заказ на поставку не найден. Давайте создадим его!"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_history
msgid "No purchase order were made for this product yet!"
msgstr "Для этого продукта еще не было сделано ни одного заказа!"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid "No request for quotation found. Let's create one!"
msgstr ""
"Не найдено ни одного запроса на коммерческое предложение. Давайте создадим "
"его!"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__0
msgid "Normal"
msgstr "Нормальный"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Not Acknowledged"
msgstr "Не принято"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Note"
msgstr "Заметка"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Notes"
msgstr "Заметки"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__no
msgid "Nothing to Bill"
msgstr "Нечего выставить в счет"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,help:purchase.field_res_users__reminder_date_before_receipt
msgid "Number of days to send reminder email before the promised receipt date"
msgstr ""
"Количество дней для отправки напоминания по электронной почте до обещанной "
"даты получения"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error_counter
msgid "Number of errors"
msgstr "Количество ошибок"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих действия"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество сообщений с ошибкой отправки"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__purchase
msgid "On ordered quantities"
msgstr "На заказанных количествах"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_method
#: model:ir.model.fields,help:purchase.field_product_template__purchase_method
msgid ""
"On ordered quantities: Control bills based on ordered quantities.\n"
"On received quantities: Control bills based on received quantities."
msgstr ""
"На заказанных количествах: контролируйте счета на основе заказных количеств."
" На полученных количествах: контролируйте счета на основе полученных "
"количеств."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__receive
msgid "On received quantities"
msgstr "На полученных количествах"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid ""
"Once you get the price from the vendor, you can complete the purchase order "
"with the right price."
msgstr ""
"Получив цену от продавца, вы сможете заполнить заказ на поставку, указав "
"правильную цену."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Once you ordered your products to your supplier, confirm your request for "
"quotation and it will turn into a purchase order."
msgstr ""
"После того как вы заказали продукцию поставщику, подтвердите запрос на "
"коммерческое предложение, и он превратится в заказ на поставку."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Order"
msgstr "Заказ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order Date"
msgstr "Дата заказа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_order
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Order Deadline"
msgstr "Крайний срок"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__order_line
msgid "Order Lines"
msgstr "Позиции заказа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Reference"
msgstr "Ссылка на заказ"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Ordered Quantity:"
msgstr "Заказанное количество:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__purchase
msgid "Ordered quantities"
msgstr "Заказанное кол-во"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Orders"
msgstr "Заказы"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Other Information"
msgstr "Прочая информация"

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase_done
#: model:mail.template,report_name:purchase.email_template_edi_purchase_reminder
msgid "PO_{{ (object.name or '').replace('/','_') }}"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_id
msgid "Packaging"
msgstr "Упаковка"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "Количество в упаковке"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__partner_id
msgid "Partner"
msgstr "Партнёр"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__country_id
msgid "Partner Country"
msgstr "Страна партнера"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__payment_term_id
msgid "Payment Terms"
msgstr "Условия оплаты"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Payment terms"
msgstr "Условия оплаты"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_url
msgid "Portal Access URL"
msgstr "Ссылка для доступа на портал"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Preview the reminder email by sending it to yourself."
msgstr ""
"Предварительно просмотрите письмо с напоминанием, отправив его самому себе."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Pricing"
msgstr "Стоимость"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Print"
msgstr "Печать"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Print RFQ"
msgstr "Распечатать Запрос цен"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__priority
msgid "Priority"
msgstr "Приоритет"

#. module: purchase
#: model:ir.model,name:purchase.model_product_template
#: model:ir.model.fields,field_description:purchase.field_purchase_order__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product"
msgstr "Продукт"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_attribute_action
msgid "Product Attributes"
msgstr "Атрибуты ТМЦ"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_category_config_purchase
msgid "Product Categories"
msgstr "Категории продуктов"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__category_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product Category"
msgstr "Категория продукта"

#. module: purchase
#: model:ir.model,name:purchase.model_product_packaging
msgid "Product Packaging"
msgstr "Тара"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_tmpl_id
msgid "Product Template"
msgstr "Шаблон продукта"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_type
msgid "Product Type"
msgstr "Тип продукта"

#. module: purchase
#: model:ir.model,name:purchase.model_product_product
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_product_action
#: model:ir.ui.menu,name:purchase.product_product_menu
msgid "Product Variants"
msgstr "Варианты продукта"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_normal_action_puchased
#: model:ir.ui.menu,name:purchase.menu_procurement_partner_contact_form
#: model:ir.ui.menu,name:purchase.menu_product_in_config_purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_products
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Products"
msgstr "Продукты"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation
msgid "Provide a double validation mechanism for purchases"
msgstr "Обеспечить механизм двойной проверки для покупок"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_packaging__purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_root
#: model:ir.ui.menu,name:purchase.purchase_report
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase"
msgstr "Закупки"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_requisition
msgid "Purchase Agreements"
msgstr "Соглашения о покупке"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_order_report_all
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_pivot
msgid "Purchase Analysis"
msgstr "Анализ закупок"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Purchase Description"
msgstr "Описание закупки"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_product_matrix
msgid "Purchase Grid Entry"
msgstr "Запись графика платежей закупа"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Purchase History"
msgstr "История покупок"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/product.py:0
#: code:addons/purchase/models/product.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Purchase History for %s"
msgstr "История покупок для %s"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lead
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lead
msgid "Purchase Lead Time"
msgstr "Запас по времени на закупку"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#: model:ir.actions.report,name:purchase.action_report_purchase_order
#: model:ir.model,name:purchase.model_purchase_order
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_account_payment__purchase_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__purchase_order_id
#: model:ir.model.fields.selection,name:purchase.selection__account_analytic_applicability__business_domain__purchase_order
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_pivot
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_activity
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
#, python-format
msgid "Purchase Order"
msgstr "Заказ на поставку"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Purchase Order #"
msgstr "Заказ на поставку #"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_order_approval
msgid "Purchase Order Approval"
msgstr "Утверждение заказа на поставку"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_analytic_account__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_account_payment__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_order_count
msgid "Purchase Order Count"
msgstr "Количество заказов на поставку"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_history
#: model:ir.model,name:purchase.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_line_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Purchase Order Line"
msgstr "Позиция заказа на поставку"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn
msgid "Purchase Order Line Warning"
msgstr "Предупреждение о позиции заказа на поставку"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
msgid "Purchase Order Lines"
msgstr "Позиции заказа на поставку"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lock
msgid "Purchase Order Modification"
msgstr "Изменение заказа на поставку"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lock
msgid "Purchase Order Modification *"
msgstr "Изменение заказа на поставку *"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lock
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lock
msgid ""
"Purchase Order Modification used when you want to purchase order editable "
"after confirm"
msgstr ""
"Изменение заказа на поставку, когда вы хотите изменить заказ на покупку "
"после подтверждения"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn
msgid "Purchase Order Warning"
msgstr "Предупреждение по заказу на закупку"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/analytic_account.py:0
#: model:ir.actions.act_window,name:purchase.purchase_form_action
#: model:ir.ui.menu,name:purchase.menu_purchase_form_action
#: model_terms:ir.ui.view,arch_db:purchase.account_analytic_account_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
#, python-format
msgid "Purchase Orders"
msgstr "Заказы на поставку"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_report
msgid "Purchase Report"
msgstr "отчет покупки"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__user_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Representative"
msgstr "Ответственный за закупку"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_warning_purchase
msgid "Purchase Warnings"
msgstr "Предупреждение о приобретении"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that have been invoiced."
msgstr "Заказы на поставку, на которые выставлен счет."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that include lines not invoiced."
msgstr "Заказы на поставку, которые имеют позиции, без выставленных счетов."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase products by multiple of unit # per package"
msgstr "Покупайте товары с множителем на количество в блоке"

#. module: purchase
#: model:ir.actions.server,name:purchase.purchase_send_reminder_mail_ir_actions_server
#: model:ir.cron,cron_name:purchase.purchase_send_reminder_mail
msgid "Purchase reminder"
msgstr "Напоминание о покупке"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase variants of a product using attributes (size, color, etc.)"
msgstr ""
"Приобретение вариантов товара, используя атрибуты (размер, цвет и т.д.)"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_done
msgid "Purchase: Purchase Order"
msgstr "Покупка: Заказ на поставку"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase
msgid "Purchase: Request For Quotation"
msgstr "Закупка: Запрос коммерческого предложения"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_reminder
msgid "Purchase: Vendor Reminder"
msgstr "Закупки: Напоминание о поставщике"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchased_product_qty
#: model:ir.model.fields,field_description:purchase.field_product_template__purchased_product_qty
msgid "Purchased"
msgstr "Куплено"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "Purchased Last 7 Days"
msgstr "Куплено за последние 7 дней"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "Purchased in the last 365 days"
msgstr "Покупки за последние 365 дней"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Purchases"
msgstr "Закупки"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_bill_union
msgid "Purchases & Bills Union"
msgstr "Объединение покупок и счетов"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""
"Поместите адрес, если вы хотите доставить напрямую от поставщика к клиенту. "
"В противном случае оставьте пустым для доставки в свою компанию."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_billed
msgid "Qty Billed"
msgstr "Количество в выставленных счетах"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_ordered
msgid "Qty Ordered"
msgstr "Заказанное к-во"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_received
msgid "Qty Received"
msgstr "Количество полученных"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_to_be_billed
msgid "Qty to be Billed"
msgstr "Количество к оплате"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Quantities billed by vendors"
msgstr "Количества, сфактурированные поставщиками"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_qty
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Quantity"
msgstr "Количество"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Quantity:"
msgstr "Количество:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__draft
msgid "RFQ"
msgstr "Запрос цены"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_approved
msgid "RFQ Approved"
msgstr "Запрос цены одобрен"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_confirmed
msgid "RFQ Confirmed"
msgstr "Запрос цены подтвержден"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_done
msgid "RFQ Done"
msgstr "Запрос цены выполнен"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__sent
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__sent
#: model:mail.message.subtype,name:purchase.mt_rfq_sent
msgid "RFQ Sent"
msgstr "Запрос цены отправлен"

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase
msgid "RFQ_{{ (object.name or '').replace('/','_') }}"
msgstr "ЗЦ_{{ (object.name or '').replace('/','_') }}"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "RFQs"
msgstr "Запросы цен"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "RFQs Sent Last 7 Days"
msgstr "Запросы цен, отправленные за последние 7 дней"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_purchase_order
msgid "RFQs and Purchases"
msgstr "Запросы цен и заказы на покупку"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__currency_rate
msgid "Ratio between the purchase order currency and the company currency"
msgstr "Соотношение между валютой заказа на поставку и валютой компании"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Send by Email"
msgstr "Переслать через Email"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_send_reminder
#: model:ir.model.fields,field_description:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,field_description:purchase.field_res_users__receipt_reminder_email
msgid "Receipt Reminder"
msgstr "Напоминание об отгрузке"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__receipt_reminder_email
msgid "Receipt Reminder Email"
msgstr "Напоминание о квитанции по электронной почте"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received"
msgstr "Получено"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received
msgid "Received Qty"
msgstr "Полученное кол-во"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "Метод полученного количества"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received Quantity"
msgstr "Полученное количество"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Received Quantity:"
msgstr "Полученное количество:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__receive
msgid "Received quantities"
msgstr "Полученные количества"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reception_confirmed
msgid "Reception Confirmed"
msgstr "Прием подтвержден"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid "Record a new vendor bill"
msgstr "Запишите новый счет поставщика"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__name
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Reference"
msgstr "Ссылка"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_tree
msgid "Reference Document"
msgstr "ссылка документа"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_uom
msgid "Reference Unit of Measure"
msgstr "Ссылка Единица Измерения"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__origin
msgid ""
"Reference of the document that generated this purchase order request (e.g. a"
" sales order)"
msgstr ""
"Ссылка на документ, который сгенерировал этот запрос заказа на поставку "
"(например, заказ на продажу)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_ref
msgid ""
"Reference of the sales order or bid sent by the vendor. It's used to do the "
"matching when you receive the products as this reference is usually written "
"on the delivery order sent by your vendor."
msgstr ""
"Ссылка заказа продажи или предложение, отправленное поставщиком. Он "
"используется для выполнения сопоставления при получении продукции, так как "
"эта ссылка обычно пишется на заказ доставки, отправленный поставщиком."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reminder_confirmed
msgid "Reminder Confirmed"
msgstr "Напоминание подтверждено"

#. module: purchase
#: model:ir.ui.menu,name:purchase.purchase_report_main
msgid "Reporting"
msgstr "Отчетность"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#: model:ir.actions.report,name:purchase.report_purchase_quotation
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
#, python-format
msgid "Request for Quotation"
msgstr "Запрос цены поставщика"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Request for Quotation #"
msgstr "Запрос цен #"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Request managers to approve orders above a minimum amount"
msgstr "Просить менеджеров одобрять заказы сверх минимальной суммы"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Requests For Quotation"
msgstr "Запросы на котировки"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_rfq_form
#: model:ir.actions.act_window,name:purchase.purchase_rfq
#: model:ir.ui.menu,name:purchase.menu_purchase_rfq
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Requests for Quotation"
msgstr "Запросы цен"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"Requests for quotation are documents that will be sent to your suppliers to request prices for different products you consider buying.\n"
"                Once an agreement has been found with the supplier, they will be confirmed and turned into purchase orders."
msgstr ""
"Запросы на коммерческое предложение — это документы, отправляемые вашим поставщикам для запроса цен на различные продукты, которые вы собираетесь приобрести.\n"
"                После достижения договоренности с поставщиком они будут подтверждены и превратятся в заказы на поставку."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_user_id
msgid "Responsible User"
msgstr "Ответственный"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Scheduled Date"
msgstr "Плановая дата"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Search Purchase Order"
msgstr "Искать заказ на поставку"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Search Reference Document"
msgstr "Поиск ссылки документа"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Search a vendor name, or create one on the fly."
msgstr "Найдите имя поставщика или создайте его на ходу."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_section
msgid "Section"
msgstr "Раздел"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "Название раздела (Продукты, Услуги и т.п.)"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__use_po_lead
msgid "Security Lead Time for Purchase"
msgstr "Запас по времени для закупок"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_token
msgid "Security Token"
msgstr "Код доступа"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Select a product, or create a new one on the fly."
msgstr "Выберите товар или создайте новый на лету."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Select a purchase order or an old bill"
msgstr "Выберите заказ на поставку или старый счет"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_product_template__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,help:purchase.field_res_users__purchase_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"\"Предупреждение\" - сообщить пользователю. \"Блокирующее сообщение\" - "
"исключительная ситуация, сообщить пользователю и заблокировать рабочий "
"процесс. Текст сообщения должен быть записан в следующее поле."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send PO by Email"
msgstr "Отправить Заказ Закупки по эл.почте"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_send_reminder
msgid "Send Reminder"
msgstr "Отправить Напоминание"

#. module: purchase
#: model:res.groups,name:purchase.group_send_reminder
msgid "Send an automatic reminder email to confirm delivery"
msgstr ""
"Отправьте автоматическое напоминание по электронной почте для подтверждения "
"доставки"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send by Email"
msgstr "Отправить по эл. почте"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Send the request for quotation to your vendor."
msgstr "Отправьте запрос на коммерческое предложение вашему поставщику."

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase
msgid "Sent manually to vendor to request a quotation"
msgstr "Отправить вручную поставщику запрос на коммерческое предложение"

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase_done
msgid "Sent to vendor with the purchase order in attachment"
msgstr "Отправляется поставщику вместе с заказом на поставку во вложении"

#. module: purchase
#: model:mail.template,description:purchase.email_template_edi_purchase_reminder
msgid ""
"Sent to vendors before expected arrival, based on the purchase order setting"
msgstr ""
"Отправка поставщикам до ожидаемого прибытия на основании настроек заказа на "
"поставку"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__sequence
msgid "Sequence"
msgstr "Нумерация"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set to Draft"
msgstr "Сделать черновиком"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_configuration
#: model:ir.ui.menu,name:purchase.menu_purchase_general_settings
msgid "Settings"
msgstr "Настройки"

#. module: purchase
#: model:ir.actions.server,name:purchase.model_purchase_order_action_share
msgid "Share"
msgstr "Поделиться"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Показать все записи, у которых дата следующего действия не превышает текущей"
" даты"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__reference
msgid "Source"
msgstr "Источник"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__origin
msgid "Source Document"
msgstr "Документ-источник"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Starred"
msgstr "Отмеченные"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__state
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__state
#: model:ir.model.fields,field_description:purchase.field_purchase_report__state
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Status"
msgstr "Статус"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус основан на плановых действиях\n"
"Просрочено: срок исполнения истек\n"
"Сегодня: выполнить сегодня\n"
"Запланировано: срок в будущем."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_subtotal
msgid "Subtotal"
msgstr "Подытог"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Billed"
msgstr "Сумма выставленного счета"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Ordered"
msgstr "Сумма заказанного количества"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Received"
msgstr "Сумма полученных количеств"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Total"
msgstr "Сумма Итого"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "Сумма необлагаемого налогом итога"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_users__property_purchase_currency_id
msgid "Supplier Currency"
msgstr "Валюта Поставщика"

#. module: purchase
#: model:ir.model,name:purchase.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Прайслист поставщика"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_tax
msgid "Tax"
msgstr "Налог"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Tax 15%"
msgstr "Налог 15%"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_country_id
msgid "Tax Country"
msgstr "Страна налогооблажения"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_totals
msgid "Tax Totals"
msgstr "Итоговые показатели по налогам"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__taxes_id
msgid "Taxes"
msgstr "Налоги"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__display_type
msgid "Technical field for UX purpose."
msgstr "Техническое поле для назначения UX."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__tax_country_id
msgid ""
"Technical field to filter the available taxes depending on the fiscal "
"country and fiscal position."
msgstr ""
"Техническое поле для фильтрации доступных налогов в зависимости от "
"фискальной страны и фискальной позиции."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Сроки и условия"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__notes
msgid "Terms and Conditions"
msgstr "Сроки и условия..."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Код страны ISO в двух символах.\n"
"Вы можете использовать это поле для быстрого поиска."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "The order receipt has been acknowledged by %s."
msgstr "Получение заказа было подтверждено %s."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid ""
"The request for quotation is the first step of the purchases flow. Once\n"
"                    converted into a purchase order, you will be able to control the receipt\n"
"                    of the products and the vendor bill."
msgstr ""
"Запрос цен поставщика является первым этапом процесса закупок.\n"
"Сделав его заказом на поставку, вы сможете контролировать приход\n"
"продукта и счёт-фактуру поставщика."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid "There are currently no purchase orders for your account."
msgstr "В настоящее время для вашей учетной записи нет заказов на поставку."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "There are currently no requests for quotation for your account."
msgstr "В настоящее время для вашей учетной записи нет запросов на котировки."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"There is no invoiceable line. If a product has a control policy based on "
"received quantity, please make sure that a quantity has been received."
msgstr ""
"Нет строки для выставления счетов. Если продукт имеет политику контроля на "
"основе полученного количества, пожалуйста, убедитесь, что количество было "
"получено."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid ""
"This analysis allows you to easily check and analyse your company purchase history and performance.\n"
"                You can track your negotiation performance, the delivery performance of your vendors, etc"
msgstr ""
"Этот анализ позволяет легко проверить и проанализировать историю и эффективность закупок вашей компании.\n"
"                Вы можете отслеживать результаты переговоров, эффективность поставок ваших поставщиков и т. д"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,help:purchase.field_res_users__property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""
"Эта валюта будет использоваться вместо установленной по умолчанию для "
"покупок у текущего партнера"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__default_purchase_method
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Значение по умолчанию, применяемое к создаваемому продукту. Может быть "
"изменено в карточке."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "This note is added to purchase orders."
msgstr "Это примечание добавлено к заказам на поставку."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should "
"purchase %(quantity).2f %(unit)s."
msgstr ""
"Этот продукт упакован по %(pack_size).2f %(pack_name)s. Вам следует "
"приобрести %(quantity).2f %(unit)s."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
#, python-format
msgid "This vendor bill has been created from: %s"
msgstr "Этот счет поставщика создан от: %s "

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/account_invoice.py:0
#, python-format
msgid "This vendor bill has been modified from: %s"
msgstr "Этот счет поставщика изменен от: %s "

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid "This vendor has no purchase order. Create a new RfQ"
msgstr ""
"У этого поставщика нет заказов на поставку. Создайте новый запрос цены"

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_0
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid "Tip: How to keep late receipts under control?"
msgstr "Совет: Как держать под контролем просроченные квитанции?"

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_1
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid "Tip: Never miss a purchase order"
msgstr "Совет: Никогда не пропускайте заказ на покупку"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__to_approve
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__to_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "To Approve"
msgstr "К одобрению"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "Количество без счета"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "To Send"
msgstr "К отправке"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Today Activities"
msgstr "Действия на сегодня"

#. module: purchase
#. odoo-python
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_total
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_total
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_total
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
#, python-format
msgid "Total"
msgstr "Всего"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_qty
msgid "Total Quantity"
msgstr "Общее количество"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total Untaxed amount"
msgstr "Итого без налога"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total amount"
msgstr "Итоговая сумма"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reception_confirmed
msgid "True if PO reception is confirmed by the vendor."
msgstr "Истина, если получение PO подтверждено поставщиком."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reminder_confirmed
msgid "True if the reminder email is confirmed by the vendor."
msgstr "Истина, если письмо с напоминанием подтверждено продавцом."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Type a message..."
msgstr "Напечатать сообщение..."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип активности исключения в записи."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"Unable to cancel this purchase order. You must first cancel the related "
"vendor bills."
msgstr ""
"Невозможно отменить этот заказ на поставку. Сначала нужно отменить связанные"
" счета поставщиков."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Unit Price"
msgstr "Цена за ед."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unit Price:"
msgstr "Цена за ед.:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom
msgid "Unit of Measure"
msgstr "Единица измерения"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_form_action
msgid "Units of Measure"
msgstr "Единицы измерения"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Категории единиц измерения"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_unit_of_measure_in_config_purchase
msgid "Units of Measures"
msgstr "Единицы Измерения"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unlock"
msgstr "Разблокировать"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Untaxed"
msgstr "Без налога"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Сумма без налога"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__untaxed_total
msgid "Untaxed Total"
msgstr "Итого без налогов"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "UoM"
msgstr "ЕИ"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Update Dates"
msgstr "Даты обновления"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__1
msgid "Urgent"
msgstr "Срочно"

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_user
msgid "User"
msgstr "Пользователь"

#. module: purchase
#. odoo-python
#: code:addons/purchase/report/purchase_report.py:0
#, python-format
msgid ""
"Value: 'avg_days_to_purchase' should only be used to show an average. If you"
" are seeing this message then it is being accessed incorrectly."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Variant Grid Entry"
msgstr "Вариант записи графика платежей"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor"
msgstr "Поставщик"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__vendor_bill_id
msgid "Vendor Bill"
msgstr "Счет от поставщика"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_supplier_invoices
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Vendor Bills"
msgstr "Счета от поставщиков"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor Country"
msgstr "Страна поставщика"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_pricelist_action2_purchase
msgid "Vendor Pricelists"
msgstr "Прайс-листы производителя"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_ref
msgid "Vendor Reference"
msgstr "Счет от поставщика"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management_supplier_name
msgid "Vendors"
msgstr "Поставщики"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid ""
"Vendors bills can be pre-generated based on purchase\n"
"                    orders or receipts. This allows you to control bills\n"
"                    you receive from your vendor according to the draft\n"
"                    document in Odoo."
msgstr ""
"Счета поставщиков могут быть предварительно сгенерированы на основе заказов\n"
"                   на поставку или квитанции. Это позволяет контролировать счета,\n"
"                   которые вы получаете от вашего поставщика согласно черновым\n"
"                   документам в Odoo."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "View"
msgstr "Осмотр"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__volume
msgid "Volume"
msgstr "Объём"

#. module: purchase
#. odoo-javascript
#: code:addons/purchase/static/src/views/purchase_dashboard.xml:0
#, python-format
msgid "Waiting"
msgstr "Ожидание"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__to_invoice
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Waiting Bills"
msgstr "Ожидающие счета"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Waiting RFQs"
msgstr "Запросы цен без ответа"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__warning
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__warning
#, python-format
msgid "Warning"
msgstr "Предупреждение"

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Warning for %s"
msgstr "Предупреждение для %s "

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
msgid "Warning on the Purchase Order"
msgstr "Предупреждение для заказа на поставку"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Warning when Purchasing this Product"
msgstr "Предупреждение при покупке этого товара"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Warnings"
msgstr "Предупреждения"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__website_message_ids
msgid "Website Messages"
msgstr "Сообщения с сайта"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__website_message_ids
msgid "Website communication history"
msgstr "История общения с сайтом"

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid ""
"When creating a purchase order, have a look at the vendor's <i>On Time "
"Delivery</i> rate: the percentage of products shipped on time. If it is too "
"low, activate the <i>automated reminders</i>. A few days before the due "
"shipment, Odoo will send the vendor an email to ask confirmation of shipment"
" dates and keep you informed in case of any delays. To get the vendor's "
"performance statistics, click on the OTD rate."
msgstr ""
"При создании заказа на поставку посмотрите на коэффициент <i>своевременной "
"доставки</i> поставщика: процент товаров, отправленных вовремя. Если он "
"слишком низок, активируйте <i>автоматические напоминания</i>. За несколько "
"дней до назначенной даты отгрузки Odoo отправит поставщику письмо с просьбой"
" подтвердить дату отгрузки и проинформировать вас в случае задержек. Чтобы "
"получить статистику работы поставщика, нажмите на показатель OTD."

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid ""
"When sending a purchase order by email, Odoo asks the vendor to acknowledge "
"the reception of the order. When the vendor acknowledges the order by "
"clicking on a button in the email, the information is added on the purchase "
"order. Use filters to track orders that have not been acknowledged."
msgstr ""
"При отправке заказа на покупку по электронной почте Odoo просит продавца "
"подтвердить получение заказа. Когда поставщик подтверждает заказ, нажав на "
"кнопку в электронном письме, информация добавляется в заказ на поставку. "
"Используйте фильтры для отслеживания заказов, которые не были подтверждены."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,help:purchase.field_purchase_order_line__partner_id
msgid "You can find a vendor by its Name, TIN, Email or Internal Reference."
msgstr ""
"Вы можете найти продавца по своему именем, TIN, электронной почте или "
"внутренним ссылке."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"You cannot change the type of a purchase order line. Instead you should "
"delete the current line and create a new line of the proper type."
msgstr ""
"Вы не можете изменить тип строки заказа на поставку. Вместо этого вы должны "
"удалить текущую строку и создать новую строку правильного типа."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Вы должны определить продукт для всего, что вы продаете или покупаете,\n"
"                будь то продукт с возможностью хранения, расходный материал или услуга."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"You must define a product for everything you sell or purchase,\n"
"            whether it's a storable product, a consumable or a service."
msgstr ""
"Вы должны определить продукт для всего, что вы продаете или покупаете,\n"
"                будь то продукт с возможностью хранения, расходный материал или услуга."

#. module: purchase
#. odoo-python
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"Коммерческое предложение содержит продукты компании %(product_company)s, хотя само предложение относится к компании %(quote_company)s.\n"
"Пожалуйста, измените компанию для предложения или уберите продукты, которые относятся к другим компаниям (%(bad_products)s)."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "close"
msgstr "закрыть"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "day(s) before"
msgstr "днем(ями) ранее"

#. module: purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase_done
#: model:mail.template,subject:purchase.email_template_edi_purchase_reminder
msgid "{{ object.company_id.name }} Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} Заказ (Ссылка {{ object.name or 'n/a' }})"
