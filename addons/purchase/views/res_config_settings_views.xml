<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form_purchase" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.purchase</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="25"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" data-string="Purchase" string="Purchase" data-key="purchase" groups="purchase.group_purchase_manager">
                    <field name="po_double_validation" invisible="1"/>
                    <field name="company_currency_id" invisible="1"/>
                    <field name="po_lock" invisible="1"/>
                    <h2>Orders</h2>
                    <div class="row mt16 o_settings_container" name="purchase_setting_container">
                        <div class="col-12 col-lg-6 o_setting_box" id="po_order_approval">
                            <div class="o_setting_left_pane">
                                <field name="po_order_approval"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="po_order_approval"/>
                                <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." aria-label="Values set here are company-specific." groups="base.group_multi_company" role="img"/>
                                <div class="text-muted">
                                    Request managers to approve orders above a minimum amount
                                </div>
                                <div class="content-group" attrs="{'invisible': [('po_order_approval', '=', False)]}">
                                    <div class="row mt16">
                                        <label for="po_double_validation_amount" class="col-lg-4 o_light_label"/>
                                        <field name="po_double_validation_amount"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" id="automatic_lock_confirmed_orders">
                            <div class="o_setting_left_pane">
                                <field name="lock_confirmed_po"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="lock_confirmed_po"/>
                                <div class="text-muted">
                                    Automatically lock confirmed orders to prevent editing
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" id="get_order_warnings">
                            <div class="o_setting_left_pane">
                                <field name="group_warning_purchase"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_warning_purchase" string="Warnings"/>
                                <div class="text-muted">
                                    Get warnings in orders for products or vendors
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box"
                            id="manage_purchase_agreements"
                            title="Calls for tenders are when you want to generate requests for quotations with several vendors for a given set of products to compare offers.">
                            <div class="o_setting_left_pane">
                                <field name="module_purchase_requisition"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="module_purchase_requisition"/>
                                <a href="https://www.odoo.com/documentation/16.0/applications/inventory_and_mrp/purchase/manage_deals/agreements.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Manage your purchase agreements (call for tenders, blanket orders)
                                </div>
                                <div class="content-group" attrs="{'invisible': [('module_purchase_requisition', '=', False)]}">
                                    <div id="use_purchase_requisition"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" id="auto_receipt_reminder">
                            <div class="o_setting_left_pane">
                                <field name="group_send_reminder"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_send_reminder"/>
                                <div class="text-muted">
                                    Automatically remind the receipt date to your vendors
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2>Invoicing</h2>
                    <div class="row mt16 o_settings_container" name="invoicing_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box" id="quantities_billed_vendor" title="This default value is applied to any new product created. This can be changed in the product detail form.">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <label for="default_purchase_method"/>
                                <a href="https://www.odoo.com/documentation/16.0/applications/inventory_and_mrp/purchase/manage_deals/control_bills.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Quantities billed by vendors
                                </div>
                                <div class="content-group">
                                    <div class="mt16">
                                        <field name="default_purchase_method" class="o_light_label" widget="radio"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box"
                            id="three_way_matching"
                            title="If enabled, activates 3-way matching on vendor bills : the items must be received in order to pay the invoice.">
                            <div class="o_setting_left_pane">
                                <field name="module_account_3way_match" string="3-way matching" widget="upgrade_boolean"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="module_account_3way_match"/>
                                <a href="https://www.odoo.com/documentation/16.0/applications/inventory_and_mrp/purchase/manage_deals/control_bills.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Make sure you only pay bills for which you received the goods you ordered
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2>Products</h2>
                    <div class="row mt16 o_settings_container" name="matrix_setting_container">
                        <div class="col-12 col-lg-6 o_setting_box" id="variant_options">
                            <div class="o_setting_left_pane">
                                <field name="group_product_variant"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_product_variant"/>
                                    <a href="https://www.odoo.com/documentation/16.0/applications/sales/sales/products_prices/products/variants.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Purchase variants of a product using attributes (size, color, etc.)
                                </div>
                                <div class="content-group" attrs="{'invisible': [('group_product_variant','=',False)]}">
                                    <div class="mt8">
                                        <button name="%(product.attribute_action)d" icon="fa-arrow-right" type="action" string="Attributes" class="btn-link"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box"
                            id="product_matrix"
                            title="If installed, the product variants will be added to purchase orders through a grid entry.">
                            <div class="o_setting_left_pane">
                                <field name="module_purchase_product_matrix"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="module_purchase_product_matrix" string="Variant Grid Entry"/>
                                <div class="text-muted">
                                    Add several variants to the purchase order from a grid
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box"
                            id="stock_packaging_purchase"
                            title="Ability to select a package type in purchase orders and to force a quantity that is a multiple of the number of units per package.">
                            <div class="o_setting_left_pane">
                                <field name="group_stock_packaging"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_stock_packaging"/>
                                <div class="text-muted">
                                    Purchase products by multiple of unit # per package
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="action_purchase_configuration" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'purchase', 'bin_size': False}</field>
    </record>

    <menuitem id="menu_purchase_general_settings" name="Settings" parent="menu_purchase_config"
        sequence="0" action="action_purchase_configuration" groups="base.group_system"/>

</odoo>
