# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_price_diff
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <jaro.b<PERSON><PERSON>@ekoenergo.sk>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-02 10:33+0000\n"
"PO-Revision-Date: 2023-02-02 12:25+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: purchase_price_diff
#: model:ir.model,name:purchase_price_diff.model_account_move_line
msgid "Journal Item"
msgstr "Položka účtovnej knihy"

#. module: purchase_price_diff
#: model:ir.model.fields,field_description:purchase_price_diff.field_product_category__property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase_price_diff.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase_price_diff.field_product_template__property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "Cenový rozdiel účtu"

#. module: purchase_price_diff
#: model:ir.model,name:purchase_price_diff.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: purchase_price_diff
#: model:ir.model,name:purchase_price_diff.model_product_category
msgid "Product Category"
msgstr "Kategória produktu"

#. module: purchase_price_diff
#: model:ir.model.fields,help:purchase_price_diff.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase_price_diff.field_product_template__property_account_creditor_price_difference
msgid ""
"This account is used in automated inventory valuation to record the price "
"difference between a purchase order and its related vendor bill when "
"validating this vendor bill."
msgstr ""
"Tento účet sa používa pri automatizovanom oceňovaní zásob na zaznamenanie "
"cenového rozdielu medzi objednávkou a súvisiacim dokladom dodávateľa pri "
"overovaní tohto dokladu dodávateľa."

#. module: purchase_price_diff
#: model:ir.model.fields,help:purchase_price_diff.field_product_category__property_account_creditor_price_difference_categ
msgid ""
"This account will be used to value price difference between purchase price "
"and accounting cost."
msgstr ""
"Tento účet bude používaný na hodnotenie cenového rozdielu medzi nákupnou "
"cenou a účtovným nákladom."
