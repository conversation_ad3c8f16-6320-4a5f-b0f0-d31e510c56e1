# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_discount
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_discount
#. odoo-python
#: code:addons/pos_discount/models/pos_config.py:0
#, python-format
msgid ""
"A discount product is needed to use the Global Discount feature. Go to Point"
" of Sale > Configuration > Settings to set it."
msgstr ""
"จำเป็นต้องมีสินค้าลดราคา เพื่อใช้คุณสมบัติส่วนลดแบบทั่วโลก เพื่อตั้งค่า "
"ไปที่การขายหน้าร้าน > การกำหนดค่า > การตั้งค่า"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr "อนุญาตให้แคชเชียร์มอบส่วนลดสำหรับคำสั่งทั้งหมด"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/xml/DiscountButton.xml:0
#, python-format
msgid "Discount"
msgstr "ส่วนลด"

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount %"
msgstr "ส่วนลด %"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_pc
#, python-format
msgid "Discount Percentage"
msgstr "เปอร์เซ็นต์ส่วนลด"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount Product"
msgstr "สินค้าลดราคา"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "No discount product found"
msgstr "ไม่พบสินค้าลดราคา"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "No tax"
msgstr "ไม่มีภาษี"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr "คำสั่งลดราคา"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr "กำหนดค่าการขายหน้าร้าน"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_session
msgid "Point of Sale Session"
msgstr "เซสชั่นการขายหน้าร้าน"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_product_id
msgid "Pos Discount Product"
msgstr "POS ส่วนลดสินค้า"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "Tax: %s"
msgstr "ภาษี: %s"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,help:pos_discount.field_res_config_settings__pos_discount_pc
msgid "The default discount percentage when clicking on the Discount button"
msgstr "เปอร์เซ็นต์ส่วนลดเริ่มต้นเมื่อคลิกที่ปุ่มส่วนลด"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid ""
"The discount product seems misconfigured. Make sure it is flagged as 'Can be"
" Sold' and 'Available in Point of Sale'."
msgstr ""
"ดูเหมือนว่าสินค้าส่วนลดจะกำหนดค่าไม่ถูกต้อง "
"ตรวจสอบให้แน่ใจว่าถูกตั้งค่าสถานะเป็น 'สามารถขายได้' และ "
"'มีจำหน่ายในการขายหน้าร้าน'"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to apply the discount on the ticket."
msgstr "สินค้าที่ใช้ในการรับส่วนลดบนทิกเก็ต"
