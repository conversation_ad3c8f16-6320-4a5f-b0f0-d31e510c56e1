# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_discount
# 
# Translators:
# Silvija <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> V<PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: Linas Versada <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: pos_discount
#. odoo-python
#: code:addons/pos_discount/models/pos_config.py:0
#, python-format
msgid ""
"A discount product is needed to use the Global Discount feature. Go to Point"
" of Sale > Configuration > Settings to set it."
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr "Leisti kasininkui suteikti nuolaidą visam užsakymui."

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/xml/DiscountButton.xml:0
#, python-format
msgid "Discount"
msgstr "Nuolaida"

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount %"
msgstr "Nuolaida %"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_pc
#, python-format
msgid "Discount Percentage"
msgstr "Nuolaidos procentas"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount Product"
msgstr "Nuolaidinis produktas"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "No discount product found"
msgstr "Nerasta nuolaidos produktas"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "No tax"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr "Užsakymo nuolaidos"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Pardavimo taško konfigūracija"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_session
msgid "Point of Sale Session"
msgstr "Pardavimo taško sesija"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_product_id
msgid "Pos Discount Product"
msgstr ""

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "Tax: %s"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,help:pos_discount.field_res_config_settings__pos_discount_pc
msgid "The default discount percentage when clicking on the Discount button"
msgstr ""

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid ""
"The discount product seems misconfigured. Make sure it is flagged as 'Can be"
" Sold' and 'Available in Point of Sale'."
msgstr ""
"Nuolaidinis produktas atrodo nustatytas neteisingai. Įsitikinkite kad jis "
"pažymėtas kaip \"Gali būti parduodamas\" ir \"Galimas pardavimo taške\"."

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to apply the discount on the ticket."
msgstr ""
