# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_stripe
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:22+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Complete the Stripe onboarding for company %s."
msgstr "Selesaikan onboarding Stripe untuk perusahaan %s."

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Do not have access to fetch token from Stripe"
msgstr "Anda tidak memiliki akses untuk fetch token dari Stripe"

#. module: pos_stripe
#: model_terms:ir.ui.view,arch_db:pos_stripe.pos_payment_method_view_form_inherit_pos_stripe
msgid ""
"Don't forget to complete Stripe connect before using this payment method."
msgstr ""
"Jangan lupa untuk menyelesaikan STripe connect sebelum menggunakan metode "
"pembayaran ini."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Failed to discover: %s"
msgstr "Gagal untuk menemukan: %s"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Failed to load resource: net::ERR_INTERNET_DISCONNECTED."
msgstr "Gagal untuk memuat resource: net::ERR_INTERNET_DISCONNECTED."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "No available Stripe readers."
msgstr "Tidak ada Stripe reader yang tersedia."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Payment canceled because not reader connected"
msgstr "Pembayaran dibatalkan karena reader tidak terhubung"

#. module: pos_stripe
#: model:ir.model,name:pos_stripe.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Metode Pembayaran Point of Sale POS"

#. module: pos_stripe
#: model:ir.model,name:pos_stripe.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesi Point of Sale"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Reader disconnected"
msgstr "Reader disconnected"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Stripe"
msgstr "Stripe"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Stripe Error"
msgstr "Stripe Error"

#. module: pos_stripe
#: model:ir.model.fields,field_description:pos_stripe.field_pos_payment_method__stripe_serial_number
msgid "Stripe Serial Number"
msgstr "Nomor Seri Stripe"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Stripe payment provider for company %s is missing"
msgstr "Penyedia pembayaran Stripe untuk perusahaan %s tidak ada"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Stripe readers %s not listed in your account"
msgstr "Stripe reader %s tidak terdaftar di akun Anda"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Terminal %s is already used on payment method %s."
msgstr "Terminal %s sudah digunakan pada metode pembayaran %s."

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Stripe, try again later."
msgstr "Terdapat beberapa masalah di antara kita dan Stripe, coba lagi nanti."

#. module: pos_stripe
#: model:ir.model.fields,help:pos_stripe.field_pos_payment_method__stripe_serial_number
msgid "[Serial number of the stripe terminal], for example: WSC513105011295"
msgstr "[Serial number of the stripe terminal], contohnya: WSC513105011295"
