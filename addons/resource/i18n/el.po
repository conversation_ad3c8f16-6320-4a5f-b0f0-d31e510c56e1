# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * resource
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: resource
#: code:addons/resource/models/resource.py:485
#, python-format
msgid "%s (copy)"
msgstr "%s (αντίγραφο)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__active
msgid "Active"
msgstr "Σε Ισχύ"

#. module: resource
#: selection:resource.calendar.attendance,day_period:0
msgid "Afternoon"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__hours_per_day
msgid "Average hour per day"
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__hours_per_day
msgid ""
"Average hours per day a resource is supposed to work with this calendar."
msgstr ""

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr "Οι ημέρες αργίας"

#. module: resource
#: model:ir.model,name:resource.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__company_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource__company_id
#: model:ir.model.fields,field_description:resource.field_resource_test__company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Εταιρία"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_test__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_date
#: model:ir.model.fields,field_description:resource.field_resource_test__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__day_period
msgid "Day Period"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__dayofweek
msgid "Day of Week"
msgstr "Ημέρα της εβδομάδας"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_res_users__resource_calendar_id
msgid "Default Working Hours"
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_res_users__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_resource__calendar_id
#: model:ir.model.fields,help:resource.field_resource_test__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Ορίστε το χρονοδιάγραμμα των πόρων"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""
"Καθορισμός των ωρών εργασίας και χρονοδιάγραμμα που θα μπορούσε να "
"προγραμματιστεί για τα μέλη του έργου σας"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__display_name
#: model:ir.model.fields,field_description:resource.field_resource_mixin__display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource__display_name
#: model:ir.model.fields,field_description:resource.field_resource_test__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__time_efficiency
msgid "Efficiency Factor"
msgstr "Αποδοτικότητα "

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_to
msgid "End Date"
msgstr "Ημερ. Λήξης"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Friday"
msgstr "Παρασκευή"

#. module: resource
#: code:addons/resource/models/resource.py:166
#, python-format
msgid "Friday Evening"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:165
#, python-format
msgid "Friday Morning"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__global_leave_ids
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Global Leaves"
msgstr "Γενικές Άδειες"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "Ώρες"

#. module: resource
#: selection:resource.resource,resource_type:0
msgid "Human"
msgstr "Άνθρωπος"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__id
#: model:ir.model.fields,field_description:resource.field_resource_resource__id
#: model:ir.model.fields,field_description:resource.field_resource_test__id
msgid "ID"
msgstr "Κωδικός"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__resource_id
msgid ""
"If empty, this is a generic holiday for the company. If a resource is set, "
"the holiday/leave is only for this resource"
msgstr ""
"Αν είναι κενό, αυτό είναι μια γενική αργία για την εταιρεία. Αν έχει οριστεί"
" ένας πόρος, η γιορτή/άδεια είναι μόνο για αυτόν τον πόρο"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Αν το ενεργό πεδίο έχει οριστεί σε Ψευδές, θα σας επιτρέψει να αποκρύψετε "
"την εγγραφή πόρου χωρίς να την καταργήσετε."

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Inactive"
msgstr "Ανενεργή"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar____last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance____last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves____last_update
#: model:ir.model.fields,field_description:resource.field_resource_mixin____last_update
#: model:ir.model.fields,field_description:resource.field_resource_resource____last_update
#: model:ir.model.fields,field_description:resource.field_resource_test____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_test__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_date
#: model:ir.model.fields,field_description:resource.field_resource_test__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: resource
#: selection:resource.calendar.leaves,time_type:0
msgid "Leave"
msgstr "Αποχώρηση"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Leave Date"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Leave Detail"
msgstr "Λεπτομέρεια Άδειας"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__leave_ids
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Leaves"
msgstr "Άδειες"

#. module: resource
#: selection:resource.resource,resource_type:0
msgid "Material"
msgstr "Υλικό"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Monday"
msgstr "Δευτέρα"

#. module: resource
#: code:addons/resource/models/resource.py:158
#, python-format
msgid "Monday Evening"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:157
#, python-format
msgid "Monday Morning"
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,day_period:0
msgid "Morning"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__name
#: model:ir.model.fields,field_description:resource.field_resource_resource__name
#: model:ir.model.fields,field_description:resource.field_resource_test__name
msgid "Name"
msgstr "Περιγραφή"

#. module: resource
#: selection:resource.calendar.leaves,time_type:0
msgid "Other"
msgstr "Άλλο"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__name
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Reason"
msgstr "Αιτία"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Σχετιζόμενο όνομα χρήστη του πόρου για τη διαχείριση της πρόσβασης."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_test__resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Resource"
msgstr "Πόρος"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.actions.act_window,name:resource.resource_calendar_leaves_action_from_calendar
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Leaves"
msgstr "Πόρος Αδειών"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
msgid "Resource Leaves Detail"
msgstr ""

#. module: resource
#: model:ir.model,name:resource.model_resource_mixin
msgid "Resource Mixin"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__resource_type
msgid "Resource Type"
msgstr "Τύπος Πόρου"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Working Time"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__calendar_id
msgid "Resource's Calendar"
msgstr "Ημερολόγιο Πόρων"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.actions.act_window,name:resource.resource_resource_action_from_calendar
#: model:ir.model,name:resource.model_resource_resource
#: model:ir.model.fields,field_description:resource.field_res_users__resource_ids
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Πόροι"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Leaves"
msgstr "Πόροι Αδειών"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
#: model_terms:ir.actions.act_window,help:resource.resource_resource_action_from_calendar
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""
"Οι 'Πόροι' σας επιτρέπουν να δημιουργήσετε και να διαχειρίζεται τους πόρους "
"που θα πρέπει να συμμετέχουν σε μια συγκεκριμένη φάση έργου. Μπορείτε επίσης"
" να ρυθμίσετε το επίπεδο της αποτελεσματικότητας τους και του φόρτου "
"εργασίας που βασίζονται σε εβδομαδιαίες ώρες εργασίας."

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Saturday"
msgstr "Σάββατο"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr "Αναζήτηση Πόρων"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Leaves"
msgstr "Αναζήτηση Περιόδων Αδειών Εργασίας"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr "Αναζήτηση Χρόνου Εργασίας"

#. module: resource
#: code:addons/resource/models/res_company.py:22
#, python-format
msgid "Standard 40 hours/week"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_from
msgid "Start Date"
msgstr "Ημερομηνία Έναρξης"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__hour_from
msgid ""
"Start and End time of working.\n"
"A specific value of 24:00 is interpreted as 23:59:59.999999."
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_from
msgid "Starting Date"
msgstr "Ημερομηνία Εκκίνησης"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Leave"
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Sunday"
msgstr "Κυριακή"

#. module: resource
#: model:ir.model,name:resource.model_resource_test
msgid "Test Resource Model"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:464
#, python-format
msgid "The efficiency factor cannot be equal to 0."
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:519
#, python-format
msgid "The start date of the leave must be earlier end date."
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__tz
#: model:ir.model.fields,help:resource.field_resource_mixin__tz
#: model:ir.model.fields,help:resource.field_resource_resource__tz
#: model:ir.model.fields,help:resource.field_resource_test__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__time_efficiency
msgid ""
"This field is used to calculate the the expected duration of a work order at"
" this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Thursday"
msgstr "Πέμπτη"

#. module: resource
#: code:addons/resource/models/resource.py:164
#, python-format
msgid "Thursday Evening"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:163
#, python-format
msgid "Thursday Morning"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__time_type
msgid "Time Type"
msgstr ""

#. module: resource
#: sql_constraint:resource.resource:0
msgid "Time efficiency must be strictly positive"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz
#: model:ir.model.fields,field_description:resource.field_resource_mixin__tz
#: model:ir.model.fields,field_description:resource.field_resource_resource__tz
#: model:ir.model.fields,field_description:resource.field_resource_test__tz
msgid "Timezone"
msgstr "Ζώνη Ώρας"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Tuesday"
msgstr "Τρίτη"

#. module: resource
#: code:addons/resource/models/resource.py:160
#, python-format
msgid "Tuesday Evening"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:159
#, python-format
msgid "Tuesday Morning"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Τύπος"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Χρήστης"

#. module: resource
#: model:ir.model,name:resource.model_res_users
msgid "Users"
msgstr "Χρήστες"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Wednesday"
msgstr "Τετάρτη"

#. module: resource
#: code:addons/resource/models/resource.py:162
#, python-format
msgid "Wednesday Evening"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:161
#, python-format
msgid "Wednesday Morning"
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__time_type
msgid ""
"Whether this should be computed as a holiday or as work time (eg: formation)"
msgstr ""

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Λεπτομέρειες Εργασίας"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Work Resources"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_from
msgid "Work from"
msgstr "Eργασία από"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_to
msgid "Work to"
msgstr "Eργασία έως"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_test__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Working Hours"
msgstr "Ώρες Εργασίας"

#. module: resource
#: code:addons/resource/models/resource.py:152
#, python-format
msgid "Working Hours of %s"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Period"
msgstr "Περίοδος Λειτουργίας"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_resource__calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
msgid "Working Time"
msgstr "Ωράριο Εργασίας"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.ui.menu,name:resource.menu_resource_calendar
msgid "Working Times"
msgstr ""
