# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_holidays
# 
# Translators:
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:01+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Belarusian (https://app.transifex.com/odoo/teams/41243/be/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: be\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: hr_work_entry_holidays
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: Time Off"
msgstr ""

#. module: hr_work_entry_holidays
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"A leave cannot be set across multiple contracts with different working schedules.\n"
"\n"
"Please create one time off for each contract.\n"
"\n"
"Time off:\n"
"%s\n"
"\n"
"Contracts:\n"
"%s"
msgstr ""

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_work_entry
msgid "HR Work Entry"
msgstr ""

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr ""

#. module: hr_work_entry_holidays
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays.work_entry_type_leave_form_inherit
msgid "Payroll"
msgstr ""

#. module: hr_work_entry_holidays
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry__leave_state
msgid "Status"
msgstr "Статус"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,help:hr_work_entry_holidays.field_hr_work_entry__leave_state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""

#. module: hr_work_entry_holidays
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"There is no employee set on the time off. Please make sure you're logged in "
"the correct company."
msgstr ""

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry__leave_id
msgid "Time Off"
msgstr ""

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry_type__leave_type_ids
msgid "Time Off Type"
msgstr ""

#. module: hr_work_entry_holidays
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_leave_type__work_entry_type_id
msgid "Work Entry Type"
msgstr ""

#. module: hr_work_entry_holidays
#: model:ir.model.fields,help:hr_work_entry_holidays.field_hr_work_entry_type__leave_type_ids
msgid "Work entry used in the payslip."
msgstr ""
