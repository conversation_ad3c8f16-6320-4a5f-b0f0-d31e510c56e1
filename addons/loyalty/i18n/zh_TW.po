# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* loyalty
# 
# Translators:
# <PERSON> <<PERSON>.<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 18:35+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__trigger
msgid ""
"\n"
"        Automatic: Customers will be eligible for a reward automatically in their cart.\n"
"        Use a code: Customers will be eligible for a reward if they enter a code.\n"
"        "
msgstr ""
"\n"
"        自動：顧客的購物車將自動獲得獎勵。\n"
"        使用代碼：顧客輸入代碼即可獲得獎勵。\n"
"        "

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__available_on
msgid ""
"\n"
"        Manage where your program should be available for use.\n"
"        "
msgstr ""
"\n"
"        管理您的程式應在哪裏使用。\n"
"        "

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__portal_visible
msgid ""
"\n"
"        Show in web portal, PoS customer ticket, eCommerce checkout, the number of points available and used by reward.\n"
"        "
msgstr ""
"\n"
"        在門戶網站、PoS 客戶票、電子商務結賬中顯示可用積分和已用積分。\n"
"        "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid " (Max %s)"
msgstr "（最大%s）"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%g%% on "
msgstr "%g%% 用於 "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per order"
msgstr "%s 每張訂單"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per order on "
msgstr "每張訂單%s,用於"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per point"
msgstr "%s 每積分"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per point on "
msgstr "每積分%s,用於"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "( Max"
msgstr "( 最大"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "(if at least"
msgstr "(如果至少"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "(tax excluded)"
msgstr "（未連稅）"

#. module: loyalty
#: model:loyalty.program,name:loyalty.10_percent_coupon
msgid "10% Discount Coupons"
msgstr "10% 折扣優惠券"

#. module: loyalty
#: model:loyalty.reward,description:loyalty.10_percent_coupon_reward
#: model:loyalty.reward,description:loyalty.10_percent_with_code_reward
msgid "10% on your order"
msgstr "全單九折"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "2+1 Free"
msgstr "買兩件，第三件免費"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"<br/>\n"
"                                        <br/>\n"
"\n"
"                                        <span class=\"fw-bold text-decoration-underline\">Applied to:</span>"
msgstr ""
"<br/>\n"
"                                        <br/>\n"
"\n"
"                                        <span class=\"fw-bold text-decoration-underline\">已套用至：</span>"

#. module: loyalty
#: model:mail.template,body_html:loyalty.mail_template_gift_card
msgid ""
"<div style=\"background: #ffffff\">\n"
"                <div style=\"margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center\">\n"
"                    Here is your gift card!\n"
"                </div>\n"
"                <div style=\"padding-top:20px; padding-bottom:20px\">\n"
"                    <img src=\"/loyalty/static/img/gift_card.png\" style=\"display:block; border:0; outline:none; text-decoration:none; margin:auto;\" width=\"300\">\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center\">\n"
"                        <strong t-out=\"format_amount(object.points, object.currency_id) or ''\">$ 150.00</strong></h3>\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;\">\n"
"                    <p style=\"margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333\">\n"
"                        <strong>Gift Card Code</strong>\n"
"                    </p>\n"
"                    <p style=\"margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9\" t-out=\"object.code or ''\">4f10-15d6-41b7-b04c-7b3e</p>\n"
"                </div>\n"
"                <div t-if=\"object.expiration_date\" style=\"padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center\">Card expires <t t-out=\"format_date(object.expiration_date) or ''\">05/05/2021</t></h3>\n"
"                </div>\n"
"                <div style=\"padding:20px; margin:0px; text-align:center;\">\n"
"                    <span style=\"background-color:#999999; display:inline-block; width:auto; border-radius:5px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop\" target=\"_blank\" style=\"text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center\">Use it right now!</a>\n"
"                    </span>\n"
"                </div>\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"background: #ffffff\">\n"
"                <div style=\"margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center\">\n"
"                    這是你的禮品卡！\n"
"                </div>\n"
"                <div style=\"padding-top:20px; padding-bottom:20px\">\n"
"                    <img src=\"/loyalty/static/img/gift_card.png\" style=\"display:block; border:0; outline:none; text-decoration:none; margin:auto;\" width=\"300\">\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center\">\n"
"                        <strong t-out=\"format_amount(object.points, object.currency_id) or ''\">$ 150.00</strong></h3>\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;\">\n"
"                    <p style=\"margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333\">\n"
"                        <strong>禮品卡編號</strong>\n"
"                    </p>\n"
"                    <p style=\"margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9\" t-out=\"object.code or ''\">4f10-15d6-41b7-b04c-7b3e</p>\n"
"                </div>\n"
"                <div t-if=\"object.expiration_date\" style=\"padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center\">卡到期日： <t t-out=\"format_date(object.expiration_date) or ''\">05/05/2021</t></h3>\n"
"                </div>\n"
"                <div style=\"padding:20px; margin:0px; text-align:center;\">\n"
"                    <span style=\"background-color:#999999; display:inline-block; width:auto; border-radius:5px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop\" target=\"_blank\" style=\"text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center\">現在立即使用！</a>\n"
"                    </span>\n"
"                </div>\n"
"            </div>\n"
"        "

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Product Domain\"/>"
msgstr "<i class=\"fa fa-cube fa-fw\" title=\"產品域\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/>"
msgstr "<i class=\"fa fa-cube fa-fw\" title=\"產品\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/><span>All Products</span>"
msgstr "<i class=\"fa fa-cube fa-fw\" title=\"產品\"/><span>所有產品</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cubes fa-fw\" title=\"Product Categories\"/>"
msgstr "<i class=\"fa fa-cubes fa-fw\" title=\"產品類別\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-search fa-fw\" title=\"Product Domain\"/>"
msgstr "<i class=\"fa fa-search fa-fw\" title=\"產品域\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-tags fa-fw\" title=\"Product Tags\"/>"
msgstr "<i class=\"fa fa-tags fa-fw\" title=\"產品標籤\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid ""
"<span attrs=\"{'invisible': [('clear_wallet', '!=', True)]}\"> (or "
"more)</span>"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid ""
"<span attrs=\"{'invisible': [('will_send_mail', '=', False)]}\">\n"
"                            Generate and Send \n"
"                        </span>\n"
"                        <span attrs=\"{'invisible': [('will_send_mail', '=', True)]}\">\n"
"                            Generate \n"
"                        </span>"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">Among:</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">在其中:</span>\n"
"                                    <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">Grant</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">授予</span>\n"
"                                    <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">Grant</span>\n"
"                                    <br/>\n"
"                                    the value of the coupon"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">授予</span>\n"
"                                    <br/>\n"
"                                    優惠券價值"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">In exchange of</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">可換取</span>\n"
"                                    <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('program_type', 'not in', ('coupons', 'next_order_coupons'))]}\">Coupons</span>\n"
"                                <span class=\"o_stat_text\" attrs=\"{'invisible': [('program_type', '!=', 'loyalty')]}\">Loyalty Cards</span>\n"
"                                <span class=\"o_stat_text\" attrs=\"{'invisible': [('program_type', 'not in', ('promotion', 'buy_x_get_y'))]}\">Promos</span>\n"
"                                <span class=\"o_stat_text\" attrs=\"{'invisible': [('program_type', '!=', 'promo_code')]}\">Discount</span>\n"
"                                <span class=\"o_stat_text\" attrs=\"{'invisible': [('program_type', '!=', 'gift_card')]}\">Gift Cards</span>\n"
"                                <span class=\"o_stat_text\" attrs=\"{'invisible': [('program_type', '!=', 'ewallet')]}\">eWallets</span>"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid ""
"<span class=\"text-center\">OR</span>\n"
"                                        <br/>"
msgstr ""
"<span class=\"text-center\">或</span>\n"
"                                        <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid ""
"<span colspan=\"2\" attrs=\"{'invisible': [('program_type', '!=', "
"'coupons')]}\">Grant the amount of coupon points defined as the coupon "
"value</span>"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "<span> x </span>"
msgstr "<span> x </span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>Minimum purchase of</span>"
msgstr "<span>最低採購量</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>Valid for purchase above</span>"
msgstr "<span>以上購買有效</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "<span>on</span>"
msgstr "<span>於</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>products</span>"
msgstr "<span>產品</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "<span>tax</span>"
msgstr "<span>稅</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "<strong>Gift Card Code</strong>"
msgstr "<strong>禮物卡代碼</strong>"

#. module: loyalty
#: model:mail.template,body_html:loyalty.mail_template_loyalty_card
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto; background:#ffffff; color:#333333;\"><tbody>\n"
"<tr>\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object._get_mail_partner().name\">\n"
"            Congratulations <t t-out=\"object._get_mail_partner().name or ''\">Brandon Freeman</t>,<br>\n"
"        </t>\n"
"\n"
"        Here is your reward from <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t>.<br>\n"
"\n"
"        <t t-foreach=\"object.program_id.reward_ids\" t-as=\"reward\">\n"
"            <t t-if=\"reward.required_points &lt;= object.points\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-esc=\"reward.description\">Reward Description</span>\n"
"                <br>\n"
"            </t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"<tr style=\"margin-top: 16px\">\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Use this promo code\n"
"        <t t-if=\"object.expiration_date\">\n"
"            before <t t-out=\"object.expiration_date or ''\">2021-06-16</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">15637502648479132902</strong>\n"
"        </p>\n"
"        <t t-foreach=\"object.program_id.rule_ids\" t-as=\"rule\">\n"
"            <t t-if=\"rule.minimum_qty not in [0, 1]\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Minimum purchase of <t t-out=\"rule.minimum_qty or ''\">10</t> products\n"
"                </span><br>\n"
"            </t>\n"
"            <t t-if=\"rule.minimum_amount != 0.00\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Valid for purchase above <t t-out=\"rule.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(rule.minimum_amount) or ''\">10.00</t>\n"
"                </span><br>\n"
"            </t>\n"
"        </t>\n"
"        <br>\n"
"        Thank you,\n"
"        <t t-if=\"object._get_signature()\">\n"
"            <br>\n"
"            <t t-out=\"object._get_signature() or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"</tbody></table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto; background:#ffffff; color:#333333;\"><tbody>\n"
"<tr>\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object._get_mail_partner().name\">\n"
"            恭喜你 <t t-out=\"object._get_mail_partner().name or ''\">Brandon Freeman</t>！<br>\n"
"        </t>\n"
"\n"
"        以下是你從 <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t> 獲得的獎賞。<br>\n"
"\n"
"        <t t-foreach=\"object.program_id.reward_ids\" t-as=\"reward\">\n"
"            <t t-if=\"reward.required_points &lt;= object.points\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-esc=\"reward.description\">獎賞描述</span>\n"
"                <br>\n"
"            </t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"<tr style=\"margin-top: 16px\">\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        請使用此推廣碼\n"
"        <t t-if=\"object.expiration_date\">\n"
"            （到期日： <t t-out=\"object.expiration_date or ''\">2021-06-16</t>）\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">15637502648479132902</strong>\n"
"        </p>\n"
"        <t t-foreach=\"object.program_id.rule_ids\" t-as=\"rule\">\n"
"            <t t-if=\"rule.minimum_qty not in [0, 1]\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    購買至少 <t t-out=\"rule.minimum_qty or ''\">10</t> 件貨品起適用。\n"
"                </span><br>\n"
"            </t>\n"
"            <t t-if=\"rule.minimum_amount != 0.00\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    購物金額 <t t-out=\"rule.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(rule.minimum_amount) or ''\">10.00</t> 以上適用。\n"
"                </span><br>\n"
"            </t>\n"
"        </t>\n"
"        <br>\n"
"        謝謝！\n"
"        <t t-if=\"object._get_signature()\">\n"
"            <br>\n"
"            <t t-out=\"object._get_signature() or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"</tbody></table>\n"
"        "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "A coupon with the same code was found."
msgstr "找到代碼相同的優惠券。"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_card_card_code_unique
msgid "A coupon/loyalty card must have a unique code."
msgstr "優惠券/忠誠卡必須有唯一的代碼。"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "A program must have at least one reward."
msgstr "一個計劃必須至少有一項獎勵。"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
#, python-format
msgid "A trigger with the same code as one of your coupon already exists."
msgstr "一個與您的優惠券的代碼相同的觸發器已經存在。"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_needaction
msgid "Action Needed"
msgstr "需採取行動"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__active
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_search
msgid "Active"
msgstr "啟用"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#, python-format
msgid "Add"
msgstr "增加"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Add a reward"
msgstr "加入獎勵"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Add a rule"
msgstr "加入規則"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__all_discount_product_ids
msgid "All Discount Product"
msgstr "所有折扣產品"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Among"
msgstr "在其中:"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_generate_wizard__mode__anonymous
msgid "Anonymous Customers"
msgstr "匿名客戶"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__mode
msgid "Application"
msgstr "應用"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__applies_on
msgid "Applies On"
msgstr "適用於"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_search
msgid "Archived"
msgstr "已封存"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_mail__trigger__create
msgid "At Creation"
msgstr "建立時"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_attachment_count
msgid "Attachment Count"
msgstr "附件數目"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__trigger__auto
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__mode__auto
msgid "Automatic"
msgstr "自動的"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__available_on
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Available On"
msgstr "在以下可用:"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
msgid "Balance"
msgstr "餘額"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Barcode"
msgstr "條碼"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Buy 10 products, and get 10$ discount on the 11th one."
msgstr ""

#. module: loyalty
#: model:loyalty.program,name:loyalty.3_cabinets_plus_1_free
msgid "Buy 3 large cabinets, get one for free"
msgstr "購買3個大櫃子，免費獲得一個"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__buy_x_get_y
#, python-format
msgid "Buy X Get Y"
msgstr "買X送Y"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_generate_wizard.py:0
#, python-format
msgid "Can not generate coupon, no program is set."
msgstr "不能生成優惠券，沒有設置方案"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Cancel"
msgstr "取消"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Card expires"
msgstr "卡片過期"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_category_id
msgid "Categories"
msgstr "類別"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__cheapest
msgid "Cheapest Product"
msgstr "最便宜的產品"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__clear_wallet
msgid "Clear Wallet"
msgstr "清空錢包"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Clear all promo point(s)"
msgstr "清除所有推廣積分"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__code
msgid "Code"
msgstr "代號"

#. module: loyalty
#: model:loyalty.program,name:loyalty.10_percent_with_code
msgid "Code for 10% on orders"
msgstr "訂單上 10% 的代碼"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__communication_plan_ids
msgid "Communication Plan"
msgstr "溝通計劃"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Communications"
msgstr "通訊"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__company_id
msgid "Company"
msgstr "公司"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
#, python-format
msgid "Compose Email"
msgstr "撰寫電郵"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__rule_ids
msgid "Conditional rules"
msgstr "有條件規則"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Conditions"
msgstr "條件"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Congratulations"
msgstr "恭喜"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#, python-format
msgid "Control panel buttons"
msgstr "控制面板按鈕"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_ids
msgid "Coupon"
msgstr "優惠券"

#. module: loyalty
#: model:ir.actions.report,name:loyalty.report_loyalty_card
msgid "Coupon Code"
msgstr "優惠碼"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_count
msgid "Coupon Count"
msgstr "息數"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Coupon point(s)"
msgstr "優惠券積分"

#. module: loyalty
#: model:loyalty.program,portal_point_name:loyalty.10_percent_coupon
msgid "Coupon points"
msgstr "優惠券積分"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Coupon value"
msgstr "優惠券價值"

#. module: loyalty
#: model:mail.template,name:loyalty.mail_template_loyalty_card
msgid "Coupon: Coupon Information"
msgstr "優惠券：優惠券資料"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.actions.act_window,name:loyalty.loyalty_card_action
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__coupons
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
#, python-format
msgid "Coupons"
msgstr "優惠券"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_discount_loyalty_action
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_gift_ewallet_action
msgid "Create a new one from scratch, or use one of the templates below."
msgstr "由零開始建立一個新範本，或使用以下其中一個範本。"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#, python-format
msgid "Create record"
msgstr "建立記錄"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__create_uid
msgid "Created by"
msgstr "創立者"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__create_date
msgid "Created on"
msgstr "建立於"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,portal_point_name:loyalty.3_cabinets_plus_1_free
#, python-format
msgid "Credit(s)"
msgstr "積分"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__currency_id
msgid "Currency"
msgstr "幣別"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__currency_symbol
msgid "Currency sign, to be used when printing amounts."
msgstr "貨幣符號，列印金額時使用."

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__both
msgid "Current & Future orders"
msgstr "目前及將來訂單"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__current
msgid "Current order"
msgstr "目前訂單"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__customer_tag_ids
msgid "Customer Tags"
msgstr "客戶標籤"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__customer_ids
msgid "Customers"
msgstr "客戶"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Define Discount codes on conditional rules then share it with your customers"
" for rewards."
msgstr "根據有條件規則定義折扣代碼，然後與您的客戶共享，以提供獎勵。"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Define promotions to apply automatically on your customers' orders."
msgstr ""

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__description
msgid "Description"
msgstr "說明"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Description on order"
msgstr "訂單上的描述"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__reward_type__discount
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Discount"
msgstr "折扣"

#. module: loyalty
#: model:ir.actions.act_window,name:loyalty.loyalty_program_discount_loyalty_action
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Discount & Loyalty"
msgstr "折扣及會員計劃"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_applicability
msgid "Discount Applicability"
msgstr "折扣可套用性"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__promo_code
#, python-format
msgid "Discount Code"
msgstr "折扣代碼"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_line_product_id
msgid "Discount Line Product"
msgstr "折扣資料行產品"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_mode
msgid "Discount Mode"
msgstr "折扣模式"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__payment_program_discount_product_id
msgid "Discount Product"
msgstr "折扣產品"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_domain
msgid "Discount Product Domain"
msgstr "折扣產品篩選範圍"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__code
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
#, python-format
msgid "Discount code"
msgstr "折扣代碼"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,portal_point_name:loyalty.10_percent_with_code
#, python-format
msgid "Discount point(s)"
msgstr "折扣積分"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Discount product"
msgstr "折扣產品"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_category_id
msgid "Discounted Prod. Categories"
msgstr "折扣產品類別"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_tag_id
msgid "Discounted Prod. Tag"
msgstr "折扣產品標籤"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_ids
msgid "Discounted Products"
msgstr "折扣產品"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Discounts"
msgstr "折扣"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Displayed as"
msgstr "顯示為"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Drive repeat purchases by sending a unique, single-use coupon code for the "
"next purchase when a customer buys something in your store."
msgstr "當客戶在您的商店中購買商品時，通過發送唯一的一次性優惠券代碼來推動重複購買，以便下次購買。"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Each rule can grant points to the customer he will be able to exchange "
"against rewards"
msgstr "每條規則都可以向客戶授予積分，他將能夠兌換獎勵"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__mail_template_id
msgid "Email Template"
msgstr "電郵範本"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__mail_template_id
msgid "Email template"
msgstr "電郵範本"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__minimum_amount_tax_mode__excl
msgid "Excluded"
msgstr "排除"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__expiration_date
msgid "Expiration Date"
msgstr "過期日期"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
#, python-format
msgid "Expiration date cannot be set on a loyalty card."
msgstr "到期日不可在會員卡/忠誠計劃卡上設定。"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Fidelity Cards"
msgstr "忠誠卡"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Fill in your eWallet, and use it to pay future orders."
msgstr ""

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__mode
msgid "For"
msgstr "對於"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__reward_type__product
#, python-format
msgid "Free Product"
msgstr "免費產品"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "Free Product - %s"
msgstr "免費產品－%s"

#. module: loyalty
#: model:loyalty.reward,description:loyalty.3_cabinets_plus_1_free_reward
msgid "Free Product - Large Cabinet"
msgstr "免費產品 - 大機櫃"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "Free Product - [%s]"
msgstr "免費產品 - [%s]"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "Free product"
msgstr "免費產品"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__future
msgid "Future orders"
msgstr "未來訂單"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#: model:ir.actions.act_window,name:loyalty.loyalty_generate_wizard_action
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
#, python-format
msgid "Generate"
msgstr "生成"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Generate &amp; share coupon codes manually. It can be used in eCommerce, "
"Point of Sale or regular orders to claim the Reward. You can define "
"constraints on its usage through conditional rule."
msgstr "手動生成和共享優惠券代碼。它可用於電子商務、銷售點或常規訂單以領取獎勵。您可以通過條件規則定義對其使用的約束。"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_generate_wizard
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate Coupons"
msgstr "生成優惠券"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate Gift Cards"
msgstr "生成禮物卡"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Generate and share unique coupons with your customers."
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate eWallet"
msgstr "生成電子錢包"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_gift_ewallet_view_form
msgid "Gift &amp; Ewallet"
msgstr "禮品及電子錢包"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.actions.report,name:loyalty.report_gift_card
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__gift_card
#: model:loyalty.reward,description:loyalty.gift_card_program_reward
#: model:product.template,name:loyalty.gift_card_product_50_product_template
#, python-format
msgid "Gift Card"
msgstr "禮物卡"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Gift Card Products"
msgstr "禮品卡產品"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Gift Card value"
msgstr "禮品卡價值"

#. module: loyalty
#: model:mail.template,name:loyalty.mail_template_gift_card
msgid "Gift Card: Gift Card Information"
msgstr "禮品卡：禮品卡資料"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,name:loyalty.gift_card_program
#, python-format
msgid "Gift Cards"
msgstr "禮物卡"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Gift Cards are created manually or automatically sent by email when the customer orders a gift card product.\n"
"                                    <br/>\n"
"                                    Then, Gift Cards can be used to pay orders."
msgstr ""
"禮品卡是手動建立的，或在客戶訂購禮品卡產品時透過電子郵件自動發送。\n"
"                                    <br/>\n"
"                                    然後，禮品卡可以用於支付訂單。"

#. module: loyalty
#: model:ir.actions.act_window,name:loyalty.loyalty_program_gift_ewallet_action
msgid "Gift cards & eWallet"
msgstr "禮品卡及電子錢包"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__points_granted
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Grant"
msgstr "授予"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Grant 1 credit for each item bought then reward the customer with Y items in"
" exchange of X credits."
msgstr "每購買一件物品即可獲得 1 個積分，然後可以獎勵客戶，可享以 X 積分 換取 Y 件物品。"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Here is your gift card!"
msgstr "這是您的禮物卡！"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Here is your reward from"
msgstr "這是你的獎勵"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__id
msgid "ID"
msgstr "ID"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "If minimum"
msgstr "如果最小"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "In exchange of"
msgstr "可換取"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_search
msgid "Inactive"
msgstr "停用"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__minimum_amount_tax_mode__incl
msgid "Included"
msgstr "已包括"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_generate_wizard.py:0
#, python-format
msgid "Invalid quantity."
msgstr "數量無效。"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__is_global_discount
msgid "Is Global Discount"
msgstr "總體折扣"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__is_nominative
msgid "Is Nominative"
msgstr "是名義性質"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__is_payment_program
msgid "Is Payment Program"
msgstr "是支付方案"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_count_display
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_tree
msgid "Items"
msgstr "項目"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card____last_update
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard____last_update
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail____last_update
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program____last_update
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward____last_update
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_mail_view_tree
msgid "Limit"
msgstr "限制"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__limit_usage
msgid "Limit Usage"
msgstr "限制使用"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Logo"
msgstr "Logo"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__loyalty
#, python-format
msgid "Loyalty Cards"
msgstr "會員卡"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_mail
msgid "Loyalty Communication"
msgstr "會員通訊"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "會員優惠券"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_program
msgid "Loyalty Program"
msgstr "會員計劃"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "會員獎賞"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "會員規則"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Loyalty point(s)"
msgstr "會員積分"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_max_amount
msgid "Max Discount"
msgstr "最大折扣"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__max_usage
msgid "Max Usage"
msgstr "最大用量"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_program_check_max_usage
msgid "Max usage must be strictly positive if a limit is used."
msgstr "如果使用限制，則最大使用量必須嚴格為正數。"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_ids
msgid "Messages"
msgstr "訊息"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_amount_tax_mode
msgid "Minimum Amount Tax Mode"
msgstr "最小額度稅收模式"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_amount
msgid "Minimum Purchase"
msgstr "最低購買金額"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_qty
msgid "Minimum Quantity"
msgstr "最小數量"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__multi_product
msgid "Multi Product"
msgstr "多項產品"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__next_order_coupons
#, python-format
msgid "Next Order Coupons"
msgstr "下一訂單優惠券"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_card_action
msgid "No Coupons Found."
msgstr "找不到優惠券。"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_gift_ewallet_action
msgid "No loyalty program found."
msgstr "找不到會員計劃。"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_discount_loyalty_action
msgid "No program found."
msgstr "找不到計劃。"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要採取行動的訊息數目"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Offer Y to your customers if they are buying X; for example, 2+1 free."
msgstr ""

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__order
msgid "Order"
msgstr "訂單"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__partner_id
msgid "Partner"
msgstr "業務夥伴"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Point(s)"
msgstr "積分"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__points
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__points
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Points"
msgstr "分"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__points_display
msgid "Points Display"
msgstr "積分顯示"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Points Unit"
msgstr "積分單位"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__required_points
msgid "Points needed"
msgstr "需要積分"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__points_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__portal_point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_name
msgid "Portal Point Name"
msgstr "門戶點名稱"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__portal_visible
msgid "Portal Visible"
msgstr "門戶可見"

#. module: loyalty
#: model:ir.model,name:loyalty.model_product_template
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_id
msgid "Product"
msgstr "商品"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_domain
msgid "Product Domain"
msgstr "產品domain"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_tag_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_tag_id
msgid "Product Tag"
msgstr "產品標籤"

#. module: loyalty
#: model:ir.model,name:loyalty.model_product_product
msgid "Product Variant"
msgstr "產品款式"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__payment_program_discount_product_id
msgid "Product used in the sales order to apply the discount."
msgstr "用銷售訂單中應用折扣產品。"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__discount_line_product_id
msgid ""
"Product used in the sales order to apply the discount. Each reward has its "
"own product for reporting purpose"
msgstr "在銷售訂單中使用的產品應用折扣。每個獎勵都有自己的產品用於報告目的."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__trigger_product_ids
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_ids
msgid "Products"
msgstr "產品"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__program_id
msgid "Program"
msgstr "計劃"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__name
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Program Name"
msgstr "計劃名稱"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__program_type
msgid "Program Type"
msgstr "計劃類型"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Program trigger"
msgstr "計劃觸發器"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promo point(s)"
msgstr "推廣促銷積分"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promos"
msgstr "推廣"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promotion Program"
msgstr "推廣計劃"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promotional Program"
msgstr "推廣計劃"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__promotion
msgid "Promotions"
msgstr "促銷"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__coupon_qty
msgid "Quantity"
msgstr "數量"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Quantity rewarded"
msgstr "已獎勵的數量"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Quantity to generate"
msgstr "生成的數量"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_amount
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Reward"
msgstr "獎勵"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_mode
msgid "Reward Point Mode"
msgstr "獎勵積分模式"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_domain
msgid "Reward Product Domain"
msgstr "獎勵產品域"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_qty
msgid "Reward Product Qty"
msgstr "獎勵產品數量"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_uom_id
msgid "Reward Product Uom"
msgstr "獎勵產品單位"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_ids
msgid "Reward Products"
msgstr "獎賞產品"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_type
msgid "Reward Type"
msgstr "獎勵類型"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid ""
"Reward your customers for a purchase with a coupon to use on their next "
"order."
msgstr ""

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__reward_ids
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Rewards"
msgstr "獎勵"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_rule_reward_point_amount_positive
msgid "Rule points reward must be strictly positive."
msgstr "規則積分獎勵必須限定為正數。"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Rules & Rewards"
msgstr "規則及獎勵"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_generate_wizard__mode__selected
msgid "Selected Customers"
msgstr "已選取客戶"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Sell Gift Cards, that can be used to purchase products."
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
msgid "Send"
msgstr "發送"

#. module: loyalty
#: model:mail.template,description:loyalty.mail_template_gift_card
msgid "Sent to customer who purchased a gift card"
msgstr "發送給採購禮品卡的客戶"

#. module: loyalty
#: model:mail.template,description:loyalty.mail_template_loyalty_card
msgid "Sent to customer with coupon information"
msgstr "發送給客戶的優惠券資料"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__sequence
msgid "Sequence"
msgstr "序號"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Set up conditional rules on the order that will give access to rewards for "
"customers"
msgstr "在訂單上設置有條件規則，以便為客戶提供獎勵存取權限"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid ""
"Share a discount code with your customers to create a purchase incentive."
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Show points Unit"
msgstr "顯示積分單位"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__specific
msgid "Specific Products"
msgstr "特殊產品"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_split
msgid "Split per unit"
msgstr "按單位分拆"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "Split per unit is not allowed for Loyalty and eWallet programs."
msgstr "會員和電子錢包計劃不允許按單位分拆。"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__currency_symbol
msgid "Symbol"
msgstr "符號"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Thank you,"
msgstr "非常感謝,"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_discount_positive
msgid "The discount must be strictly positive."
msgstr "折扣必須限定為正數。"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "The promo code must be unique."
msgstr "推廣代碼必須是唯一的。"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_required_points_positive
msgid "The required points for a reward must be strictly positive."
msgstr "獎勵所需的積分必須限定為正數。"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "The reward description field cannot be empty."
msgstr "獎勵描述欄位不可留空。"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_product_qty_positive
msgid "The reward product quantity must be strictly positive."
msgstr "獎勵產品數量必須限定為正數。"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_card_action
msgid "There haven't been any coupons generated yet."
msgstr "尚未產生任何優惠券。"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__reward_product_ids
msgid "These are the products that can be claimed with this rule."
msgstr "可按此規則領取優惠的產品如下。"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__discount_max_amount
msgid ""
"This is the max amount this reward may discount, leave to 0 for no limit."
msgstr "這是此獎勵可以套用折扣的最大金額，保留為 0 表示無限制。"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/product_product.py:0
#, python-format
msgid ""
"This product may not be archived. It is being used for an active promotion "
"program."
msgstr "此產品可能未封存。它被用於生效的促銷計劃。"

#. module: loyalty
#: model:product.template,name:loyalty.ewallet_product_50_product_template
msgid "Top-up eWallet"
msgstr "充值電子錢包"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__total_order_count
msgid "Total Order Count"
msgstr "總訂單統計"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__trigger
msgid "Trigger"
msgstr "觸發器"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "Unsupported search operator"
msgstr ""

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__use_count
msgid "Use Count"
msgstr "使用數目"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__trigger__with_code
msgid "Use a code"
msgstr "使用代碼"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Use points on"
msgstr "使用積分在"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Use this promo code before"
msgstr "之前使用此促銷代碼"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__user_has_debug
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__user_has_debug
msgid "User Has Debug"
msgstr "用戶有除錯"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__valid_until
msgid "Valid Until"
msgstr "有效期至"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__date_to
msgid "Validity"
msgstr "驗證"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__trigger
msgid "When"
msgstr "何時"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_mail__trigger__points_reach
msgid "When Reaching"
msgstr "到達時"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"When customers make an order, they accumulate points they can exchange for "
"rewards on the current order or on a future one."
msgstr "當客戶下訂單時，他們會累積積分，以兌換當前訂單或未來訂單的獎勵。"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"When generating coupon, you can define a specific points value that can be "
"exchanged for rewards."
msgstr "生成優惠券時，您可以定義可以兌換獎勵的特定積分值。"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_rule__reward_point_split
msgid ""
"Whether to separate reward coupons per matched unit, only applies to "
"'future' programs and trigger mode per money spent or unit paid.."
msgstr "是否按匹配單位分開獎勵優惠券，僅適用於「未來」計劃和每筆花費或單位支付的觸發模式。"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__will_send_mail
msgid "Will Send Mail"
msgstr "將發送郵件"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Win points with each purchase, and use points to get gifts."
msgstr ""

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__mode__with_code
msgid "With a promotion code"
msgstr "有促銷代碼"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "You can not delete a program in an active state"
msgstr "不可刪除一個已啟用的計劃"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/product_product.py:0
#: code:addons/loyalty/models/product_template.py:0
#, python-format
msgid ""
"You cannot delete %(name)s as it is used in 'Coupons & Loyalty'. Please "
"archive it instead."
msgstr "不可刪除 %(name)s，因為它用於「優惠券及會員」。請將其封存。"

#. module: loyalty
#: model:mail.template,report_name:loyalty.mail_template_loyalty_card
msgid "Your Coupon Code"
msgstr "你的優惠券代碼"

#. module: loyalty
#: model:mail.template,report_name:loyalty.mail_template_gift_card
msgid "Your Gift Card"
msgstr "您的禮物卡"

#. module: loyalty
#: model:mail.template,subject:loyalty.mail_template_gift_card
msgid "Your Gift Card at {{ object.company_id.name }}"
msgstr "您在 {{ object.company_id.name }}的禮品卡"

#. module: loyalty
#: model:mail.template,subject:loyalty.mail_template_loyalty_card
msgid "Your reward coupon from {{ object.program_id.company_id.name }} "
msgstr "您透過 {{ object.program_id.company_id.name }} 活動獲得此優惠券 "

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "all"
msgstr "所有"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "discount"
msgstr "折扣"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "e.g. 10% discount on laptops"
msgstr "例如：筆記型電腦有10%折扣"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__ewallet
#, python-format
msgid "eWallet"
msgstr "電子錢包"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "eWallet Products"
msgstr "電子錢包產品"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "eWallet value"
msgstr "電子錢包價值"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "eWallets"
msgstr "電子錢包"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"eWallets are created manually or automatically when the customer orders a eWallet product.\n"
"                                    <br/>\n"
"                                    Then, eWallets are proposed during the checkout, to pay orders."
msgstr ""
"電子錢包是在客戶訂購電子錢包產品時手動或自動建立的。\n"
"                                    <br/>\n"
"                                    然後，在結賬時建議使用電子錢包來支付訂單。"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "item(s) bought"
msgstr "項產品已買"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"on the cheapest product\n"
"                                        <br/>"
msgstr ""
"用於最便宜的產品上\n"
"                                        <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "on your next order"
msgstr "在您的下一個訂單"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"on your order\n"
"                                        <br/>"
msgstr ""
"用於你的訂單\n"
"                                        <br/>"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "per %s spent"
msgstr "每消費 %s"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "per order"
msgstr "每張訂單"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "per unit paid"
msgstr "每個已付款單位"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "specific products"
msgstr "指定產品"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "spent"
msgstr "消費"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "the cheapest product"
msgstr "最便宜的產品"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "to"
msgstr "到"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "usages"
msgstr "使用"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "your order"
msgstr "您的訂單"
