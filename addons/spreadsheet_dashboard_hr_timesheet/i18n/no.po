# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_hr_timesheet
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-29 09:44+0000\n"
"Language-Team: Norwegian (https://app.transifex.com/odoo/teams/41243/no/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: no\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid " days"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Assignee"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Assignees"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Current"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Customer"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Days to assign"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Days to close"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Hours Logged"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Hours logged"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "KPI"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Period"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Previous"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Project"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tag"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tags"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks Analysis by Assignees"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks Analysis by Customer"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks Analysis by Project"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks Analysis by Tags"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks by Stage"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks by State"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Time to Assign"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Time to Close"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Top Assignees"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Top Customers"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Top Projects"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Top Tags"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "last period"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "since last period"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "stats - current"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "stats - previous"
msgstr ""
