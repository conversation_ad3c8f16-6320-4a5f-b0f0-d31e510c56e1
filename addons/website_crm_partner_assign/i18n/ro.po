# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_crm_partner_assign
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <fold<PERSON><EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__nbr_opportunities
msgid "# of Opportunity"
msgstr "# Oportunitate"

#. module: website_crm_partner_assign
#. odoo-javascript
#: code:addons/website_crm_partner_assign/static/src/js/crm_partner_assign.js:0
#, python-format
msgid "%s's Opportunity"
msgstr "%sOportunitate"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_view_kanban
msgid ""
"<i class=\"fa fa-arrow-circle-right\" aria-label=\"Assigned Partner\" "
"title=\"Assigned Partner\"/>"
msgstr ""
"<i class=\"fa fa-arrow-circle-right\" aria-label=\"Assigned Partner\" "
"title=\"Assigned Partner\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<i class=\"fa fa-file-text-o\"/> I'm interested"
msgstr "<i class=\"fa fa-file-text-o\"/>Sunt interesat"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<i class=\"fa fa-fw fa-times\"/> I'm not interested"
msgstr "<i class=\"fa fa-fw fa-times\"/> Nu sunt interesat"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>Editează"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<option>Countries...</option>"
msgstr "<option>Țări...</option>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<option>States...</option>"
msgstr "<option>State...</option>"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
#, python-format
msgid "<p>I am interested by this lead.</p>"
msgstr "<p>Sunt interesat de acest argument.</p>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<small class=\"me-2 mt-1 float-start\"><b>Stage:</b></small>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<small class=\"text-muted\">Opportunity - </small>"
msgstr "<small class=\"text-muted\">Oportunitate - </small>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid ""
"<span class=\"fa fa-envelope fa-fw\" role=\"img\" aria-label=\"Email\" "
"title=\"Email\"/>"
msgstr ""
"<span class=\"fa fa-envelope fa-fw\" role=\"img\" aria-label=\"Email\" "
"title=\"Email\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<span class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr "<span class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid ""
"<span class=\"fa fa-map-marker fa-fw\" role=\"img\" aria-label=\"Address\" "
"title=\"Address\"/>"
msgstr ""
"<span class=\"fa fa-map-marker fa-fw\" role=\"img\" aria-label=\"Address\" "
"title=\"Address\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid ""
"<span class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" "
"title=\"Mobile\"/>"
msgstr ""
"<span class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" "
"title=\"Mobile\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<span class=\"fa fa-mobile\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"
msgstr "<span class=\"fa fa-mobile\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<span class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr "<span class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<span class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr "<span class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<span class=\"fa fa-user fa-fw\" role=\"img\" aria-label=\"User\" title=\"User\"/>"
msgstr "<span class=\"fa fa-user fa-fw\" role=\"img\" aria-label=\"User\" title=\"User\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid ""
"<span class=\"input-group-text\" data-target=\"#exp_closing_div\" data-toggle=\"datetimepicker\">\n"
"                                                                <span class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\"/>\n"
"                                                            </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid ""
"<span class=\"input-group-text\" data-target=\"#next_activity_div\" data-toggle=\"datetimepicker\">\n"
"                                                                <span class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>\n"
"                                                            </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid ""
"<span class=\"oe_grey\" attrs=\"{'invisible':[('partner_latitude','&lt;=',0)]}\">N </span>\n"
"                                    <span class=\"oe_grey\" attrs=\"{'invisible':[('partner_latitude','&gt;=',0)]}\">S </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid ""
"<span class=\"oe_grey\" attrs=\"{'invisible':[('partner_longitude','&lt;=',0)]}\">E </span>\n"
"                                    <span class=\"oe_grey\" attrs=\"{'invisible':[('partner_longitude','&gt;=',0)]}\">W </span>\n"
"                                    <span class=\"oe_grey ps-1\">) </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "<span class=\"oe_grey\">( </span>"
msgstr "<span class=\"oe_grey\">( </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid ""
"<span class=\"text-danger error_partner_assign_desinterested\" "
"style=\"display:none;\">You need to fill up the next action and contact the "
"customer before accepting the lead</span>"
msgstr ""
"<span class=\"text-danger error_partner_assign_desinterested\" "
"style=\"display:none;\">Trebuie să completați următoarea acțiune și să "
"contactați clientul înainte de a accepta potențialul</span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid ""
"<span class=\"text-danger error_partner_assign_interested\" "
"style=\"display:none;\">You need to fill up the next action and contact the "
"customer before accepting the lead</span>"
msgstr ""
"<span class=\"text-danger error_partner_assign_interested\" "
"style=\"display:none;\">Trebuie să completați următoarea acțiune și să "
"contactați clientul înainte de a accepta potențialul</span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<span class=\"text-muted\"> - </span>"
msgstr "<span class=\"text-muted\"> - </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<span> at </span>"
msgstr "<span> at </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-3\">Address</strong>"
msgstr "<strong class=\"col-12 col-sm-3\">Adresă</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-3\">Customer</strong>"
msgstr "<strong class=\"col-12 col-sm-3\">Client</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Expected Closing</strong>"
msgstr "<strong class=\"col-12 col-sm-4\">Închidere preconizată</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Next Activity</strong>"
msgstr "<strong class=\"col-12 col-sm-4\">Următoarea activitate</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Priority</strong>"
msgstr "<strong class=\"col-12 col-sm-4\">Prioritate</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong>Message and communication history</strong>"
msgstr "<strong>Istoric mesaje și comunicare</strong>"

#. module: website_crm_partner_assign
#: model:mail.template,body_html:website_crm_partner_assign.email_template_lead_forward_mail
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your leads</span><br>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            Hello,<br>\n"
"                            We have been contacted by those prospects that are in your region. Thus, the following leads have been assigned to <t t-out=\"ctx['partner_id'].name or ''\"></t>:<br>\n"
"                            <ol>\n"
"                                <li t-foreach=\"ctx['partner_leads']\" t-as=\"lead\"><a t-att-href=\"lead['lead_link']\" t-out=\"lead['lead_id'].name or 'Subject Undefined'\">Subject Undefined</a>, <t t-out=\"lead['lead_id'].partner_name or lead['lead_id'].contact_name or 'Contact Name Undefined'\">Contact Name Undefined</t>, <t t-out=\"lead['lead_id'].country_id and lead['lead_id'].country_id.name or 'Country Undefined'\">Country Undefined</t>, <t t-out=\"lead['lead_id'].email_from or 'Email Undefined' or ''\">Email Undefined</t>, <t t-out=\"lead['lead_id'].phone or ''\">******-123-4567</t> </li><br>\n"
"                            </ol>\n"
"                            <t t-if=\"ctx.get('partner_in_portal')\">\n"
"                                Please connect to your <a t-att-href=\"'%s?db=%s' % (object.get_base_url(), object.env.cr.dbname)\">Partner Portal</a> to get details. On each lead are two buttons on the top left corner that you should press after having contacted the lead: \"I'm interested\" &amp; \"I'm not interested\".<br>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                You do not have yet a portal access to our database. Please contact\n"
"                                <t t-out=\"ctx['partner_id'].user_id and ctx['partner_id'].user_id.email and 'your account manager %s (%s)' % (ctx['partner_id'].user_id.name,ctx['partner_id'].user_id.email) or 'us'\">us</t>.<br>\n"
"                            </t>\n"
"                            The lead will be sent to another partner if you do not contact the lead before 20 days.<br><br>\n"
"                            Thank you,<br>\n"
"                            <t t-out=\"ctx['partner_id'].user_id and ctx['partner_id'].user_id.signature or ''\"></t>\n"
"                            <br>\n"
"                            <t t-if=\"not ctx['partner_id'].user_id\">\n"
"                                PS: It looks like you do not have an account manager assigned to you, please contact us.\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"user.company_id.phone and (user.company_id.email or user.company_id.website)\">|</t>\n"
"                    <a t-if=\"user.company_id.email\" t-att-href=\"'mailto:%s' % user.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                    <t t-if=\"user.company_id.email and user.company_id.website\">|</t>\n"
"                    <a t-if=\"user.company_id.website\" t-att-href=\"'%s' % user.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__activation
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__activation
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__activation
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_tree
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_view_search
msgid "Activation"
msgstr "Activare"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__active
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__active
#, python-format
msgid "Active"
msgstr "Activ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Add an opportunity"
msgstr "Adăugați o oportunitate"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Address"
msgstr "Adresa"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#, python-format
msgid "All Categories"
msgstr "Toate categoriile"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Toate țările"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
#, python-format
msgid "All fields are required !"
msgstr "Toate câmpurile sunt necesare!"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_view_search
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_grade_view_search
msgid "Archived"
msgstr "Arhivat"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_assigned_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__partner_assigned_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_opportunity_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Assigned Partner"
msgstr "Partenerul Alocat"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "Assigned Partner:"
msgstr "Partener atribuit:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Automatic Assignment"
msgstr "Alocare automată"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead_forward_to_partner__body
msgid "Automatically sanitized HTML contents"
msgstr "Conținuturi HTML cenzurate automat"

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_data_bronze
msgid "Bronze"
msgstr "Bronz"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_partner_report_assign
msgid "CRM Partnership Analysis"
msgstr "Analiza parteneriatului CRM"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Campaign"
msgstr "Campanie"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__can_publish
msgid "Can Publish"
msgstr "Poate Publica"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Cancel"
msgstr "Anulează"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "City"
msgstr "Localitate"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid "Close"
msgstr "Închide"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Confirm"
msgstr "Confirmă"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Contact"
msgstr "Contact"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#, python-format
msgid "Contact Name"
msgstr "Numele Contactului"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Contact a reseller"
msgstr "Contactați un revânzător"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Contact name"
msgstr "Nume contact"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__body
msgid "Contents"
msgstr "Conținut"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__country_id
msgid "Country"
msgstr "Țară"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Create Opportunity"
msgstr "Creează oportunitate"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_activation_act
msgid "Create a Partner Activation"
msgstr "Creați o activare de partener"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_grade_action
msgid "Create a Partner Level"
msgstr "Creați un nivel de partener"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: website_crm_partner_assign
#: model:crm.tag,name:website_crm_partner_assign.tag_portal_lead_own_opp
msgid "Created by Partner"
msgstr "Creat de Partener"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__create_date
msgid "Created on"
msgstr "Creat în"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Current stage of the opportunity"
msgstr "Stadiul curent al oportunității"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Customer"
msgstr "Client"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Customer Name"
msgstr "Nume client"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Date"
msgstr "Dată"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Date Partnership"
msgstr "Data Parteneriatului"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Date Review"
msgstr "Verificarea Datei"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Description"
msgstr "Descriere"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Details Next Activity"
msgstr "Detalii Activitate următoare"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Edit Contact"
msgstr "Editare contact"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Edit Opportunity"
msgstr "Editare oportunitate"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Email"
msgstr "Email"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Email Template"
msgstr "Șablon email"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Expected"
msgstr "Estimat"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Expected Closing"
msgstr "Închidere estimată"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Expected Closing:"
msgstr "Închidere estimată:"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#, python-format
msgid "Expected Revenue"
msgstr "Venit estimate"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Country"
msgstr "Filtrează după țară"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Level"
msgstr "Filtrează după nivel"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__partner_id
msgid "Forward Leads To"
msgstr "Transmiteți potențialii clienți către"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__forward_type
msgid "Forward selected leads to"
msgstr "Trimiteți potențialii clienți selectați către"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.crm_lead_forward_to_partner_act
msgid "Forward to Partner"
msgstr "Redirectioneaza catre partener"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_crm_send_mass_forward
msgid "Forward to partner"
msgstr "Transmite partenerului"

#. module: website_crm_partner_assign
#: model:mail.template,subject:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Fwd: Lead: {{ ctx['partner_id'].name }}"
msgstr "Fwd: Lead: {{ ctx['partner_id'].name }}"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_latitude
msgid "Geo Latitude"
msgstr "Geo Latitudine"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_longitude
msgid "Geo Longitude"
msgstr "Geo Longitudine"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Geolocation"
msgstr "Geolocation"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "Geolocation:"
msgstr "Geolocalizare:"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner_grade__partner_weight
msgid ""
"Gives the probability to assign a lead to this partner. (0 means no "
"assignment.)"
msgstr ""
"Indică probabilitatea de a atribui un potențial client acestui partener. (0 "
"înseamnă nicio atribuire.)"

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_data_gold
msgid "Gold"
msgstr "Aur"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__grade_id
msgid "Grade"
msgstr "Grad"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Group By"
msgstr "Grupează după"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
#, python-format
msgid "I am not interested by this lead. I contacted the lead."
msgstr "Nu sunt interesat de acest potențial client. L-am contactat."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
#, python-format
msgid "I am not interested by this lead. I have not contacted the lead."
msgstr "Nu sunt interesat de acest potențial client. Nu l-am contactat."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "I have contacted the customer"
msgstr "Am contactat clientul"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__id
msgid "ID"
msgstr "ID"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__implemented_partner_ids
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__implemented_partner_ids
msgid "Implementation References"
msgstr "Referințe implementare"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__implemented_partner_count
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__implemented_partner_count
msgid "Implemented Partner Count"
msgstr "Număr de parteneri implementați"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__assigned_partner_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__assigned_partner_id
msgid "Implemented by"
msgstr "Implementat de"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date
msgid "Invoice Account Date"
msgstr "Dată cont factură"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__is_published
msgid "Is Published"
msgstr "Este Publicat"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation____last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner____last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign____last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation____last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead__date_partner_assign
msgid "Last date this case was forwarded/assigned to a partner"
msgstr ""
"Ultima data cand acest caz a fost redirectionat/atribuit unui partener"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date_review
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_review
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__date_review
msgid "Latest Partner Review"
msgstr "Verificarea cea mai Recenta a Partenerului"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_id
msgid "Lead"
msgstr "Pistă"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Lead -"
msgstr "Potențial client -"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_assignation
msgid "Lead Assignation"
msgstr "Atribuirea potențiali clienți"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Lead Feedback"
msgstr "Feedback potențial client"

#. module: website_crm_partner_assign
#: model:mail.template,name:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Lead Forward: Send to partner"
msgstr "Forward potențial client: Trimite către partener"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_location
msgid "Lead Location"
msgstr "Locație potențial client"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_forward_to_partner
msgid "Lead forward to partner"
msgstr "Transmiteți potențialul client către partener"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Pista/Oportunitate"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_menu_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "Leads"
msgstr "Piste"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
msgid "Level"
msgstr "Nivel"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__name
msgid "Level Name"
msgstr "Nume nivel"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__partner_weight
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__partner_weight
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__partner_weight
msgid "Level Weight"
msgstr "Etichetă masă"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_link
msgid "Link to Lead"
msgstr "Link către potențialul client"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Looking For a Local Store?"
msgstr "Căutați un magazin local?"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#, python-format
msgid "Lost"
msgstr "Pierdut(ă)"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Medium"
msgstr "Mediu"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Mobile"
msgstr "Mobil"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_opportunity_partner_filter
msgid "My Assigned Partners"
msgstr "Partenerii alocați mie"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__name
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#, python-format
msgid "Name"
msgstr "Nume"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "New Opportunity"
msgstr "Oportunitate nouă"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#, python-format
msgid "Newest"
msgstr "Cele mai noi"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Next Activity"
msgstr "Următoarea activitate"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Next Activity Date"
msgstr "Data activității urmatoare"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_review_next
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__date_review_next
msgid "Next Partner Review"
msgstr "Urmatoarea Verificare a Partenerului"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.action_report_crm_partner_assign
msgid "No data yet!"
msgstr "Nu există date încă!"

#. module: website_crm_partner_assign
#: model:crm.tag,name:website_crm_partner_assign.tag_portal_lead_partner_unavailable
msgid "No more partner available"
msgstr "Nu mai există partener disponibil"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "No result found"
msgstr "Niciun rezultat găsit"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
#, python-format
msgid "Not allowed to update the following field(s) : %s."
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_menu_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Opportunities"
msgstr "Oportunități"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_graph
msgid "Opportunities Assignment Analysis"
msgstr "Analiza Atribuirii Oportunitatilor"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Opportunity"
msgstr "Oportunitate"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.partner
msgid "Our Partners"
msgstr "Partenerii noștri"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#, python-format
msgid "Overdue Activities"
msgstr "Activități restante"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__partner_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Partner"
msgstr "Partener"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_activation
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_view_search
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_partner_assign_form
msgid "Partner Activation"
msgstr "Activare Partener"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.res_partner_activation_act
#: model:ir.ui.menu,name:website_crm_partner_assign.res_partner_activation_config_mi
msgid "Partner Activations"
msgstr "Activari Partener"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__forward_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__assignation_lines
msgid "Partner Assignment"
msgstr "Atribuire Partener"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__date_partner_assign
msgid "Partner Assignment Date"
msgstr "Dată asignare partener"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "Partner Assignment Date:"
msgstr "Dată asignare partener:"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_grade
msgid "Partner Grade"
msgstr "Grad Partener"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__grade_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__grade_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_tree
msgid "Partner Level"
msgstr "Nivel Partener"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.res_partner_grade_action
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_res_partner_grade_action
msgid "Partner Levels"
msgstr "Niveluri de parteneriat"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_grade_action
msgid ""
"Partner Levels allow you to rank your Partners based on their performances."
msgstr ""
"Nivelurile partenerilor vă permit să vă clasificați partenerii în funcție de"
" performanțele lor."

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__partner_location
msgid "Partner Location"
msgstr "Locații partener"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_partner_assign_form
msgid "Partner Review"
msgstr "Verificare Partener"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Partner assigned Analysis"
msgstr "Analiza Partener alocat"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_declined_ids
msgid "Partner not interested"
msgstr "Partener neinteresat"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead__partner_assigned_id
msgid "Partner this case has been forwarded/assigned to."
msgstr "Partenerul caruia i-a fost redirectionat/atribuit cazul."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "Partners Page"
msgstr "Pagina partenerilor"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_report_crm_partner_assign
msgid "Partnership Analysis"
msgstr "Analiza Parteneriatului"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date_partnership
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_partnership
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__date_partnership
msgid "Partnership Date"
msgstr "Data Parteneriatului"

#. module: website_crm_partner_assign
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_report_crm_partner_assign_tree
msgid "Partnerships"
msgstr "Parteneriate"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Phone"
msgstr "Telefon"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Planned Revenue"
msgstr "Venitul planificat"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Priority:"
msgstr "Prioritate:"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
#, python-format
msgid "Probability"
msgstr "Probabilitate"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating"
msgstr "Evaluare"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Rating: #{lead.priority} on 3"
msgstr "Rating: #{lead.priority} pe 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: #{opportunity.priority} on 4"
msgstr "Rating: #{opportunity.priority} pe 4"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 0 on 3"
msgstr "Evaluare: 0 pe 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 1 on 3"
msgstr "Evaluare: 1 pe 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 2 on 3"
msgstr "Evaluare: 2 pe 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 3 on 3"
msgstr "Evaluare: 3 pe 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.references_block
msgid "References"
msgstr "Referințe"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/website.py:0
#: model:ir.ui.menu,name:website_crm_partner_assign.crm_menu_resellers
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.layout
#, python-format
msgid "Resellers"
msgstr "Revânzători"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__team_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Sales Team"
msgstr "Echipa de vânzări"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Salesperson"
msgstr "Agent de vânzări"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Search"
msgstr "Caută"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_grade_view_search
msgid "Search Partner Grade"
msgstr "Gradul partenerului de căutare"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Send"
msgstr "Trimite"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Send Email"
msgstr "Trimite email"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Send Mail"
msgstr "Trimite email"

#. module: website_crm_partner_assign
#: model:mail.template,description:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Sent to partner when a lead has been assigned to him"
msgstr "Trimis partenerului atunci când i s-a atribuit un potențial client "

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__grade_sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__grade_sequence
msgid "Sequence"
msgstr "Secvență"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:0
#, python-format
msgid "Set an email address for the partner %s"
msgstr "Setați o adresă de e-mail pentru partener %s"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:0
#, python-format
msgid "Set an email address for the partner(s): %s"
msgstr "Setați o adresă de e-mail pentru partener(i): %s"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "Show Leads / Opps"
msgstr "Afișați Lead-urile / Oportunitățile"

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_data_silver
msgid "Silver"
msgstr "Argint"

#. module: website_crm_partner_assign
#: model:crm.tag,name:website_crm_partner_assign.tag_portal_lead_is_spam
msgid "Spam"
msgstr "Spam"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#, python-format
msgid "Stage"
msgstr "Etapă"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Street"
msgstr "Stradă"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Street2"
msgstr "Strada2"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Tags"
msgstr "Etichete"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:0
#, python-format
msgid "The Forward Email Template is not in the database"
msgstr "Șablonul Forward Email nu este în baza de date "

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner_grade__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"URL-ul complet pentru accesarea documentului prin intermediul site-ului web."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "There are no leads."
msgstr "Nu există niciun potențial client"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "There are no opportunities."
msgstr "Nu  există oportunități."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
#, python-format
msgid "There is no country set in addresses for %(lead_names)s."
msgstr "Nu există nicio țară setată în adrese pentru %(lead_names)s."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#, python-format
msgid "This Week Activities"
msgstr "Activități în săpt. curentă"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "This lead is a spam"
msgstr "Acest potențial client este un spam"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner__partner_weight
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_users__partner_weight
msgid ""
"This should be a numerical value greater than 0 which will decide the "
"contention for this partner to take this lead/opportunity."
msgstr ""
"Aceasta ar trebui să fie o valoare numerică mai mare decât 0, care va decide"
" în ce măsură acest partener va prelua acest lead/oportunitate."

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_activation_act
msgid ""
"Those are used to know where your Partners stand in your onboarding process."
msgstr ""
"Acestea sunt utilizate pentru a ști unde se află partenerii dvs. în procesul"
" de îmbarcare."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#, python-format
msgid "Today Activities"
msgstr "Activitățile de astăzi"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__turnover
msgid "Turnover"
msgstr "Cifră de afaceri"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__user_id
msgid "User"
msgstr "Operator"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__website_published
msgid "Visible on current website"
msgstr "Vizibil pe site-ul curent"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
#, python-format
msgid "Warning"
msgstr "Atenție"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_website
msgid "Website"
msgstr "Pagină web"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__website_url
msgid "Website URL"
msgstr "URL website"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "What is the next action? When? What is the expected revenue?"
msgstr "Care este următoarea acțiune? Cand? Care este venitul scontat?"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Why aren't you interested in this lead?"
msgstr "De ce nu vă interesează acest lead?"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#, python-format
msgid "Won"
msgstr "Câștigat(ă)"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "World Map"
msgstr "Harta lumii"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "ZIP"
msgstr "Cod poștal"

#. module: website_crm_partner_assign
#: model:ir.model.fields.selection,name:website_crm_partner_assign.selection__crm_lead_forward_to_partner__forward_type__single
msgid "a single partner: manual selection of partner"
msgstr "un singur partener: selectarea manuală a partenerului"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "at"
msgstr "la"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
msgid "e.g. Gold Partner"
msgstr "e.g. Partener Gold"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "latitude,"
msgstr "latitudine,"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "longitude"
msgstr "longitudine"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "on"
msgstr "pe"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "reference(s)"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields.selection,name:website_crm_partner_assign.selection__crm_lead_forward_to_partner__forward_type__assigned
msgid ""
"several partners: automatic assignment, using GPS coordinates and partner's "
"grades"
msgstr ""
"mai mulți parteneri: asignare automată, folosind coordonatele GPS și "
"evaluările partenerilor"
