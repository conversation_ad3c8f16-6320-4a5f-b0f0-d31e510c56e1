<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="fp_local" model="account.fiscal.position.template">
        <field name="sequence">10</field>
        <field name="chart_template_id" ref="l10n_ec_ifrs"/>
        <field name="name">Régimen nacional</field>
        <field name="auto_apply" eval="True"/>
        <field name="vat_required" eval="True"/>
        <field name="country_id" ref="base.ec"/> 
    </record>
    <record id="fp_foreign" model="account.fiscal.position.template">
        <field name="sequence">20</field>
        <field name="chart_template_id" ref="l10n_ec_ifrs"/>
        <field name="name">Régimen extranjero</field>
        <field name="auto_apply" eval="True"/>
        <field name="vat_required" eval="True"/>
    </record>
    <record id="fp_foreign_sale_vat15_goods" model="account.fiscal.position.tax.template">
        <field name="tax_src_id" ref="l10n_ec.tax_vat_15_411_goods"/>
        <field name="tax_dest_id" ref="l10n_ec.tax_vat_417"/>
        <field name="position_id" ref="fp_foreign"/>
    </record>
    <record id="fp_foreign_sale_vat15_services" model="account.fiscal.position.tax.template">
        <field name="tax_src_id" ref="l10n_ec.tax_vat_15_411_services"/>
        <field name="tax_dest_id" ref="l10n_ec.tax_vat_417"/>
        <field name="position_id" ref="fp_foreign"/>
    </record>
    <record id="fp_foreign_sale_vat12_goods" model="account.fiscal.position.tax.template">
        <field name="tax_src_id" ref="l10n_ec.tax_vat_411_goods"/>
        <field name="tax_dest_id" ref="l10n_ec.tax_vat_417"/>
        <field name="position_id" ref="fp_foreign"/>
    </record>
    <record id="fp_foreign_sale_vat12_services" model="account.fiscal.position.tax.template">
        <field name="tax_src_id" ref="l10n_ec.tax_vat_411_services"/>
        <field name="tax_dest_id" ref="l10n_ec.tax_vat_417"/>
        <field name="position_id" ref="fp_foreign"/>
    </record>
    <record id="fp_foreign_sale_vat0_goods" model="account.fiscal.position.tax.template">
        <field name="tax_src_id" ref="l10n_ec.tax_vat_415_goods"/>
        <field name="tax_dest_id" ref="l10n_ec.tax_vat_417"/>
        <field name="position_id" ref="fp_foreign"/>
    </record>
    <record id="fp_foreign_sale_vat0_services" model="account.fiscal.position.tax.template">
        <field name="tax_src_id" ref="l10n_ec.tax_vat_415_services"/>
        <field name="tax_dest_id" ref="l10n_ec.tax_vat_417"/>
        <field name="position_id" ref="fp_foreign"/>
    </record>    
</odoo>
