# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>gaux, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# alenafairy, 2023
# Collex100, 2024
# <AUTHOR> <EMAIL>, 2024
# Смородин Даниил, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-06 20:36+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"<p class=\"o_view_nocontent_smiling_face\"> Запишите табели </p><p> Вы можете зарегистрировать и отслеживать ваши рабочие часы по проекту каждый день. Каждый час, потраченный на проект, будет переведен в стоимость и может быть выставлен в счете клиентам, если нужно. </p>"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice___candidate_orders
msgid " Candidate Orders"
msgstr " Приказы для кандидатов"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__billable_percentage
msgid ""
"% of timesheets that are billable compared to the total number of timesheets"
" linked to the AA of the project, rounded to the unit."
msgstr ""
"% табелей, которые подлежат оплате по отношению к общему количеству табелей,"
" связанных с аналитическим счетом проекта, с округлением до единицы."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "%(amount)s %(label)s will be added to the new Sales Order."
msgstr "%(amount)s%(label)s будут добавлены к новому заказу на продажу."

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid ", for a revenue of"
msgstr ", при выручке"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid ", leading to a"
msgstr ", приводящий к"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Time Billing</span>"
msgstr "<span class=\"o_form_label\">Выставления счета по времени</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "Зарегистрировано"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoiced:</strong>"
msgstr "<strong>Выставлен счет:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoices:</strong>"
msgstr "<strong>Счета:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Sales Order:</strong>"
msgstr "<strong>Заказ на продажу:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>To invoice:</strong>"
msgstr "<strong>К оплате:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Profitability</u>"
msgstr "<u>Доходность</u>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Sold</u>"
msgstr "<u>Продано</u>"

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid "About us"
msgstr "О нас"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Согласно настройкам для товаров, отгруженное количество может автоматически вычисляться с помощью следующих механизмов:\n"
"- Вручную: количество устанавливается вручную\n"
"- Аналитика по расходам: количество рассчитывается на основе счетов\n"
"- Табель: количество рассчитывается как сумма часов, затраченных на все задачи по этой позиции\n"
"- Складские перемещения: количество заполняют на складе при отгрузке\n"

#. module: sale_timesheet
#: model:project.project,name:sale_timesheet.project_support
msgid "After-Sales Services"
msgstr "Послепродажное обслуживание"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__allocated_hours
msgid "Allocated Hours"
msgstr "Выделенные часы"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr "Сумма к выставлению в счете"

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_create_sale_order_line_unique_employee_per_wizard
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""
"Работника нельзя выбрать более одного раза в сопоставлении. Удалите "
"дубликаты и повторите попытку."

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Позиция аналитики"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Строки аналитики"

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"As a leading professional services firm,\n"
"                                we know that success is all about the\n"
"                                commitment we put on strong services."
msgstr ""
"Как ведущая компания профессиональных услуг, мы знаем, что успех - это об "
"обязательствах, которые мы предоставляем с сильными услугами."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "At least one line should be filled."
msgstr "Необходимо заполнить хотя одну строку."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid "Based on Timesheets"
msgstr "На основе табелей"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__allow_billable
msgid "Billable"
msgstr "Подлежит оплате"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Billable Hours"
msgstr "Оплачиваемые часы"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billable_percentage
msgid "Billable Percentage"
msgstr "Оплачиваемый процент"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
msgid "Billable Type"
msgstr "оплачиваемый тип"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_manual
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_manual
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed Manually"
msgstr "Расчет вручную"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr "Расчет по фиксированной ставке"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_fixed
msgid "Billed at a Fixed price"
msgstr "Расчет по фиксированной ставке"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_milestones
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_milestones
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Milestones"
msgstr "Расчет по вехам"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr "Расчет по табелям"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr "Выставление счетов"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billing Type"
msgstr "Тип выставления счета"

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Type"
msgstr "По типу расчета"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Cancel"
msgstr "Отмена"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr "Выберите заказ на продажу для выставления счета"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__commercial_partner_id
msgid "Commercial Entity"
msgstr "Коммерческая организация"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__commercial_partner_id
msgid "Commercial Partner"
msgstr "Коммерческий партнер"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__company_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__company_id
msgid "Company"
msgstr "Компания"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr "Настройте сервисы"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "Cost"
msgstr "Стоимость"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost_currency_id
msgid "Cost Currency"
msgstr "Валюта стоимости"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Invoice"
msgstr "Добавить счет"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr "Создайте акт по проекту"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order_line
msgid "Create SO Line from project"
msgstr "Создайте строку заказа на продажу из проекта"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order
msgid "Create SO from project"
msgstr "Создайте заказ на продажу из проекта"

#. module: sale_timesheet
#. odoo-javascript
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
#, python-format
msgid "Create Sales Order"
msgstr "Создайте заказ на продажу"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr "Создайте заказ на продажу из проекта"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Create a Sales Order"
msgstr "Создайте заказ на продажу"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr "Создал"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr "Дата создания"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__partner_id
msgid "Customer"
msgstr "Клиент"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr "Обслуживание клиентов (Подписка часа)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "Customer Ratings"
msgstr "Оценки клиента"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__partner_id
msgid "Customer of the sales order"
msgstr "Клиент заказ на продажу"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Ordered,"
msgstr "Заказанные дни,"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Remaining)"
msgstr "дней осталось)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Default Service"
msgstr "Услуга по умолчанию"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__display_create_order
msgid "Display Create Order"
msgstr "Показать Создать заказ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "Draft Invoice"
msgstr "Черновик счета"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Effective"
msgstr "эффективность"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__employee_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "Сотрудник"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__employee_rate
msgid "Employee rate"
msgstr "Ставка сотрудника"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__employee_id
msgid "Employee that has timesheets on the project."
msgstr "Сотрудник, который имеет табели на проекте."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
msgid "End Date"
msgstr "Дата окончания"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr "Фиксированная стоимость услуг"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Furniture Delivery (Manual)"
msgstr "Доставка мебели (вручную)"

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"Great quotation templates will significantly\n"
"                                <strong>boost your success rate</strong>. The\n"
"                                first section is usually about your company,\n"
"                                your references, your methodology or\n"
"                                guarantees, your team, SLA, terms and conditions, etc."
msgstr ""
"Великолепные шаблоны коммерческих предложений значительно\n"
"                                <strong>увеличивают процент успеха</strong>.\n"
"                                Первый раздел, как правило, о вашей компании,\n"
"                                ваши ссылки, ваша методология или\n"
"                                гарантии, ваша команда, условия уровня обслуживания и т.д."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__has_displayed_warning_upsell
msgid "Has Displayed Warning Upsell"
msgstr "Отображается предупреждение о повышении цены"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__has_multi_sol
msgid "Has Multi Sol"
msgstr "Имеет множественное решение"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_cost
msgid "Hourly Cost"
msgstr "Почасовая стоимость"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Ordered,"
msgstr "Заказанные часы,"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Remaining)"
msgstr "Оставшиеся часы)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr "Идентификатор"

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"If you edit a quotation from the 'Preview' of a quotation, you will\n"
"                        update that quotation only. If you edit the quotation\n"
"                        template (from the Configuration menu), all future quotations will\n"
"                        use this modified template."
msgstr ""
"При редактировании коммерческого предложения из 'Предварительного "
"просмотра', вы обновите только это коммерческое предложение. При "
"редактировании шаблона предложения (меню Настройка), все будущие предложения"
" будут использовать этот измененный шаблон."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__info_invoice
msgid "Info Invoice"
msgstr "Инфо счет"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_account_move
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
#, python-format
msgid "Invoice"
msgstr "Акт"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Invoice Policy"
msgstr "Политика выставления счетов"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity) on projects or tasks you'll"
" create later on."
msgstr ""
"Акт на основе табелей (количество выполненных работ) по проектам или "
"задачам, которые вы создадите позже."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create a project for "
"the order with a task for each sales order line to track the time spent."
msgstr ""
"Выставляйте счета на основе табелей учета рабочего времени (поставленное "
"количество) и создайте проект для заказа с задачей для каждой строки заказа "
"на продажу, чтобы отслеживать потраченное время."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create a task in an "
"existing project to track the time spent."
msgstr ""
"Выставляйте счета на основе табелей учета рабочего времени (количество "
"поставленного товара) и создавайте задачу в существующем проекте, чтобы "
"отслеживать потраченное время."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create an empty "
"project for the order to track the time spent."
msgstr ""
"Выставляйте счета на основе табелей учета рабочего времени (поставленное "
"количество) и создавайте пустой проект для заказа, чтобы отслеживать "
"потраченное время."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr "Акт на основе табеля"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form_simplified_inherit
msgid "Invoice your time and material to customers"
msgstr "Выставляйте счета клиентам за потраченное время и материалы"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Invoices"
msgstr "Акты"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr "Выставление счетов"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__invoicing_timesheet_enabled
msgid "Invoicing Timesheet Enabled"
msgstr "Счет-фактура Временная таблица включена"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__is_cost_changed
msgid "Is Cost Manually Changed"
msgstr "Стоимость изменяется вручную"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr "Чистая карта проекта"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__is_so_line_edited
msgid "Is Sales Order Item Manually Edited"
msgstr "Позиция в заказе изменяется вручную"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entry"
msgstr "Запись журнала"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr "Элемент журнала"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr "Младший архитектор (акт на основе табеля)"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid ""
"Keep track of your working hours by project every day and bill your "
"customers for that time."
msgstr ""

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_milestones_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr "Кухонная сборник (вехи)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__line_ids
msgid "Lines"
msgstr "Строк"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Задавайте количество в заказе вручную: акт на основе количества, введенного вручную, без создания аналитического счета.\n"
"Табели учета рабочего времени в контракте: счет на основе записанных часов в соответствующем табеле.\n"
"Создавайте задачи и вносите часы: Создайте задачу при подтверждении заказа и вносите в нее рабочие часы."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__margin
msgid "Margin"
msgstr "Наценка"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Materials"
msgstr "Материалы"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Метод обновления доставленной количества"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr "Сервисные центры"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Invoice"
msgstr "Нет счёта"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Sales Order"
msgstr "Как нет заказов на продажу"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Sales Order Item"
msgstr "Позиция заказа не обнаружена"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid "No activities found"
msgstr "Действий не обнаружено"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "No data yet!"
msgstr "Данных пока нет!"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid "No timesheets found. Let's create one!"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__non_billable
msgid "Non Billable Tasks"
msgstr "Задача без оплаты"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non-Billable"
msgstr "Расчету не подлежит"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Non-billable Hours"
msgstr "Не оплачиваемые часы"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
msgid "Number of hours spent multiplied by the unit price per hour/day."
msgstr "Количество затраченных часов, умноженное на цену за час/день."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Number of hours/days linked to a SOL."
msgstr "Количество часов/дней, связанных с SOL."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Number of hours/days not linked to a SOL."
msgstr "Количество часов/дней, не связанных с SOL."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_count
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_count
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_count
msgid "Number of timesheets"
msgstr "количество табелей"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"Only timesheets not yet invoiced (and validated, if applicable) from this "
"period will be invoiced. If the period is not indicated, all timesheets not "
"yet invoiced (and validated, if applicable) will be invoiced without "
"distinction."
msgstr ""
"Только неоплаченные табели (и подтвержденные, если требуется) за этот период"
" будут подлежать оплате."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Operation not supported"
msgstr "Операция не поддерживается"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__order_id
msgid "Order Reference"
msgstr "Ссылка на заказ"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_costs
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_costs
msgid "Other costs"
msgstr "Прочие расходы"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_revenues
msgid "Other revenues"
msgstr "Прочие доходы"

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid "Our Offer"
msgstr "Наше предложение"

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid "Our Quality"
msgstr "Наше качество"

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid "Our Service"
msgstr "Наш сервис"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_upsell_threshold
msgid ""
"Percentage of time delivered compared to the prepaid amount that must be "
"reached for the upselling opportunity activity to be triggered."
msgstr ""
"Процент времени, затраченного на доставку, по сравнению с суммой предоплаты,"
" которая должна быть достигнута, чтобы запустилась активность по созданию "
"возможности повышения продаж."

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid "Price"
msgstr "Цена"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__pricing_type
msgid "Pricing"
msgstr "Стоимость"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product"
msgstr "Продукт"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__product_id
msgid ""
"Product of the sales order item. Must be a service invoiced based on "
"timesheets on tasks."
msgstr ""
"Товар позиции заказа на продажу. Должен быть выставлен счет на основе "
"табелей задач."

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"Product quality is the foundation we\n"
"                                stand on; we build it with a relentless\n"
"                                focus on fabric, performance and craftsmanship."
msgstr ""
"Качество продукции является нашей основой; мы строим ее с непрерывным "
"фокусом на производстве, производительности и мастерства."

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__project_id
msgid "Project"
msgstr "Проект"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "Строка продаж проекта, сопоставление сотрудников"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr "шаблон проекта"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_update
msgid "Project Update"
msgstr "Обновление проекта"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__project_id
msgid "Project for which we are creating a sales order"
msgstr "Проект, для которого мы создаем заказ на продажу"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__fixed_rate
msgid "Project rate"
msgstr "Ставка проекта"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr "Проект, чтобы сделать оплату"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Quotation"
msgstr "Коммерческое предложение"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Remaining"
msgstr "Оставшиеся"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
msgid "Remaining Days on SO"
msgstr "Оставшиеся дни на заказ"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "Remaining Days on SO:"
msgstr "Осталось дней на заказ:"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_available
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "Доступно оставшихся часов"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
msgid "Remaining Hours on SO"
msgstr "Оставшиеся часы на заказ"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "Remaining Hours on SO:"
msgstr "Оставшиеся часы на заказ:"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid ""
"Review your timesheets by billing type and make sure your time is billable."
msgstr ""
"Проверьте свои табели по расчетному типу и убедитесь в том, что ваше время "
"является оплачиваемым."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__so_analytic_account_id
msgid "Sale Order Analytic Account"
msgstr "Аналитический счет заказа на продажу"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_line_employee_ids
msgid "Sale line/Employee map"
msgstr "Строка продажи / сопоставления сотрудника"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Акт на авансовый платеж"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__order_id
#: model:project.project,name:sale_timesheet.so_template_project
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
#, python-format
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__so_line
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__so_line
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
#, python-format
msgid "Sales Order Item"
msgstr "Элемент заказа на продажу"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet_so_button
msgid "Sales Orders"
msgstr "Заказы на продажу"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Sales order item that will be selected by default on the timesheets of the corresponding employee. It bypasses the sales order item defined on the project and the task, and can be modified on each timesheet entry if necessary. In other words, it defines the rate at which an employee's time is billed based on their expertise, skills or experience, for instance.\n"
"If you would like to bill the same service at a different rate, you need to create two separate sales order items as each sales order item can only have a single unit price at a time.\n"
"You can also define the hourly company cost of your employees for their timesheets on this project specifically. It will bypass the timesheet cost set on the employee."
msgstr ""
"Элемент заказа на продажу, который будет выбран по умолчанию в табеле учета рабочего времени соответствующего сотрудника. Он обходит элемент заказа на продажу, определенный для проекта и задачи, и при необходимости может быть изменен в каждой записи табеля учета рабочего времени. Другими словами, он определяет ставку, по которой оплачивается время сотрудника, исходя, например, из его знаний, навыков или опыта.\n"
"Если вы хотите выставить счет за одну и ту же услугу по другой ставке, вам нужно создать два отдельных элемента заказа на продажу, поскольку каждый элемент заказа на продажу может иметь только одну единичную цену за один раз.\n"
"Вы также можете определить почасовую стоимость сотрудников для их табелей учета рабочего времени в этом проекте. Она будет обходить стоимость табеля, установленную для сотрудника."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""
"Элемент заказа на продажу, к которому будет добавлено потраченное время, "
"чтобы выставить счет клиенту. Удалите элемент заказа на продажу, чтобы "
"запись в табеле учета рабочего времени не была платной."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Заказ на продажу, с которым связана задача."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "Поиск в счете"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "Искать в заказах на продажу"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order Item"
msgstr "Искать в позиции заказа на продажу"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr ""
"Выберите проект, не подлежащий оплате, в котором могут быть созданы задания."

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr "Продавайте услуги и выставляйте счета за трудочасы"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr "Старший архитектор (акт на основе табеля)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__product_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Service"
msgstr "Услуга"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__service_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__service_revenues
msgid "Service Revenues"
msgstr "Выручка от услуг"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold_ratio
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold_ratio
msgid "Service Upsell Threshold Ratio"
msgstr "Коэффициент порога повышения продаж услуг"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.time_product_product_template
msgid "Service on Timesheet"
msgstr "Сервис по табелю"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_task__timesheet_product_id
msgid ""
"Service that will be used by default when invoicing the time spent on a "
"task. It can be modified on each task individually by selecting a specific "
"sales order item."
msgstr ""
"Сервис, который будет использоваться по умолчанию при выставлении счета за "
"время, потраченное на выполнение задачи. Его можно изменить для каждой "
"задачи отдельно, выбрав конкретный элемент заказа на продажу."

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
#: model:project.project,label_tasks:sale_timesheet.project_support
msgid "Services"
msgstr "Услуги"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Sold"
msgstr "Продано"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
msgid "Start Date"
msgstr "Дата начала"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
msgid "Task"
msgstr "Задача"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Повтор задачи"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__task_rate
msgid "Task rate"
msgstr "Скорость выполнения задания"

#. module: sale_timesheet
#: model:project.project,label_tasks:sale_timesheet.so_template_project
msgid "Tasks"
msgstr "Задачи"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"The %s product is required by the Timesheets app and cannot be archived nor "
"deleted."
msgstr ""
"Продукт %s необходим для модуля Табели и не может быть ни заархивирован, ни "
"удален."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"The %s product is required by the Timesheets app and cannot be linked to a "
"company."
msgstr ""
"Продукт %s используется модулем «Табели» и не может быть привязан к "
"компании."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid ""
"The Sales Order cannot be created because you did not enter some employees that entered timesheets on this project. Please list all the relevant employees before creating the Sales Order.\n"
"Missing employee(s): %s"
msgstr ""
"Заказ на продажу не может быть создано, поскольку вы не ввели некоторых "
"сотрудников, которые вошли в табеле для этого проекта. Пожалуйста, "
"перечислите всех соответствующих работников, прежде чем создавать заказ на "
"продажу. Отсутствуют сотрудники:%s "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "The cost of the project is now at"
msgstr "Стоимость проекта составляет"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "The project has already a sale order."
msgstr "Проект уже имеет заказ на продажу."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "The project is already linked to a sales order item."
msgstr "Проект уже связан с элементом заказ на продажу."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid ""
"The sales order cannot be created because some timesheets of this project "
"are already linked to another sales order."
msgstr ""
"Заказ на продажу не может быть создано, поскольку некоторые табели этого "
"проекта уже связаны с другим заказом на продажу."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#, python-format
msgid "The selected Sales Order should contain something to invoice."
msgstr ""
"Выбранный заказ на продажу должен содержать что-то для выставления счета."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,help:sale_timesheet.field_project_task__pricing_type
msgid ""
"The task rate is perfect if you would like to bill different services to "
"different customers at different rates. The fixed rate is perfect if you "
"bill a service at a fixed rate per hour or day worked regardless of the "
"employee who performed it. The employee rate is preferable if your employees"
" deliver the same service at a different rate. For instance, junior and "
"senior consultants would deliver the same service (= consultancy), but at a "
"different rate because of their level of seniority."
msgstr ""
"Ставка по заданию - идеальный вариант, если вы хотите выставлять счета за "
"разные услуги разным клиентам по разным ставкам. Фиксированная ставка - "
"идеальный вариант, если вы выставляете счет за услугу по фиксированной "
"ставке за час или день работы, независимо от того, кто ее выполнил. Ставка "
"для сотрудников предпочтительнее, если ваши сотрудники оказывают одну и ту "
"же услугу по разным ставкам. Например, младшие и старшие консультанты будут "
"оказывать одну и ту же услугу (= консультирование), но по разным расценкам в"
" зависимости от уровня их стажа."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__cost
msgid ""
"This cost overrides the employee's default employee hourly wage in "
"employee's HR Settings"
msgstr ""
"Эта оплата перекрывает почасовую оплату труда сотрудника, установленную по "
"умолчанию в его HR-настройках."

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"This is a <strong>sample quotation template</strong>. You should\n"
"                                customize it to fit your own needs from the <i>Sales</i>\n"
"                                application, using the menu: Configuration /\n"
"                                Quotation Templates."
msgstr ""
"Это <strong>шаблон коммерческого предложения.</strong> Вам необходимо "
"настроить его в соответствии с вашими собственных нужд из приложения "
"<i>Продажи</i> используя меню: Настройка / Шаблоны коммерческого "
"предложения."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Threshold"
msgstr "Порог"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr "Услуги, основанные на часах"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
msgid "Timesheet"
msgstr "Табель"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheet Costs"
msgstr "Стоимость табеля"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "Отчетная единица табеля"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__timesheet_product_id
msgid "Timesheet Product"
msgstr "Продукт \"Табель учета рабочего времени"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__timesheet_revenues
msgid "Timesheet Revenues"
msgstr "Доходы от таймшетов"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr "Общая длительность табеля"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_count
msgid "Timesheet activities"
msgstr "Расписание мероприятий"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_ids
msgid "Timesheet activities associated to this sale"
msgstr "Табель мероприятий этой продажи"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order_item
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_account_move
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__timesheet_ids
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
#, python-format
msgid "Timesheets"
msgstr "Табели"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets (Billed Manually)"
msgstr "Табели (расчет вручную)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets (Billed on Milestones)"
msgstr "Табели (расчет по вехам)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets (Billed on Timesheets)"
msgstr "Табели (расчет по табелям)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets (Fixed Price)"
msgstr "Табели (фиксированный расчет)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets (Non Billable)"
msgstr "Табели (не подлежат расчету)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
msgid "Timesheets Analysis"
msgstr "Анализ табеля"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr "Отчет по анализу табеля"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid "Timesheets Period"
msgstr "Период табеля"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets by Billing Type"
msgstr "Табели по типу расчета"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "Timesheets for the"
msgstr "Табели для"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets of %s"
msgstr "Табели %s "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr "Табели проекта (единый тариф по сделке/проекту)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets revenues"
msgstr "Доходы от таймшетов"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__margin
msgid "Timesheets revenues minus the costs"
msgstr "Доходы по таймшиту минус расходы"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr "Табели, которые учитываются при оплате труда"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Timesheets taken when invoicing time spent"
msgstr ""
"Табели учета рабочего времени при выставлении счетов за отработанное время"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Total"
msgstr "Всего"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""
"Общая сумма счета по заказу на продажу, включая все элементы (услуги, "
"товары, затраты, ...)"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_payment__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid ""
"Total recorded duration, expressed in the encoding UoM, and rounded to the "
"unit"
msgstr ""
"Общая длительность записи, которая представлена в единице времени и "
"округлена но целого значения."

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Total:"
msgstr "Итого:"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr "Сервис отслеживания"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Отслеживайте рабочее время по проектам каждый день и выставляйте счета за "
"это время своим клиентам."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "Цена за ед."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__price_unit
msgid "Unit price of the sales order item."
msgstr "Цена за единицу элемента заказ на продажу."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Value does not exist in the pricing type"
msgstr "Значение не существует в типе ценообразования"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_order_portal_content_inherit
msgid "View Timesheets"
msgstr "Посмотреть табели"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Warn the salesperson for an upsell when work done exceeds"
msgstr ""
"Предупредите продавца о возможности повышения цены, если объем выполненной "
"работы превышает норму"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__warning_employee_rate
msgid "Warning Employee Rate"
msgstr "Ставка предупреждающего сотрудника"

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"We always ensure that our products are\n"
"                                set at a fair price so that you will be\n"
"                                happy to buy them."
msgstr ""
"Мы всегда гарантируем, что наши товары установлены по справедливой цене, так"
" что вы будете рады их купить."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__wizard_id
msgid "Wizard"
msgstr "Мастер"

#. module: sale_timesheet
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_1
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_2
#: model_terms:sale.order,website_description:sale_timesheet.sale_order_3
msgid ""
"You can <strong>set a description per product</strong>. Odoo will\n"
"                        automatically create a quotation using the descriptions\n"
"                        of all products in the proposal. The table of content\n"
"                        on the left is generated automatically using the styles you\n"
"                        used in your description (heading 1, heading 2, ...)"
msgstr ""
"Вы можете <strong>установить описание по продукту</strong>. Odoo \n"
"                        автоматически создает коммерческое предложение, используя описания\n"
"                        всех продуктов в предложении. Таблица содержания\n"
"                        слева создается автоматически, используя стили, которые вы\n"
"                        использовали в вашем описании (заглавие 1, заглавие 2, ...)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "You can only apply this action from a project."
msgstr "Это действие можно применить только с проекта."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"You cannot link a billable project to a sales order item that comes from an "
"expense or a vendor bill."
msgstr ""
"Невозможно связать оплачиваемый проект с позицией в заказе, полученной из "
"расходов или счета поставщика."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"You cannot link a billable project to a sales order item that is not a "
"service."
msgstr ""
"Невозможно связать оплачиваемый  проект с позицией в заказе, которая не "
"является услугой."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account.py:0
#, python-format
msgid "You cannot modify timesheets that are already invoiced."
msgstr "Невозможно изменить табели, по которым уже выставлены счета."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account.py:0
#, python-format
msgid "You cannot remove a timesheet that has already been invoiced."
msgstr "Невозможно удалить табель, по которому уже был выставлен счет."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order.py:0
#, python-format
msgid "day"
msgstr "día"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order.py:0
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "days"
msgstr "дней"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "hours"
msgstr "часы"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "margin ("
msgstr "маржа ("

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "of hours sold."
msgstr "количество проданных часов."
