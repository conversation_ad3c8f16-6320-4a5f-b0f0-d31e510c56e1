<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="sale_advance_payment_inv_timesheet_view_form" model="ir.ui.view">
        <field name="name">sale_timesheet.sale.advance.payment.inv.view.form</field>
        <field name="model">sale.advance.payment.inv</field>
        <field name="inherit_id" ref="sale.view_sale_advance_payment_inv"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="disable_autofocus">true</attribute>
            </xpath>
            <group name="down_payment_specification" position="after">
                <field name="invoicing_timesheet_enabled" invisible="1"/>
                <group name="timesheet_invoice_date_range"
                    attrs="{'invisible': [ '|', ('invoicing_timesheet_enabled', '=', False), ('advance_payment_method', '!=', 'delivered')]}">
                    <label for="date_start_invoice_timesheet" string="Timesheets Period" attrs="{'invisible': [ '|', ('invoicing_timesheet_enabled', '=', False), ('advance_payment_method', '!=', 'delivered')]}"/>
                    <div class="o_row" attrs="{'invisible': [ '|',('invoicing_timesheet_enabled', '=', False), ('advance_payment_method', '!=', 'delivered')]}">
                        <field name="date_start_invoice_timesheet"
                            class="oe_inline" widget="daterange"
                            options="{'related_end_date': 'date_end_invoice_timesheet'}"
                            title="Only timesheets not yet invoiced (and validated, if applicable) from this period will be invoiced. If the period is not indicated, all timesheets not yet invoiced (and validated, if applicable) will be invoiced without distinction."/>
                        <i class="fa fa-long-arrow-right mx-2" aria-label="Arrow icon" title="Arrow"/>
                        <field name="date_end_invoice_timesheet"
                            class="oe_inline" widget="daterange"
                            options="{'related_start_date': 'date_start_invoice_timesheet'}"
                            title="Only timesheets not yet invoiced (and validated, if applicable) from this period will be invoiced. If the period is not indicated, all timesheets not yet invoiced (and validated, if applicable) will be invoiced without distinction."/>
                    </div>
                </group>
            </group>
        </field>
    </record>

</odoo>
