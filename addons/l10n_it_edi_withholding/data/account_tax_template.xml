<?xml version='1.0' encoding='UTF-8'?>
<odoo>

    <!-- Withholding Taxes (Ritenute) ......................................................... -->
    <!-- Individuals -->
    <record id="20vwi" model="account.tax.template">
        <field name="description">20% Ritenuta Persone Fisiche</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">20% RIT PF</field>
        <field name="sequence">10</field>
        <field name="amount">-20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_withholding"/>
        <field name="l10n_it_withholding_type">RT01</field>
        <field name="l10n_it_withholding_reason">A</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="l10n_it_edi_withholding.1611"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.1609'),
                'minus_report_expression_ids': [ref('withh_sale_tax_report_it_line_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.1609'),
                'plus_report_expression_ids': [ref('withh_sale_tax_report_it_line_tag')],
            }),
        ]"/>
    </record>

    <record id="20awi" model="account.tax.template">
        <field name="description">20% Ritenuta Persone Fisiche</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">20% RIT PF</field>
        <field name="sequence">10</field>
        <field name="amount">-20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_withholding"/>
        <field name="l10n_it_withholding_type">RT01</field>
        <field name="l10n_it_withholding_reason">A</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="l10n_it_edi_withholding.2603"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2602'),
                'plus_report_expression_ids': [ref('withh_purchase_tax_report_it_line_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2602'),
                'minus_report_expression_ids': [ref('withh_purchase_tax_report_it_line_tag')],
            }),
        ]"/>
    </record>

    <!-- Individual Companies -->
    <record id="20vwc" model="account.tax.template">
        <field name="description">20% Ritenuta Persone Giuridiche</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">20% RIT PG</field>
        <field name="sequence">11</field>
        <field name="amount">-20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_withholding"/>
        <field name="l10n_it_withholding_type">RT02</field>
        <field name="l10n_it_withholding_reason">A</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="l10n_it_edi_withholding.1611"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.1609'),
                'minus_report_expression_ids': [ref('withh_sale_tax_report_it_line_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.1609'),
                'plus_report_expression_ids': [ref('withh_sale_tax_report_it_line_tag')],
            }),
        ]"/>
    </record>

    <record id="20awc" model="account.tax.template">
        <field name="description">20% Ritenuta Persone Giuridiche</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">20% RIT PG</field>
        <field name="sequence">11</field>
        <field name="amount">-20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_withholding"/>
        <field name="l10n_it_withholding_type">RT02</field>
        <field name="l10n_it_withholding_reason">A</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="l10n_it_edi_withholding.2603"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2602'),
                'plus_report_expression_ids': [ref('withh_purchase_tax_report_it_line_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2602'),
                'minus_report_expression_ids': [ref('withh_purchase_tax_report_it_line_tag')],
            }),
        ]"/>
    </record>

    <!-- Agents and Representatives -->
    <record id="23vwo" model="account.tax.template">
        <field name="description">23% Ritenuta Agenti e Rappresentanti</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">23% RIT AG</field>
        <field name="sequence">12</field>
        <field name="amount">-23</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_withholding"/>
        <field name="l10n_it_withholding_type">RT02</field>
        <field name="l10n_it_withholding_reason">ZO</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="l10n_it_edi_withholding.1611"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 50,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.1609'),
                'minus_report_expression_ids': [ref('withh_sale_tax_report_it_line_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 50,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.1609'),
                'plus_report_expression_ids': [ref('withh_sale_tax_report_it_line_tag')],
            }),
        ]"/>
    </record>

    <record id="23awo" model="account.tax.template">
        <field name="description">23% Ritenuta Agenti e Rappresentanti</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">23% RIT AG</field>
        <field name="sequence">12</field>
        <field name="amount">-23</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_withholding"/>
        <field name="l10n_it_withholding_type">RT02</field>
        <field name="l10n_it_withholding_reason">ZO</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="l10n_it_edi_withholding.2603"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 50,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2602'),
                'plus_report_expression_ids': [ref('withh_purchase_tax_report_it_line_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 50,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2602'),
                'minus_report_expression_ids': [ref('withh_purchase_tax_report_it_line_tag')],
            }),
        ]"/>
    </record>

    <!-- INPS ................................................................................. -->
    <record id="4vinps" model="account.tax.template">
        <field name="description">4% Contributo INPS</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">4% INPS</field>
        <field name="sequence">5</field>
        <field name="amount">4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="include_base_amount">True</field>
        <field name="tax_group_id" ref="tax_group_pension_fund"/>
        <field name="l10n_it_pension_fund_type">TC22</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2630'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2630'),
            }),
        ]"/>
    </record>

    <record id="4ainps" model="account.tax.template">
        <field name="description">4% Contributo INPS</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">4% INPS</field>
        <field name="sequence">5</field>
        <field name="amount">4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="include_base_amount">True</field>
        <field name="l10n_it_pension_fund_type">TC22</field>
        <field name="tax_group_id" ref="tax_group_pension_fund"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.4402'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.4402'),
            }),
        ]"/>
   </record>

    <!-- Pension Fund (Cassa Previdenziale) ................................................... -->
    <record id="4vcp" model="account.tax.template">
        <field name="description">4% Contributo Fondo Pensione</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">4% F.Pens.</field>
        <field name="sequence">20</field>
        <field name="amount">4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="include_base_amount">True</field>
        <field name="tax_group_id" ref="tax_group_pension_fund"/>
        <field name="l10n_it_pension_fund_type">TC01</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2630'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2630'),
            }),
        ]"/>
    </record>

    <record id="4acp" model="account.tax.template">
        <field name="description">4% Contributo Fondo Pensione</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">4% F.Pens.</field>
        <field name="sequence">20</field>
        <field name="amount">4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="include_base_amount">True</field>
        <field name="l10n_it_pension_fund_type">TC01</field>
        <field name="tax_group_id" ref="tax_group_pension_fund"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.4402'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.4402'),
            }),
        ]"/>
   </record>

    <!-- ENASARCO ................................................................ -->
    <record id="enasarcov" model="account.tax.template">
        <field name="description">8.5% Ritenuta ENASARCO</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">ENASARCO</field>
        <field name="sequence">13</field>
        <field name="amount">-8.5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_enasarco"/>
        <field name="l10n_it_pension_fund_type">TC07</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="l10n_it_edi_withholding.1611"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2630'),
                'minus_report_expression_ids': [ref('enasarco_sale_tax_report_it_line_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2630'),
                'plus_report_expression_ids': [ref('enasarco_sale_tax_report_it_line_tag')],
            }),
        ]"/>
    </record>

    <record id="enasarcoa" model="account.tax.template">
        <field name="description">8.5% Ritenuta ENASARCO</field>
        <field name="chart_template_id" ref="l10n_it.l10n_it_chart_template_generic"/>
        <field name="name">ENASARCO</field>
        <field name="sequence">13</field>
        <field name="amount">-8.5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_enasarco"/>
        <field name="l10n_it_pension_fund_type">TC07</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="l10n_it_edi_withholding.1611"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2630'),
                'plus_report_expression_ids': [ref('enasarco_purchase_tax_report_it_line_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_it.2630'),
                'minus_report_expression_ids': [ref('enasarco_purchase_tax_report_it_line_tag')],
            }),
        ]"/>
    </record>
</odoo>
