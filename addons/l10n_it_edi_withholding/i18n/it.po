# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_it_edi_withholding
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-26 11:16+0000\n"
"PO-Revision-Date: 2023-01-26 11:16+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_it_edi_withholding
#: model:ir.model,name:l10n_it_edi_withholding.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modello piano dei conti"

#. module: l10n_it_edi_withholding
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_withholding.view_invoice_tree_l10n_it_edi_extended
msgid "All Taxes Included"
msgstr "Tasse incluse"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_bank_statement_line__l10n_it_amount_before_withholding_signed
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_move__l10n_it_amount_before_withholding_signed
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_payment__l10n_it_amount_before_withholding_signed
msgid "All taxes included"
msgstr "Tasse incluse"

#. module: l10n_it_edi_withholding
#. odoo-python
#: code:addons/l10n_it_edi_withholding/models/account_edi_format.py:0
#, python-format
msgid ""
"Bad tax configuration for line %s, there must be one Withholding tax and one"
" Pension Fund tax at max."
msgstr ""
"Configurazione tasse errata nella linea %s, ci devono essere al massimo una "
"Ritenuta ed un Contributo a fondi pensionistici per linea."

#. module: l10n_it_edi_withholding
#. odoo-python
#: code:addons/l10n_it_edi_withholding/models/account_edi_format.py:0
#, python-format
msgid ""
"Bad tax configuration for line %s, there must be one and only one VAT tax "
"per line"
msgstr ""

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc16
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc16
msgid "CASAGIT Additional pension fund for journalists"
msgstr "Cassa autonoma assistenza integrativa giornalisti italiani (CASAGIT)"

#. module: l10n_it_edi_withholding
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_withholding.account_invoice_line_it_FatturaPA_withholding
msgid "CASSA-PREV"
msgstr ""

#. module: l10n_it_edi_withholding
#: model:ir.model,name:l10n_it_edi_withholding.model_account_edi_format
msgid "EDI format"
msgstr "Formato EDI"

#. module: l10n_it_edi_withholding
#: model:account.report.line,name:l10n_it_edi_withholding.enasarco_purchase_tax_report_it_line
msgid "ENASARCO Amount (Purchase)"
msgstr "Ritenute ENASARCO Operate (Acquisti)"

#. module: l10n_it_edi_withholding
#: model:account.report.line,name:l10n_it_edi_withholding.enasarco_sale_tax_report_it_line
msgid "ENASARCO Amount (Sales)"
msgstr "Ritenute ENASARCO Subite (Vendite)"

#. module: l10n_it_edi_withholding
#: model:account.tax.group,preceding_subtotal:l10n_it_edi_withholding.tax_group_enasarco
msgid "ENASARCO Excluded"
msgstr "Ritenuta ENASARCO Esclusa"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc07
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc07
msgid "ENASARCO pension fund for sales agents"
msgstr ""
"Ente nazionale assistenza agenti e rappresentanti di commercio (ENASARCO)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc19
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc19
msgid "ENPAB national pension fund for biologists"
msgstr "Ente nazionale previdenza e assistenza biologi (ENPAB)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc08
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc08
msgid "ENPACL pension fund for labor consultants"
msgstr "Ente nazionale previdenza e assistenza consulenti del lavoro (ENPACL)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc10
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc10
msgid "ENPAF pension fund for chemists"
msgstr "Ente nazionale previdenza e assistenza farmacisti (ENPAF)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc12
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc12
msgid "ENPAIA pension fund for people working in agriculture"
msgstr ""
"Ente nazionale previdenza e assistenza impiegati dell'agricoltura (ENPAIA)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc09
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc09
msgid "ENPAM pension fund for doctors"
msgstr "Ente nazionale previdenza e assistenza medici (ENPAM)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc21
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc21
msgid "ENPAP national pension fund for psychologists"
msgstr "Ente nazionale previdenza e assistenza psicologi (ENPAP)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc20
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc20
msgid "ENPAPI national pension fund for nurses"
msgstr ""
"Ente nazionale previdenza e assistenza professione infermieristica (ENPAPI)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc11
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc11
msgid "ENPAV pension fund for veterinaries"
msgstr "Ente nazionale previdenza e assistenza veterinari (ENPAV)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc18
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc18
msgid "EPAP pension fund"
msgstr "Ente previdenza e assistenza pluricategoriale (EPAP)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc17
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc17
msgid "EPPI pension fund for industrial experts"
msgstr ""
"Ente previdenza periti industriali e periti industriali laureati (EPPI)"

#. module: l10n_it_edi_withholding
#: model:account.tax.group,name:l10n_it_edi_withholding.tax_group_enasarco
msgid "Enasarco"
msgstr ""

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc14
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc14
msgid "INPGI pension fund for journalists"
msgstr "Istituto nazionale previdenza giornalisti italiani (INPGI)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc22
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc22
msgid "INPS national pension fund"
msgstr "Fondo pensione nazionale (INPS)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields,help:l10n_it_edi_withholding.field_account_tax_group__preceding_subtotal
msgid ""
"If set, this value will be used on documents as the label of a subtotal "
"excluding this tax group before displaying it. If not set, the tax group "
"will be displayed after the 'Untaxed amount' subtotal."
msgstr ""

#. module: l10n_it_edi_withholding
#: model:ir.model,name:l10n_it_edi_withholding.model_account_move
msgid "Journal Entry"
msgstr "Movimento Contabile"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc04
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc04
msgid "National pension fund for associated engineers and architects"
msgstr ""
"Cassa nazionale previdenza e assistenza ingegneri e architetti liberi "
"professionisti"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc01
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc01
msgid "National pension fund for lawyers and solicitors"
msgstr "Cassa nazionale previdenza e assistenza avvocati e procuratori legali"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc05
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc05
msgid "National pension fund for notaries"
msgstr "Cassa nazionale del notariato"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc15
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc15
msgid "ONAOSI fund for sanitary orphans"
msgstr "Opera nazionale assistenza orfani sanitari italiani (ONAOSI)"

#. module: l10n_it_edi_withholding
#: model:account.tax.group,name:l10n_it_edi_withholding.tax_group_pension_fund
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_bank_statement_line__l10n_it_amount_pension_fund_signed
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_move__l10n_it_amount_pension_fund_signed
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_payment__l10n_it_amount_pension_fund_signed
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_withholding.view_invoice_tree_l10n_it_edi_extended
msgid "Pension Fund"
msgstr "Cassa previdenziale"

#. module: l10n_it_edi_withholding
#: model:account.tax.group,preceding_subtotal:l10n_it_edi_withholding.tax_group_pension_fund
msgid "Pension Fund Excluded"
msgstr "Cassa Previdenziale Esclusa"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields,help:l10n_it_edi_withholding.field_account_tax__l10n_it_pension_fund_type
#: model:ir.model.fields,help:l10n_it_edi_withholding.field_account_tax_template__l10n_it_pension_fund_type
msgid "Pension Fund Type. Only for Italian accounting EDI."
msgstr "Tipo di Cassa Previdenziale. Solo per la contabilità italiana."

#. module: l10n_it_edi_withholding
#. odoo-python
#: code:addons/l10n_it_edi_withholding/models/account_edi_format.py:0
#, python-format
msgid "Pension Fund tax not found"
msgstr "Contributo Cassa Previdenziale non trovato"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc02
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc02
msgid "Pension fund for accountants with a degree"
msgstr "Cassa previdenza dottori commercialisti"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc06
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc06
msgid "Pension fund for accountants without a degree and commercial experts"
msgstr ""
"Cassa nazionale previdenza e assistenza ragionieri e periti commerciali"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc13
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc13
msgid "Pension fund for employees in delivery and marine agencies"
msgstr "Fondo previdenza impiegati imprese di spedizione e agenzie marittime"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_pension_fund_type__tc03
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_pension_fund_type__tc03
msgid "Pension fund for surveyors"
msgstr "Cassa previdenza e assistenza geometri"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_tax__l10n_it_pension_fund_type
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_tax_template__l10n_it_pension_fund_type
msgid "Pension fund type (Italy)"
msgstr "Tipo di Cassa Previdenziale (Italia)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_tax_group__preceding_subtotal
msgid "Preceding Subtotal"
msgstr "Subtotale precedente"

#. module: l10n_it_edi_withholding
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_withholding.account_invoice_it_FatturaPA_export_withholding
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_withholding.account_invoice_line_it_FatturaPA_withholding
msgid "SI"
msgstr ""

#. module: l10n_it_edi_withholding
#: model:ir.model,name:l10n_it_edi_withholding.model_account_tax
msgid "Tax"
msgstr "Imposta"

#. module: l10n_it_edi_withholding
#. odoo-python
#: code:addons/l10n_it_edi_withholding/models/account_tax.py:0
#, python-format
msgid ""
"Tax '%s' cannot be both a Withholding tax and a Pension fund tax. Please "
"create two separate ones."
msgstr ""
"'%s' non puo essere sia una Ritenuta che un Contributo Cassa Previdenziale. "
"Per favore, crea due Imposte separate."

#. module: l10n_it_edi_withholding
#. odoo-python
#: code:addons/l10n_it_edi_withholding/models/account_tax.py:0
#, python-format
msgid ""
"Tax '%s' has a withholding reason, so the withholding type must also be "
"specified"
msgstr ""
"'%s' ha una Causale Ritenuta, quindi dev'essere specificato anche il Tipo "
"Ritenuta"

#. module: l10n_it_edi_withholding
#. odoo-python
#: code:addons/l10n_it_edi_withholding/models/account_tax.py:0
#, python-format
msgid "Tax '%s' has a withholding type so the amount must be negative."
msgstr "'%s' ha un Tipo Ritenuta, quindi l'ammontare dev'essere negativo."

#. module: l10n_it_edi_withholding
#. odoo-python
#: code:addons/l10n_it_edi_withholding/models/account_tax.py:0
#, python-format
msgid ""
"Tax '%s' has a withholding type, so the withholding reason must also be "
"specified"
msgstr ""
"'%s' ha un Tipo Ritenuta, quindi dev'essere specificata anche una Causale "
"Ritenuta"

#. module: l10n_it_edi_withholding
#: model:ir.model,name:l10n_it_edi_withholding.model_account_tax_group
msgid "Tax Group"
msgstr "Gruppo imposta"

#. module: l10n_it_edi_withholding
#: model:ir.model,name:l10n_it_edi_withholding.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Modelli per imposte"

#. module: l10n_it_edi_withholding
#: model:account.report.column,name:l10n_it_edi_withholding.withh_tax_report_balance
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_withholding.view_invoice_tree_l10n_it_edi_extended
msgid "Total"
msgstr "Totale"

#. module: l10n_it_edi_withholding
#: model:account.tax.group,preceding_subtotal:l10n_it_edi_withholding.tax_group_withholding
msgid "Total Before Withholding"
msgstr "Totale Ritenute Escluse"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_bank_statement_line__l10n_it_amount_vat_signed
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_move__l10n_it_amount_vat_signed
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_payment__l10n_it_amount_vat_signed
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_withholding.view_invoice_tree_l10n_it_edi_extended
msgid "VAT"
msgstr "IVA"

#. module: l10n_it_edi_withholding
#: model:account.tax.group,name:l10n_it_edi_withholding.tax_group_withholding
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_bank_statement_line__l10n_it_amount_withholding_signed
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_move__l10n_it_amount_withholding_signed
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_payment__l10n_it_amount_withholding_signed
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_withholding.view_invoice_tree_l10n_it_edi_extended
msgid "Withholding"
msgstr "Ritenuta"

#. module: l10n_it_edi_withholding
#: model:account.report.line,name:l10n_it_edi_withholding.withh_purchase_tax_report_it_line
msgid "Withholding Amount (Purchase)"
msgstr "Ritenute Operate (Acquisti)"

#. module: l10n_it_edi_withholding
#: model:account.report.line,name:l10n_it_edi_withholding.withh_sale_tax_report_it_line
msgid "Withholding Amount (Sales)"
msgstr "Ritenute Subite (Vendite)"

#. module: l10n_it_edi_withholding
#: model:account.report,name:l10n_it_edi_withholding.withh_tax_report_it
msgid "Withholding Report"
msgstr "Riepilogo Ritenute"

#. module: l10n_it_edi_withholding
#. odoo-python
#: code:addons/l10n_it_edi_withholding/models/account_edi_format.py:0
#, python-format
msgid "Withholding tax not found"
msgstr "Ritenuta non trovata"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_tax__l10n_it_withholding_reason
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_tax_template__l10n_it_withholding_reason
msgid "Withholding tax reason (Italy)"
msgstr "Causale Ritenuta (Italia)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields,help:l10n_it_edi_withholding.field_account_tax__l10n_it_withholding_reason
#: model:ir.model.fields,help:l10n_it_edi_withholding.field_account_tax_template__l10n_it_withholding_reason
msgid "Withholding tax reason. Only for Italian accounting EDI."
msgstr "Causale Ritenuta. Solo per la contabilità itlaliana."

#. module: l10n_it_edi_withholding
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_tax__l10n_it_withholding_type
#: model:ir.model.fields,field_description:l10n_it_edi_withholding.field_account_tax_template__l10n_it_withholding_type
msgid "Withholding tax type (Italy)"
msgstr "Tipo Ritenuta (Italia)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields,help:l10n_it_edi_withholding.field_account_tax__l10n_it_withholding_type
#: model:ir.model.fields,help:l10n_it_edi_withholding.field_account_tax_template__l10n_it_withholding_type
msgid "Withholding tax type. Only for Italian accounting EDI."
msgstr "Tipo Ritenuta. Solo per la contabilità italiana."

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__a
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__a
msgid "[A] Autonomous work in the fields of art or profession"
msgstr ""
"[A] Prestazioni di lavoro autonomo rientranti nell’esercizio di arte o "
"professione abituale"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__b
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__b
msgid ""
"[B] Income from the use of intellectual properties or patents or processes, "
"formulas and informations in the fields of science, commerce or science"
msgstr ""
"[B] Utilizzazione economica, da parte dell’autore o dell’inventore, di opere"
" dell’ingegno, di brevetti industriali e di processi, relativi a esperienze "
"acquisite in campo industriale, commerciale o scientifico"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__c
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__c
msgid ""
"[C] Income from work as part of association groups or other cooperation "
"determined by contracts"
msgstr ""
"[C] Utili derivanti da contratti di associazione in partecipazione e da "
"contratti di cointeressenza, quando l’apporto è costituito esclusivamente "
"dalla prestazione di lavoro"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__d
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__d
msgid "[D] Income as partner or founder of a corporation"
msgstr ""
"[D] Utili spettanti ai soci promotori e ai soci fondatori delle società di "
"capitali"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__e
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__e
msgid "[E] Income from client-related bill protests made by town secretaries"
msgstr "[E] Levata di protesti cambiari da parte dei segretari comunali"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__g
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__g
msgid "[G] Compensation for the end of a professional sport career"
msgstr ""
"[G] Indennità corrisposte per la cessazione di attività sportiva "
"professionale"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__h
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__h
msgid ""
"[H] Compensation for the end of a societary career (excluded those earned "
"before 31.12.2003) and already taxed"
msgstr ""
"[H] Indennità corrisposte per la cessazione dei rapporti di agenzia delle "
"persone fisiche e delle società di persone, con esclusione delle somme "
"maturate entro il 31.12.2003, già imputate per competenza"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__i
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__i
msgid "[I] Compensation for the end of a notary career"
msgstr "[I] Indennità corrisposte per la cessazione da funzioni notarili"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__k
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__k
msgid "[K] Civil service checks, ref art. 16 D.lgs. n.40 6/03/2017"
msgstr ""
"[K] Assegni di servizio civile di cui all’art. 16 del D.lgs. n. 40 del 6 "
"marzo 2017"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__l1
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__l1
msgid ""
"[L1] Income from the use of intellectual properties or patents or processes,"
" formulas and informations in the fields of science, commerce or science, "
"from someone who actively bought the use rights"
msgstr ""
"[L1] Redditi derivanti dall’utilizzazione economica di opere dell’ingegno, "
"di brevetti industriali e di processi, che sono percepiti da soggetti che "
"abbiano acquistato a titolo oneroso i diritti alla loro utilizzazione"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__l
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__l
msgid ""
"[L] Income from the use of intellectual properties or patents or processes, "
"formulas and informations in the fields of science, commerce or science, but"
" not made by the author/inventor"
msgstr ""
"[L] Utilizzazione economica, da parte di soggetto diverso dall’autore o "
"dall’inventore, di opere dell’ingegno, di brevetti industriali e di "
"processi, formule e informazioni relative a esperienze acquisite"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__m1
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__m1
msgid "[M1] Incomes due for an obligation to act, not to act, or to allow"
msgstr ""
"[M1] Redditi derivanti dall’assunzione di obblighi di fare, di non fare o "
"permettere"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__m2
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__m2
msgid ""
"[M2] Autonomous work which isn't part of usual professional/artistic duties,"
" or incomes due for an obligation to act, not to act, or to allow - that "
"require being registered to the \"Gestione separata\""
msgstr ""
"[M2] Prestazioni di lavoro autonomo non esercitate abitualmente per le quali"
" sussiste l’obbligo di iscrizione alla Gestione Separata ENPAPI"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__m
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__m
msgid ""
"[M] Autonomous work which isn't part of usual professional/artistic duties, "
"or incomes due for an obligation to act, not to act, or to allow"
msgstr ""
"[M] Prestazioni di lavoro autonomo non esercitate abitualmente, obblighi di "
"fare, di non fare o permettere"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__n
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__n
msgid ""
"[N] Compensation for travel, expenses, prizes, or other compensations for "
"amateur sport activities"
msgstr ""
"[N] Indennità di trasferta, rimborso forfettario di spese, premi e compensi "
"erogati nell’esercizio diretto di attività sportive dilettantistiche"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__o1
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__o1
msgid ""
"[O1] Incomes due for an obligation to act, not to act, or to allow - that do"
" not require being registered to the \"Gestione Separata\""
msgstr ""
"[O1] Redditi derivanti dall’assunzione di obblighi di fare, di non fare o "
"permettere, per le quali non sussiste l’obbligo di iscrizione alla gestione "
"separata (Circ. INPS n. 104/2001)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__o
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__o
msgid ""
"[O] Autonomous work which isn't part of usual professional/artistic duties, "
"or incomes due for an obligation to act, not to act, or to allow - that do "
"not require being registered to the \"Gestione separata\""
msgstr ""
"[O] Prestazioni di lavoro autonomo non esercitate abitualmente, obblighi di "
"fare, di non fare o permettere, per le quali non sussiste l’obbligo di "
"iscrizione alla gestione separata (Circ. Inps 104/2001)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__p
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__p
msgid ""
"[P] Compensation for people residing abroad for continuous use or concession"
" of industrial machinery, commercial or scientific tools that are on the "
"Italian soil"
msgstr ""
"[P] Compensi corrisposti a soggetti non residenti privi di stabile "
"organizzazione per l’uso o la concessione in uso di attrezzature "
"industriali, commerciali o scientifiche che si trovano nel territorio dello "
"Stato"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__q
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__q
msgid "[Q] Provisions for exclusive agents or sales representatives' work"
msgstr ""
"[Q] Provvigioni corrisposte ad agente o rappresentante di commercio "
"monomandatario"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_type__rt01
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_type__rt01
msgid "[RT01] Withholding for persons"
msgstr "[RT01] Ritenuta persone fisiche"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_type__rt02
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_type__rt02
msgid "[RT02] Withholding for personal businesses"
msgstr "[RT02] Ritenuta persone giuridiche"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_type__rt03
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_type__rt03
msgid "[RT03] INPS Pension fund contribution"
msgstr "[RT04] Contributo INPS"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_type__rt04
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_type__rt04
msgid "[RT04] ENASARCO pension fund contribution"
msgstr "[RT04] Contributo ENASARCO"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_type__rt05
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_type__rt05
msgid "[RT05] ENPAM pension fund contribution"
msgstr "[RT05] Contributo ENPAM"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_type__rt06
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_type__rt06
msgid "[RT06] Other pension fund contribution"
msgstr "[RT06] Altro contributo previdenziale"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__r
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__r
msgid "[R] Provisions for non-exclusive agents or sales representatives' work"
msgstr ""
"[R] Provvigioni corrisposte ad agente o rappresentante di commercio "
"plurimandatario"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__s
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__s
msgid "[S] Provisions for commissioner work"
msgstr "[S] Provvigioni corrisposte a commissionario"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__t
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__t
msgid "[T] Provisions for mediator work"
msgstr "[T] Provvigioni corrisposte a mediatore"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__u
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__u
msgid "[U] Provisions for procurer work"
msgstr "[U] Provvigioni corrisposte a procacciatore di affari"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__v1
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__v1
msgid ""
"[V1] Income from unusual commercial activities (such as provisions for "
"occasional work or sales representative, mediator, procurer)"
msgstr ""
"[V1] Redditi derivanti da attività commerciali non esercitate abitualmente "
"(ad esempio, provvigioni corrisposte per prestazioni occasionali ad agente o"
" rappresentante di commercio, mediatore, procacciatore d’affari);"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__v2
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__v2
msgid ""
"[V2] Income from unusual work activities from door-to-door sales "
"representatives"
msgstr ""
"[V2] Redditi derivanti dalle prestazioni non esercitate abitualmente rese "
"dagli incaricati alla vendita diretta a domicilio;"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__v
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__v
msgid ""
"[V] Provisions for door-to-door sales persons and newspaper selling in "
"kiosks"
msgstr ""
"[V] Provvigioni corrisposte a incaricato per le vendite a domicilio e "
"provvigioni corrisposte a incaricato per la vendita porta a porta e per la "
"vendita ambulante di giornali quotidiani e periodici (L. 25.02.1987, n. 67)"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__w
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__w
msgid ""
"[W] Income from 2015 tinders subject to law art. 25-ter D.P.R. 600/1973"
msgstr ""
"[W] Corrispettivi erogati nel 2015 per prestazioni relative a contratti "
"d’appalto cui si sono resi applicabili le disposizioni contenute nell’art "
"25-ter D.P.R. 600/1973"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__x
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__x
msgid ""
"[X] Income from 2014 for foreign companies or institutions subject to law "
"art. 26-quater, c. 1, lett. a) and b) D.P.R. 600/1973"
msgstr ""
"[X] Canoni corrisposti nel 2004 da società o enti residenti, ovvero da "
"stabili organizzazioni di società estere di cui all’art. 26-quater, c. 1, "
"lett. a) e b) D.P.R. 600/1973"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__y
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__y
msgid ""
"[Y] Income from 1.01.2005 to 26.07.2005 from companies or institutions not "
"included in the description above"
msgstr ""
"[Y] Canoni corrisposti dal 1.01.2005 al 26.07.2005 da soggetti di cui al "
"punto precedente"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__zo
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__zo
msgid "[ZO] Other reason"
msgstr "[Z0] Titolo diverso dai precedenti"

#. module: l10n_it_edi_withholding
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax__l10n_it_withholding_reason__z
#: model:ir.model.fields.selection,name:l10n_it_edi_withholding.selection__account_tax_template__l10n_it_withholding_reason__z
msgid "[Z] Deprecated"
msgstr "[Z] Obsoleto"
