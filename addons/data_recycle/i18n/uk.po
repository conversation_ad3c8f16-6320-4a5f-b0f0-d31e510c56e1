# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_recycle
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid ""
"' recycling rule.<br/>\n"
"You can validate those changes"
msgstr ""
"' правило очищення.<br/>\n"
"Ви можете підтвердити ті зміни"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_record.py:0
#, python-format
msgid "**Record Deleted**"
msgstr "**Запис видалено**"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "<span class=\"me-1\">Every</span>"
msgstr "<span class=\"me-1\">Кожен</span>"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__active
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__active
msgid "Active"
msgstr "Активно"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_action__archive
msgid "Archive"
msgstr "Архів"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_mode__automatic
msgid "Automatic"
msgstr "Автоматичний"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__company_id
msgid "Company"
msgstr "Компанія"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config
msgid "Configuration"
msgstr "Налаштування"

#. module: data_recycle
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record_notification
msgid "Configure rules to identify records to clean"
msgstr "Налаштуйте правила, щоб визначити записи для очищення"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__create_uid
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__create_uid
msgid "Created by"
msgstr "Створив"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__create_date
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__create_date
msgid "Created on"
msgstr "Створено"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_root
msgid "Data Cleaning"
msgstr "Очищення даних"

#. module: data_recycle
#: model:ir.actions.server,name:data_recycle.ir_cron_clean_records_ir_actions_server
#: model:ir.cron,cron_name:data_recycle.ir_cron_clean_records
msgid "Data Recycle: Clean Records"
msgstr "Очищення даних: Очистити записи"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__days
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__days
msgid "Days"
msgstr "Дні"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_action__unlink
msgid "Delete"
msgstr "Видалити"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_delta
msgid "Delta"
msgstr "Дельта"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_delta_unit
msgid "Delta Unit"
msgstr "Одиниця дельти"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Discard"
msgstr "Відмінити"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Discarded"
msgstr "Відмінено"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__display_name
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: data_recycle
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_record
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_record_notification
msgid "Field Recycle Records"
msgstr "Поле очищення записів"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__domain
msgid "Filter"
msgstr "Фільтр"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__id
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__id
msgid "ID"
msgstr "ID"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__include_archived
msgid "Include Archived"
msgstr "Включно з заархівованими"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model____last_update
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record____last_update
msgid "Last Modified on"
msgstr "Остання модифікація"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__last_notification
msgid "Last Notification"
msgstr "Останнє сповіщення"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__write_uid
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__write_date
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: data_recycle
#: model:ir.model.fields,help:data_recycle.field_data_recycle_model__notify_user_ids
msgid "List of users to notify when there are new records to recycle"
msgstr ""
"Список користувачів, для сповіщення, коли будуть нові записи для очищення"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_mode__manual
msgid "Manual"
msgstr "Вручну"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__res_model_id
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_model_id
msgid "Model"
msgstr "Модель"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__res_model_name
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_model_name
msgid "Model Name"
msgstr "Назва моделі"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__months
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__months
msgid "Months"
msgstr "Місяці"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__name
msgid "Name"
msgstr "Назва"

#. module: data_recycle
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record_notification
msgid "No cleaning suggestions"
msgstr "Немає пропозицій для очищення"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_frequency
msgid "Notify"
msgstr "Повідомити"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "Сповіщати періодично"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_user_ids
msgid "Notify Users"
msgstr "Сповістити користувачів"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_id
msgid "Record ID"
msgstr "ID запису"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__name
msgid "Record Name"
msgstr "Назва запису"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Records"
msgstr "Записи"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__records_to_recycle_count
msgid "Records To Recycle"
msgstr "Записи для очищення"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_action
msgid "Recycle Action"
msgstr "Дія очищення"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_mode
msgid "Recycle Mode"
msgstr "Режим очищення"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__recycle_model_id
msgid "Recycle Model"
msgstr "Модель очищення"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_record_ids
msgid "Recycle Record"
msgstr "Запис очищення"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config_rules_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_recycle_record
msgid "Recycle Records"
msgstr "Записи очищення"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Recycle Rule"
msgstr "Правило очищення"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Recycle Rules"
msgstr "Правила очищення"

#. module: data_recycle
#: model:ir.model,name:data_recycle.model_data_recycle_model
msgid "Recycling Model"
msgstr "Модель очищення"

#. module: data_recycle
#: model:ir.model,name:data_recycle.model_data_recycle_record
msgid "Recycling Record"
msgstr "Запис очищення"

#. module: data_recycle
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_config
msgid "Recyle Records Rules"
msgstr "Правила очищення записів"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config_rules
msgid "Rules"
msgstr "Правила"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "Run Now"
msgstr "Запустити зараз"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "Select a model to configure recycling actions"
msgstr "Оберіть модель для налаштування дій очищення"

#. module: data_recycle
#: model:ir.model.constraint,message:data_recycle.constraint_data_recycle_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "Період сповіщень повинен бути більше 0."

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_model.py:0
#, python-format
msgid "This model doesn't manage archived records. Only deletion is possible."
msgstr "Ця модель не керує заархівованими записами. Можливе лише видалення."

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_id
msgid "Time Field"
msgstr "Поле часу"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_record.py:0
#, python-format
msgid "Undefined Name"
msgstr "Невизначена назва"

#. module: data_recycle
#. odoo-javascript
#: code:addons/data_recycle/static/src/views/data_recycle_list_view.xml:0
#, python-format
msgid "Unselect"
msgstr "Не обрано"

#. module: data_recycle
#. odoo-javascript
#: code:addons/data_recycle/static/src/views/data_recycle_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
#, python-format
msgid "Validate"
msgstr "Підтвердити"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "We've identified"
msgstr "Ми ідентифікували"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__weeks
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__weeks
msgid "Weeks"
msgstr "Тижні"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__years
msgid "Years"
msgstr "Роки"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "here"
msgstr "тут"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "records to clean with the '"
msgstr "записи для очищення з '"
