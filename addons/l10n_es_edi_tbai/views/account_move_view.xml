<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_move_form_inherit_l10n_es_edi_tbai" model="ir.ui.view">
            <field name="name">account.move.form.inherit.l10n_es_edi_tbai</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@id='other_tab_group']/group[last()]" position='after'>
                    <group id="ticketbai_group" string="TicketBAI" attrs="{'invisible': [('l10n_es_tbai_is_required', '=', False)]}">
                        <field name="l10n_es_tbai_is_required" invisible="1"/>
                        <field name="l10n_es_tbai_chain_index" groups="base.group_no_one"/>
                        <field name="l10n_es_tbai_refund_reason"
                            attrs="{
                            'readonly': [('state', '!=', 'draft')],
                            'invisible': [('move_type', 'not in', ('in_refund', 'out_refund'))]
                        }"/>
                        <field name="reversed_entry_id" attrs="{'invisible': [('move_type', '!=', 'in_refund')]}"/>
                    </group>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
