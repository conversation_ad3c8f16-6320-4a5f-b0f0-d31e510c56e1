# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_account
# 
# Translators:
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-23 08:34+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_datasource.js:0
#, python-format
msgid "%s is not a valid year."
msgstr "%s سال معتبر نیست."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid ""
"'%s' is not a valid period. Supported formats are \"21/12/2022\", "
"\"Q1/2022\", \"12/2022\", and \"2022\"."
msgstr ""
"'%s' یک دوره معتبر نیست. فرمت‌های پشتیبانی شده عبارتند از \"21/12/2022\"، "
"\"Q1/2022\"، \"12/2022\"، و \"2022\"."

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_account_account
msgid "Account"
msgstr "حساب"

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_res_company
msgid "Companies"
msgstr "شرکت"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Get the total balance for the specified account(s) and period."
msgstr "حاصل جمع تراز برای حساب(های) مشخص شده و دوره را دریافت کنید."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Get the total credit for the specified account(s) and period."
msgstr "دریافت اعتبار کل برای حساب(های) مشخص‌شده و دوره زمانی مشخص‌شده."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Get the total debit for the specified account(s) and period."
msgstr "مجموع بدهی برای حساب (حساب‌های) مشخص و دوره زمانی مشخص را بدست آورید."

#. module: spreadsheet_account
#. odoo-python
#: code:addons/spreadsheet_account/models/account.py:0
#, python-format
msgid "Journal items for account prefix %s"
msgstr "موارد ژورنال برای پیشوند حساب %s"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Returns the account ids of a given group."
msgstr "شناسه‌های حساب یک گروه خاص را برمی‌گرداند."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid ""
"Returns the ending date of the fiscal year encompassing the provided date."
msgstr ""
"```html\n"
"تاریخ پایان سال مالی که تاریخ ارائه شده را در بر می گیرد، باز می گرداند.\n"
"```"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid ""
"Returns the starting date of the fiscal year encompassing the provided date."
msgstr "تاریخ شروع سال مالی که تاریخ ارائه شده را شامل می‌شود برمی‌گرداند."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/index.js:0
#, python-format
msgid "See records"
msgstr "رکوردها را ببینید"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_datasource.js:0
#, python-format
msgid "The company fiscal year could not be found."
msgstr "<br>سال مالی شرکت یافت نشد.</br>"
