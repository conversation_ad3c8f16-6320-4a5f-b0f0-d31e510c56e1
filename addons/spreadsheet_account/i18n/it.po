# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_account
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-23 08:34+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_datasource.js:0
#, python-format
msgid "%s is not a valid year."
msgstr "%s non è un anno valido."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid ""
"'%s' is not a valid period. Supported formats are \"21/12/2022\", "
"\"Q1/2022\", \"12/2022\", and \"2022\"."
msgstr ""
"\"%s\" non è un periodo valido. I formati supportati sono \"21/12/2022\", "
"\"Q1/2022\", \"12/2022\" e \"2022\"."

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_account_account
msgid "Account"
msgstr "Conto"

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Get the total balance for the specified account(s) and period."
msgstr ""
"Ottieni il saldo totale del conto o dei conti e del periodo specificati."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Get the total credit for the specified account(s) and period."
msgstr "Ottieni il credito totale per i conti e i periodi specificati."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Get the total debit for the specified account(s) and period."
msgstr "Ottieni il totale degli addebiti per i conti e i periodi specificati."

#. module: spreadsheet_account
#. odoo-python
#: code:addons/spreadsheet_account/models/account.py:0
#, python-format
msgid "Journal items for account prefix %s"
msgstr "Movimenti contabili per il prefisso del conto %s"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Returns the account ids of a given group."
msgstr "Restituisce gli id degli conti di un dato gruppo."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid ""
"Returns the ending date of the fiscal year encompassing the provided date."
msgstr ""
"Restituisce la data finale dell'anno fiscale che comprende la data fornita."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid ""
"Returns the starting date of the fiscal year encompassing the provided date."
msgstr ""
"Restituisce la data di inizio dell'anno fiscale che comprende la data "
"fornita."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/index.js:0
#, python-format
msgid "See records"
msgstr "Mostra record"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_datasource.js:0
#, python-format
msgid "The company fiscal year could not be found."
msgstr "Non è stato possibile trovare l'anno fiscale della società."
