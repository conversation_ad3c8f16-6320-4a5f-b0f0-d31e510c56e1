# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_daily_sales_reports
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON> <g<PERSON><PERSON><PERSON>@it-projects.info>, 2023
# <AUTHOR> <EMAIL>, 2023
# Сергей <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2023-04-14 06:18+0000\n"
"Last-Translator: ILMIR <<EMAIL>>, 2023\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>Amount of discounts</strong>:"
msgstr "<strong>Сумма скидок</strong>:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>Config names</strong>"
msgstr "<strong>Названия конфигураций</strong>"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>End of session note:</strong>"
msgstr "<strong>Заметка об окончании сеанса:</strong>"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>Number of discounts</strong>:"
msgstr "<strong>Количество скидок</strong>:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>Opening of session note:</strong>"
msgstr "<strong>Заметка об открытии сессии:</strong>"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>Total</strong>"
msgstr "<strong>Итого</strong>"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Base Amount"
msgstr "Базовая сумма"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.view_pos_daily_sales_reports_wizard
msgid "Cancel"
msgstr "Отмена"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_session__closing_notes
msgid "Closing Notes"
msgstr "Заключительные замечания"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Counted"
msgstr "Подсчитано"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__create_uid
msgid "Created by"
msgstr "Создано"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__create_date
msgid "Created on"
msgstr "Дата создания"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Daily Report"
msgstr "Ежедневный отчет"

#. module: pos_daily_sales_reports
#: model:ir.actions.act_window,name:pos_daily_sales_reports.action_report_pos_daily_sales_reports
msgid "Daily Reports"
msgstr "Ежедневные отчеты"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Difference"
msgstr "Расхождение"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Disc:"
msgstr "Скидка:"

#. module: pos_daily_sales_reports
#. odoo-javascript
#: code:addons/pos_daily_sales_reports/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/pos_daily_sales_reports/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "Discount:"
msgstr "Скидка:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Discounts:"
msgstr "Скидки:"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__display_name
msgid "Display Name"
msgstr "Отображаемое название"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Expected"
msgstr "Ожидается"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__id
msgid "ID"
msgstr "Идентификатор"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Invoices"
msgstr "Счета"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Multiple Report"
msgstr ""

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Name"
msgstr "Название"

#. module: pos_daily_sales_reports
#. odoo-python
#: code:addons/pos_daily_sales_reports/models/pos_daily_sales_reports.py:0
#, python-format
msgid "No Taxes"
msgstr "Без налогов"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Number of transactions:"
msgstr "Количество транзакций:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Order reference"
msgstr "Ссылка на заказ"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Payments"
msgstr "Платежи"

#. module: pos_daily_sales_reports
#: model:ir.model,name:pos_daily_sales_reports.model_pos_daily_sales_reports_wizard
msgid "Point of Sale Daily Report"
msgstr "Ежедневный отчет точки продаж"

#. module: pos_daily_sales_reports
#: model:ir.model,name:pos_daily_sales_reports.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "Детали точки продаж"

#. module: pos_daily_sales_reports
#: model:ir.model,name:pos_daily_sales_reports.model_pos_session
msgid "Point of Sale Session"
msgstr "Смена"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__pos_session_id
msgid "Pos Session"
msgstr "Сессия точки продаж"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.view_pos_daily_sales_reports_wizard
msgid "Pos session"
msgstr "Сессия точки продаж"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.view_pos_daily_sales_reports_wizard
msgid "Print"
msgstr "Печать"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Product"
msgstr "Продукт"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Product Category"
msgstr "Категория продукта"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Quantity"
msgstr "Количество"

#. module: pos_daily_sales_reports
#. odoo-javascript
#: code:addons/pos_daily_sales_reports/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "REFUNDED:"
msgstr "ВОЗВРАЩЕНО:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Refunds"
msgstr "Возвраты"

#. module: pos_daily_sales_reports
#. odoo-javascript
#: code:addons/pos_daily_sales_reports/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "SOLD:"
msgstr "ПРОДАНО:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Sales"
msgstr "Продажи"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.view_pos_daily_sales_reports_wizard
msgid "Sales Details"
msgstr "Детали продаж"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Session Control"
msgstr "Контроль сессии"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Session ID:"
msgstr "Идентификатор сессии:"

#. module: pos_daily_sales_reports
#: model:ir.ui.menu,name:pos_daily_sales_reports.menu_report_daily_details
msgid "Session Report"
msgstr "Отчет сессии"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Tax Amount"
msgstr "Сумма налога"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Taxes on refunds"
msgstr "Налоги на возвраты"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Taxes on sales"
msgstr "Налоги на продажи"

#. module: pos_daily_sales_reports
#. odoo-python
#: code:addons/pos_daily_sales_reports/models/pos_daily_sales_reports.py:0
#, python-format
msgid "This session is already closed."
msgstr "Сессия уже закрыта."

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Total"
msgstr "Всего"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Total:"
msgstr "Итого:"
