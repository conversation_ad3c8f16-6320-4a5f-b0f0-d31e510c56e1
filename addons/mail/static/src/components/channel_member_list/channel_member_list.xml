<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.ChannelMemberList" owl="1">
        <t t-if="channelMemberListView">
            <div class="o_ChannelMemberList d-flex flex-column overflow-auto bg-light" t-attf-class="{{ className }}" t-ref="root">
                <t t-if="channelMemberListView.onlineCategoryView">
                    <ChannelMemberListCategory record="channelMemberListView.onlineCategoryView"/>
                </t>
                <t t-if="channelMemberListView.offlineCategoryView">
                    <ChannelMemberListCategory record="channelMemberListView.offlineCategoryView"/>
                </t>
                <t t-if="channelMemberListView.channel.unknownMemberCount === 1">
                    <span class="mx-2 mt-2">And 1 other member.</span>
                </t>
                <t t-if="channelMemberListView.channel.unknownMemberCount > 1">
                    <span class="mx-2 mt-2">And <t t-esc="channelMemberListView.channel.unknownMemberCount"/> other members.</span>
                </t>
                <t t-if="!channelMemberListView.channel.areAllMembersLoaded">
                    <div class="mx-2 my-1">
                        <button class="o_ChannelMemberList_loadMoreButton btn btn-secondary" t-on-click="channelMemberListView.onClickLoadMoreMembers">Load more</button>
                    </div>
                </t>
            </div>
        </t>
    </t>

</templates>
