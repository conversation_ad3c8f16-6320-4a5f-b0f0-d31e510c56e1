# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
#, python-format
msgid "An error occured while loading the dashboard"
msgstr "Ocurrió un error al cargar el tablero"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_search_panel/mobile_search_panel.xml:0
#, python-format
msgid "BACK"
msgstr "REGRESAR"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_search_panel/mobile_search_panel.js:0
#, python-format
msgid "Choose a dashboard...."
msgstr "Elegir un tablero..."

#. module: spreadsheet_dashboard
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__create_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__create_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__create_date
msgid "Created on"
msgstr "Creado el"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__dashboard_ids
msgid "Dashboard"
msgstr "Tablero"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__dashboard_group_id
msgid "Dashboard Group"
msgstr "Grupo de tablero"

#. module: spreadsheet_dashboard
#: model:ir.actions.act_window,name:spreadsheet_dashboard.spreadsheet_dashboard_action_configuration_dashboards
#: model:ir.actions.client,name:spreadsheet_dashboard.ir_actions_dashboard_action
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_configuration_dashboards
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_dashboard
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_root
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_container_view_list
msgid "Dashboards"
msgstr "Tableros"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__data
msgid "Data"
msgstr "Datos"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__display_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__group_ids
msgid "Group"
msgstr "Grupo"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard_group
msgid "Group of dashboards"
msgstr "Grupo de tableros"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__id
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__id
msgid "ID"
msgstr "ID"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard.py:0
#, python-format
msgid "Invalid JSON Data"
msgstr "Datos JSON no válidos "

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard____last_update
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__write_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__write_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
#, python-format
msgid "Loading..."
msgstr "Cargando…"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__name
msgid "Name"
msgstr "Nombre"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
#, python-format
msgid "No available dashboard"
msgstr "No hay tablero disponible"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_figure_container/mobile_figure_container.xml:0
#, python-format
msgid ""
"Only chart figures are displayed in small screens but this dashboard doesn't"
" contain any"
msgstr ""
"Lo único que se muestra en recuadros pequeños son los gráficos, pero este "
"tablero no tiene ninguno"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__raw
msgid "Raw"
msgstr "Raw"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__sequence
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard.py:0
#, python-format
msgid "Sheet1"
msgstr "Hoja1"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard
msgid "Spreadsheet Dashboard"
msgstr "Tablero de Hojas de cálculo"

#. module: spreadsheet_dashboard
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_container_view_form
msgid "Spreadsheets"
msgstr "Hojas de cálculo"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__thumbnail
msgid "Thumbnail"
msgstr "Miniatura"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard_group.py:0
#, python-format
msgid "You cannot delete %s as it is used in another module."
msgstr "No puede eliminar %s ya que se usa en otro módulo."
