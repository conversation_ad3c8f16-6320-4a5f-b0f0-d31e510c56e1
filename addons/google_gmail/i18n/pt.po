# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_gmail
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connect your Gmail account"
msgstr ""

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"
msgstr ""

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('server_type', '!=', 'gmail'), ('google_gmail_refresh_token', '=', False)]}\" class=\"badge text-bg-success\">\n"
"                        Gmail Token Valid\n"
"                    </span>"
msgstr ""

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('smtp_authentication', '!=', 'gmail'), ('google_gmail_refresh_token', '=', False)]}\" class=\"badge text-bg-success\">\n"
"                        Gmail Token Valid\n"
"                    </span>"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_access_token
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token
msgid "Access Token"
msgstr "Código de Acesso"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_access_token_expiration
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token_expiration
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token_expiration
msgid "Access Token Expiration Timestamp"
msgstr ""

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/controllers/main.py:0
#: code:addons/google_gmail/controllers/main.py:0
#, python-format
msgid "An error occur during the authentication process."
msgstr ""

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
#, python-format
msgid "An error occurred when fetching the access token."
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__smtp_authentication
msgid "Authenticate with"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_authorization_code
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_authorization_code
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_authorization_code
msgid "Authorization Code"
msgstr "Código de Autorização"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/fetchmail_server.py:0
#, python-format
msgid ""
"Connect your Gmail account with the OAuth Authentication process. \n"
"You will be redirected to the Gmail login page where you will need to accept the permission."
msgstr ""

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
#, python-format
msgid ""
"Connect your Gmail account with the OAuth Authentication process.  \n"
"By default, only a user with a matching email address will be able to use this server. To extend its use, you should set a \"mail.default.from\" system parameter."
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_identifier
msgid "Gmail Client Id"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_secret
msgid "Gmail Client Secret"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields.selection,name:google_gmail.selection__fetchmail_server__server_type__gmail
#: model:ir.model.fields.selection,name:google_gmail.selection__ir_mail_server__smtp_authentication__gmail
msgid "Gmail OAuth Authentication"
msgstr ""

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_google_gmail_mixin
msgid "Google Gmail Mixin"
msgstr ""

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "ID"
msgstr "ID"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "ID of your Google app"
msgstr ""

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "Servidor de E-mail de Entrada"

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
#, python-format
msgid ""
"Incorrect Connection Security for Gmail mail server %r. Please set it to "
"\"TLS (STARTTLS)\"."
msgstr ""

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_ir_mail_server
msgid "Mail Server"
msgstr ""

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
#, python-format
msgid "Only the administrator can link a Gmail mail server."
msgstr ""

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
#, python-format
msgid "Please configure your Gmail credentials."
msgstr ""

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please fill the \"Username\" field with your Gmail username (your email "
"address). This should be the same account as the one used for the Gmail "
"OAuthentication Token."
msgstr ""

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please leave the password field empty for Gmail mail server %r. The OAuth "
"process does not require it"
msgstr ""

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid "Read More"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_refresh_token
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_refresh_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_refresh_token
msgid "Refresh Token"
msgstr "Atualizar Código"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Secret"
msgstr "Segredo"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Secret of your Google app"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Tipo de servidor"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"Setup your Gmail API credentials in the general settings to link a Gmail "
"account."
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,help:google_gmail.field_fetchmail_server__google_gmail_uri
#: model:ir.model.fields,help:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,help:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "The URL to generate the authorization code from Google"
msgstr "O URL para gerar o código de autorização da Google"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_uri
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "URI"
msgstr "URI"
