<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="EditListInput" owl="1">
        <div>
            <input type="text" t-model="props.item.text" class="popup-input list-line-input"
                   placeholder="Serial/Lot Number" t-on-keyup="onKeyup" />
            <i class="oe_link_icon fa fa-trash-o" role="img" aria-label="Remove" title="Remove"
               t-on-click="() => this.trigger('remove-item', props.item)"></i>
        </div>
    </t>

</templates>
