# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-10 06:06+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr "Sử dụng máy in được kết nối với Hộp IoT"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>Tên tầng: </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sale: </strong>"
msgstr "<strong>Điểm bán lẻ: </strong>"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""
"Một tầng nhà hàng đại diện cho một nơi mà khách hàng được phục vụ. Đây là nơi mà bạn xác\n"
"                định và định vị các bàn."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Add"
msgstr "Thêm"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineNoteButton.js:0
#, python-format
msgid "Add Internal Note"
msgstr "Thêm ghi chú nội bộ"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr "Thêm tầng nhà hàng mới "

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid "Add a new restaurant order printer"
msgstr "Thêm máy in đơn hàng nhà hàng mới"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Add a tip"
msgstr "Thêm tiền tip"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "Add button"
msgstr "Thêm nút"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add internal notes on order lines for the kitchen"
msgstr "Thêm ghi chú nội bộ trong chi tiết đơn hàng cho nhà bếp"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add tip after payment (North America specific)"
msgstr "Thêm tiền tip sau thanh toán (chỉ dành cho Bắc Mĩ)"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Adjust Amount"
msgstr "Điều chỉnh số tiền"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid ""
"Adjust the amount authorized by payment terminals to add a tip after the "
"customers left or at the end of the day."
msgstr ""
"Điều chỉnh số tiền được thiết bị đầu cuối thanh toán cho phép để thêm tiền "
"tip sau khi khách hàng đã rời đi hoặc vào cuối ngày. "

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow Bill Splitting"
msgstr "Cho phép tách hóa đơn "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Allow custom internal notes on Orderlines."
msgstr "Cho phép ghi chú nội bộ tùy chỉnh trong chi tiết đơn hàng. "

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow to print receipt before payment"
msgstr "Cho phép in biên lai trước khi thanh toán"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr "Cho phép in Hoá đơn trước khi thanh toán"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Amount"
msgstr "Số tiền"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__name
msgid "An internal identification of a table"
msgstr "Một định danh nội bộ cho mỗi bàn"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__name
msgid "An internal identification of the printer"
msgstr "Một định danh nội bộ cho máy in"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "Hình thức"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Are you sure ?"
msgstr "Bạn có chắc không?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Back"
msgstr "Quay lại"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/ChromeWidgets/BackToFloorButton.xml:0
#: code:addons/pos_restaurant/static/src/xml/ChromeWidgets/BackToFloorButton.xml:0
#, python-format
msgid "Back to floor"
msgstr "Quay lại tầng"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "Màu nền"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "Hình nền"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr "Bánh burger thịt heo muối"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/PrintBillButton.xml:0
#, python-format
msgid "Bill"
msgstr "Hoá đơn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
#, python-format
msgid "Bill Printing"
msgstr "In hóa đơn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr "Tách hóa đơn "

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "Blocked action"
msgstr "Hành động bị chặn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Blue"
msgstr "Xanh dương"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "CANCELLED"
msgstr "HUỶ"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Cash %s"
msgstr "Tiền mặt %s"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Cash Bar"
msgstr "Tiền mặt"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr "Burger phô mai"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr "Bánh kẹp cà ri gà"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Close"
msgstr "Đóng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Close Tab"
msgstr "Đóng tab"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr "Bánh kẹp ba lát"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr "Coca-Cola"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Màu sắc"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Delete"
msgstr "Xoá"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Design floors and assign orders to tables"
msgstr "Thiết kế tầng và gán đơn hàng cho từng bàn"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "Đồ uống"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/ChromeWidgets/TicketButton.js:0
#, python-format
msgid "Due to a connection error, the orders are not synchronized."
msgstr "Do lỗi kết nối nên các đơn hàng không được đồng bộ."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Duplicate"
msgstr "Nhân bản"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"                Proxy where the printer can be found, and a list of product categories.\n"
"                An Order Printer will only print updates for products belonging to one of\n"
"                its categories."
msgstr ""
"Mỗi Máy in đơn hàng có một Địa chỉ IP xác định Proxy của Phần cứng/Hộp IoT\n"
"                nơi có thể tìm thấy máy in và danh sách danh mục sản phẩm.\n"
"                Máy in đơn hàng sẽ chỉ in thông tin cập nhật cho sản phẩm thuộc về một\n"
"                trong những danh mục này. "

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Early Receipt Printing"
msgstr "In biên lai sớm"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "Edit"
msgstr "Sửa"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr "Kích hoạt tính năng Tách hoá đơn ở Điểm bán lẻ"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/SubmitOrderButton.js:0
#, python-format
msgid "Failed in printing the changes in the order"
msgstr "Không thể in thay đổi đơn hàng"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "Tầng"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "Tên tầng"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "Sơ đồ tầng"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "Floor: %s - PoS Config: %s \n"
msgstr "Tầng: %s - Cấu hình PoS: %s \n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors"
msgstr "Tầng"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_table_management
msgid "Floors & Tables"
msgstr "Tầng & bàn"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors & Tables Map"
msgstr "Sơ đồ tầng & bàn"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "Món ăn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr ""
"Để thuận tiện, chúng tôi cung cấp các cách tính toán tiền tip như sau: "

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr "Nấm"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Green"
msgstr "Xanh lá"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Grey"
msgstr "Xám"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/TableGuestsButton.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
#, python-format
msgid "Guests"
msgstr "Khách"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "Guests ?"
msgstr "Khách?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Guests:"
msgstr "Khách:"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "Chiều cao"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "Vị trí theo phương ngang"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""
"Nếu sai, bàn sẽ bị hủy kích hoạt và sẽ không khả dụng trong điểm bán hàng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineNoteButton.xml:0
#, python-format
msgid "Internal Note"
msgstr "Ghi chú nội bộ"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__note
msgid "Internal Note added by the waiter."
msgstr "Ghi chú nội bộ từ nhân viên phục vụ bàn. "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Internal Notes"
msgstr "Ghi chú nội bộ"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Là một Nhà hàng/Quầy bar"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Keep Open"
msgstr "Tiếp tục mở"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Kitchen Notes"
msgstr "Ghi chú nhà bếp"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Kitchen Printers"
msgstr "Máy in nhà bếp"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table____last_update
msgid "Last Modified on"
msgstr "Chỉnh sửa lần cuối vào"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__multiprint_resume
msgid "Last printed state of the order"
msgstr "Trạng thái đơn hàng được in cuối cùng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Light grey"
msgstr "Xám nhạt"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Logo"
msgstr "Biểu tượng"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr "Sushi cuộn 18 miếng"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr "Sushi cá hồi 20 miếng"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr "Sushi Temaki 3 miếng"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Margherita"
msgstr "Pizza Margherita"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr "Minute Maid"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr "Bánh kẹp Mozzarella"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__multiprint_resume
msgid "Multiprint Resume"
msgstr "Tiếp tục in nhiều lần"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NEW"
msgstr "MỚI"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NOTE"
msgstr "GHI CHÚ"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "No Tip"
msgstr "Không có tip"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Note"
msgstr "Ghi chú"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/PrintBillButton.js:0
#, python-format
msgid "Nothing to Print"
msgstr "Không có gì để in"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Number of Seats ?"
msgstr "Số chỗ?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/ChromeWidgets/TicketButton.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Offline"
msgstr "Offline"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#, python-format
msgid "Ok"
msgstr "Đồng ý"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#, python-format
msgid "Open"
msgstr "Mở"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Orange"
msgstr "Cam"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/SubmitOrderButton.xml:0
#, python-format
msgid "Order"
msgstr "Đơn hàng"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr "Máy in đơn hàng"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_printer_form
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__printer_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_printer_ids
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_printer_all
msgid "Order Printers"
msgstr "Máy in đơn hàng"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"                order updates in the kitchen/bar when the waiter updates the order."
msgstr ""
"Máy in đơn hàng được nhà hàng và quán bar sử dụng để in\n"
"                thông tin cập nhật đơn hàng trong bếp/quầy bar khi nhân viên phục vụ bàn cập nhật đơn hàng. "

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer_form
msgid "POS Printer"
msgstr "Máy in POS"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "PRO FORMA"
msgstr "CHIẾU LỆ"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 formaggi "
msgstr "Mỳ ống 4 loại phô mai"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr "Mỳ Ý sốt cà chua"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#, python-format
msgid "Payment"
msgstr "Thanh toán"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_id
msgid "Point of Sale"
msgstr "Điểm bán lẻ"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Cấu hình điểm bán lẻ"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Chi tiết đơn hàng điểm bán lẻ"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "Đơn hàng điểm bán lẻ"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Thanh toán điểm bán lẻ"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_session
msgid "Point of Sale Session"
msgstr "Phiên POS"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_orderline_notes
msgid "Pos Iface Orderline Notes"
msgstr "Pos Iface Ghi chú chi tiết đơn hàng "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_printbill
msgid "Pos Iface Printbill"
msgstr "Pos Iface In hoá đơn"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_splitbill
msgid "Pos Iface Splitbill"
msgstr "Pos Iface Tách hoá đơn"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_is_order_printer
msgid "Pos Is Order Printer"
msgstr "POS làm Máy in đơn hàng"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_is_table_management
msgid "Pos Is Table Management"
msgstr "POS làm Quản lý bàn"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_set_tip_after_payment
msgid "Pos Set Tip After Payment"
msgstr "POS đặt tiền tip sau thanh toán"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#, python-format
msgid "Print"
msgstr "In"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Print orders at the kitchen, at the bar, etc."
msgstr "In đơn hàng ở nhà bếp, quầy bar,..."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "Danh mục sản phẩm được in"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__name
msgid "Printer Name"
msgstr "Tên máy in"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__printer_type
msgid "Printer Type"
msgstr "Loại máy in"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Printers"
msgstr "Máy in"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/SubmitOrderButton.js:0
#, python-format
msgid "Printing failed"
msgstr "In không thành công"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "Một số trình duyệt không hỗ trợ tính năng in"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid ""
"Printing is not supported on some browsers due to no default printing "
"protocol is available. It is possible to print your tickets by making use of"
" an IoT Box."
msgstr ""
"Một số trình duyệt không hỗ trợ tính năng in do không có sẵn giao thức in ấn"
" mặc định. Bạn có thể in phiếu bằng cách sử dụng Hộp IoT. "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "Địa chỉ IP của Proxy"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Purple"
msgstr "Tím"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Red"
msgstr "Đỏ"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Removing a table cannot be undone"
msgstr "Không thể hoàn tác việc xóa bàn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Rename"
msgstr "Đổi tên"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Reprint receipts"
msgstr "In lại biên lai"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Restaurant &amp; Bar"
msgstr "Nhà hàng &amp; Quầy bar"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "Tầng nhà hàng"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Tầng nhà hàng"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer
msgid "Restaurant Order Printers"
msgstr "Máy in đơn hàng nhà hàng"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_printer
msgid "Restaurant Printer"
msgstr "Máy in nhà hàng"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "Bàn nhà hàng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Reverse"
msgstr "Đảo ngược"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "Đảo ngược thanh toán"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr "Tròn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Round Shape"
msgstr "Hình tròn"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr "Cá hồi kèm bơ"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
#, python-format
msgid "Seats"
msgstr "Chỗ ngồi"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Served by"
msgstr "Phục vụ bởi"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr "Đặt tiền tip sau thanh toán"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Settle"
msgstr "Settle"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr "Hình dáng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Signature"
msgstr "Chữ ký"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__mp_skip
msgid "Skip line when sending ticket to kitchen printers."
msgstr "Bỏ dòng khi gửi phiếu tới máy in nhà bếp. "

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr "Bánh kẹp cá ngừ cay"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/SplitBillButton.xml:0
#, python-format
msgid "Split"
msgstr "Tách"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Split total or order lines"
msgstr "Tách tổng tiền hoặc chi tiết đơn hàng"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "Vuông"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Square Shape"
msgstr "Hình vuông"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Subtotal"
msgstr "Thành tiền"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
#, python-format
msgid "Table"
msgstr "Bàn"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__name
msgid "Table Name"
msgstr "Tên bàn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Table Name ?"
msgstr "Tên bàn?"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "Bàn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr "ĐT:"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "The %s is already used in another Pos Config."
msgstr "%s đã được sử dụng trong một Cấu hình POS khác."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "Địa chỉ IP hoặc hostname của proxy phần cứng của máy in"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr "Số lượng khách hàng được phục vụ bởi đơn hàng này."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid "The background color of the floor in a html-compatible format"
msgstr "Màu nền của tầng ở định dạng tương thích với html"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr "Số khách hàng mặc định có thể được phục vụ tại bàn này."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr "Các tầng nhà hàng được phục vụ bởi điểm bán hàng này."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr "Bàn phục vụ đơn hàng này"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr ""
"Màu của bàn, được thể hiện dưới dạng giá trị thuộc tính CSS 'nền' hợp lệ"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr "Chiều cao bàn tính bằng pixel"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr ""
"Vị trí của bàn theo phương ngang từ phía bên trái đến tâm của bàn, tính bằng"
" pixel"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr ""
"Vị trí của bàn theo phương dọc từ phía bên trên đến tâm của bàn, tính bằng "
"pixel."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr "Chiều rộng của bàn tính bằng pixel"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/PrintBillButton.js:0
#, python-format
msgid "There are no order lines"
msgstr "Không có chi tiết đơn hàng nào"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "This floor has no tables yet, use the"
msgstr "Tầng này chưa có bàn nào, hãy sử dụng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"Đơn hàng này chưa được đồng bộ với máy chủ. Bảo đảm đơn đã được đồng bộ và "
"thử lại. "

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Tint"
msgstr "Tông màu"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#, python-format
msgid "Tip"
msgstr "Tiền tip"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Tip:"
msgstr "Tip:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#, python-format
msgid "Tipping"
msgstr "Tip"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Total:"
msgstr "Tổng:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/TransferOrderButton.xml:0
#, python-format
msgid "Transfer"
msgstr "Lệnh chuyển hàng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Turquoise"
msgstr "Ngọc lam"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to change background color"
msgstr "Không thể thay đổi màu nền"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to create table because you are offline."
msgstr "Không thể tạo bàn vì bạn đang không có kết nối mạng. "

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to delete table"
msgstr "Không thể xóa bàn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to fetch orders"
msgstr "Không thể truy xuất đơn hàng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to get orders count"
msgstr "Không thể lấy số đơn hàng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/ChromeWidgets/TicketButton.js:0
#, python-format
msgid "Unknown error"
msgstr "Lỗi chưa xác định"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr "Đơn hàng chưa đồng bộ"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__uuid
msgid "Uuid"
msgstr "Uuid"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "Thuế GTGT:"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Vegetarian"
msgstr "Đồ chay"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "Ví trị theo phương dọc"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "Nước "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "Chiều rộng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "With a"
msgstr "Với một"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Yellow"
msgstr "Vàng"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "You cannot put a number that exceeds %s "
msgstr "Bạn không thể nhập số lớn hơn %s "

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr ""
"Bạn không thể xóa tầng đang được sử dụng trong một phiên PoS, hãy đóng (các)"
" phiên trước:\n"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a table that is used in a PoS session, close the "
"session(s) first."
msgstr ""
"Bạn không thể xóa bàn đang được sử dụng trong một phiên PoS, hãy đóng (các) "
"phiên trước. "

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "________________________"
msgstr "________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "______________________________________________"
msgstr "______________________________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "at"
msgstr "giá"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "at table"
msgstr "ở bàn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "button in the editing toolbar to create new tables."
msgstr "nút ở thanh công cụ để tạo các bàn mới."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "discount"
msgstr "chiết khấu"
