# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON> <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<PERSON><PERSON>@gmail.com>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# fardin marda<PERSON> <<EMAIL>>, 2023
# Mostafa Bar<PERSON>hory <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-10 06:06+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr "استفاده از چاپگری که به جعبه IoT متصل است"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sale: </strong>"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "فعال"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Add"
msgstr "افزودن"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineNoteButton.js:0
#, python-format
msgid "Add Internal Note"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid "Add a new restaurant order printer"
msgstr "افزودن چاپگر سفارش رستوران جدید"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Add a tip"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "Add button"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add internal notes on order lines for the kitchen"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add tip after payment (North America specific)"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Adjust Amount"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid ""
"Adjust the amount authorized by payment terminals to add a tip after the "
"customers left or at the end of the day."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow Bill Splitting"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Allow custom internal notes on Orderlines."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow to print receipt before payment"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Amount"
msgstr "مقدار"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__name
msgid "An internal identification of a table"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__name
msgid "An internal identification of the printer"
msgstr "شناسه داخلی چاپگر"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr "بایگانی شده"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Are you sure ?"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Back"
msgstr "بازگشت"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/ChromeWidgets/BackToFloorButton.xml:0
#: code:addons/pos_restaurant/static/src/xml/ChromeWidgets/BackToFloorButton.xml:0
#, python-format
msgid "Back to floor"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "رنگ پس‌زمینه"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "تصویر پس زمینه"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr "بیکن برگر"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/PrintBillButton.xml:0
#, python-format
msgid "Bill"
msgstr "صورتحساب"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
#, python-format
msgid "Bill Printing"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "Blocked action"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Blue"
msgstr "آبی"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "CANCELLED"
msgstr "لغو شد"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Cash %s"
msgstr "پول نقد %s"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Cash Bar"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Close"
msgstr "بستن"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Close Tab"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "رنگ"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Delete"
msgstr "حذف"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Design floors and assign orders to tables"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "نوشیدنی‌ها"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/ChromeWidgets/TicketButton.js:0
#, python-format
msgid "Due to a connection error, the orders are not synchronized."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Duplicate"
msgstr "تکثیر کردن"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"                Proxy where the printer can be found, and a list of product categories.\n"
"                An Order Printer will only print updates for products belonging to one of\n"
"                its categories."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Early Receipt Printing"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "Edit"
msgstr "ویرایش"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/SubmitOrderButton.js:0
#, python-format
msgid "Failed in printing the changes in the order"
msgstr "در چاپ تغییرات در سفارش ناموفق بود."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr ""

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "Floor: %s - PoS Config: %s \n"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_table_management
msgid "Floors & Tables"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors & Tables Map"
msgstr ""

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "غذا"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Green"
msgstr "سبز"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Grey"
msgstr "خاکستری"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/TableGuestsButton.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
#, python-format
msgid "Guests"
msgstr "مهمانان"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "Guests ?"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Guests:"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "ارتفاع"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "شناسه"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineNoteButton.xml:0
#, python-format
msgid "Internal Note"
msgstr "یادداشت داخلی"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__note
msgid "Internal Note added by the waiter."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Internal Notes"
msgstr "یادداشت های داخلی"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "یک بار/رستوران است"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Keep Open"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Kitchen Notes"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Kitchen Printers"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table____last_update
msgid "Last Modified on"
msgstr "آخرین اصلاح در"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "آخرین تغییر توسط"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__multiprint_resume
msgid "Last printed state of the order"
msgstr "آخرین وضعیت چاپ شده سفارش"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Light grey"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Logo"
msgstr "نشان"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Margherita"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__multiprint_resume
msgid "Multiprint Resume"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NEW"
msgstr "جدید"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NOTE"
msgstr "یادداشت"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "No Tip"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Note"
msgstr "یادداشت"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/PrintBillButton.js:0
#, python-format
msgid "Nothing to Print"
msgstr "چیزی برای چاپ کردن وجود ندارد"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Number of Seats ?"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/ChromeWidgets/TicketButton.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Offline"
msgstr "آفلاین"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#, python-format
msgid "Ok"
msgstr "تأیید"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#, python-format
msgid "Open"
msgstr "باز"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Orange"
msgstr "نارنجی"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/SubmitOrderButton.xml:0
#, python-format
msgid "Order"
msgstr "سفارش"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr "پرینتر سفارش"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_printer_form
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__printer_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_printer_ids
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_printer_all
msgid "Order Printers"
msgstr "پرینترهای سفارشات"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"                order updates in the kitchen/bar when the waiter updates the order."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer_form
msgid "POS Printer"
msgstr "پرینتر POS"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "PRO FORMA"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 formaggi "
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#, python-format
msgid "Payment"
msgstr "پرداخت"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_id
msgid "Point of Sale"
msgstr "پایانه فروش"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "پیکربندی پایانه فروش"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "سطرهای سفارش پایانه فروش"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "سفارشات پایانه فروش"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr "پرداخت های پایانه فروش"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_session
msgid "Point of Sale Session"
msgstr "نشست پایانه فروش"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_orderline_notes
msgid "Pos Iface Orderline Notes"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_printbill
msgid "Pos Iface Printbill"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_splitbill
msgid "Pos Iface Splitbill"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_is_order_printer
msgid "Pos Is Order Printer"
msgstr "پرینتر سفارش نقطه فروش"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_is_table_management
msgid "Pos Is Table Management"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_set_tip_after_payment
msgid "Pos Set Tip After Payment"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#, python-format
msgid "Print"
msgstr "چاپ"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Print orders at the kitchen, at the bar, etc."
msgstr "چاپ سفارشات در آشپزخانه، در بار و غیره."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "دسته‌بندی‌های محصولات چاپ‌شده"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__name
msgid "Printer Name"
msgstr "نام پرینتر"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__printer_type
msgid "Printer Type"
msgstr "نوع چاپگر"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Printers"
msgstr "چاپگرها"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/SubmitOrderButton.js:0
#, python-format
msgid "Printing failed"
msgstr "چاپ شکست خورد"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "چاپ در برخی از مرورگرها پشتیبانی نمی شود"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid ""
"Printing is not supported on some browsers due to no default printing "
"protocol is available. It is possible to print your tickets by making use of"
" an IoT Box."
msgstr ""
"به دلیل وجود نداشتن پروتکل چاپ پیش‌فرض، چاپ در برخی از مرورگرها پشتیبانی "
"نمی‌شود. با استفاده از جعبه اینترنت اشیا می توانید تیکت های خود را چاپ کنید."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "آدرس IP پراکسی"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Purple"
msgstr "بنفش"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Red"
msgstr "قرمز"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Removing a table cannot be undone"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Rename"
msgstr "تغییر نام دهید"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Reprint receipts"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Restaurant &amp; Bar"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer
msgid "Restaurant Order Printers"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_printer
msgid "Restaurant Printer"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Reverse"
msgstr "معکوس"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "پرداخت معکوس"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Round Shape"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr "سالمون و آواکادو"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
#, python-format
msgid "Seats"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "دنباله"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Served by"
msgstr "سرو شده توسط"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Settle"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr "شکل"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Signature"
msgstr "امضا"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__mp_skip
msgid "Skip line when sending ticket to kitchen printers."
msgstr "هنگام ارسال بلیط به چاپگرهای آشپزخانه از خط عبور کنید."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/SplitBillButton.xml:0
#, python-format
msgid "Split"
msgstr "جدا کردن"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Split total or order lines"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "مربع"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Square Shape"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Subtotal"
msgstr "جمع جزء"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
#, python-format
msgid "Table"
msgstr "میز"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__name
msgid "Table Name"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Table Name ?"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "جداول"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr "تلفن تماس:"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "The %s is already used in another Pos Config."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "آدرس IP یا نام میزبان پروکسی سخت‌افزار چاپگر"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid "The background color of the floor in a html-compatible format"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/PrintBillButton.js:0
#, python-format
msgid "There are no order lines"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "This floor has no tables yet, use the"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"این سفارش هنوز با سرور هماهنگ نشده است. مطمئن شوید که هماهنگ شده است و سپس "
"دوباره تلاش کنید."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Tint"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#, python-format
msgid "Tip"
msgstr "نکته"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Tip:"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#, python-format
msgid "Tipping"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Total:"
msgstr "مجموع:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/TransferOrderButton.xml:0
#, python-format
msgid "Transfer"
msgstr "انتقال"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Turquoise"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to change background color"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to create table because you are offline."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to delete table"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to fetch orders"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to get orders count"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/ChromeWidgets/TicketButton.js:0
#, python-format
msgid "Unknown error"
msgstr "خطای ناشناخته"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr "سفارش همگام‌نشده"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__uuid
msgid "Uuid"
msgstr "Uuid"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "مالیات ارزش‌افزوده:"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Vegetarian"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "عرض"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "With a"
msgstr "با یک"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Yellow"
msgstr "زرد"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "You cannot put a number that exceeds %s "
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a table that is used in a PoS session, close the "
"session(s) first."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "________________________"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "______________________________________________"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "at"
msgstr "در"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "at table"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "button in the editing toolbar to create new tables."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "discount"
msgstr "تخفیف"
