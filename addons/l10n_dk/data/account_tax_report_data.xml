<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_tax_report_skat_dk" model="account.report">
        <field name="name">VAT Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.dk"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="account_tax_report_skat_dk_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_tax_report_line_sales_group" model="account.report.line">
                <field name="name">Skyldig moms</field>
                <field name="code">DUE_VAT</field>
                <field name="aggregation_formula">DK_SALES_VAT.balance + REVERSE_CHARGE_MERCHANDISES_PURCHASED_OUT_DK.balance + REVERSE_CHARGE_SERVICES_PURCHASED_OUT_DK.balance</field>
                <field name="children_ids">
                    <record id="account_tax_report_line_sales_tax" model="account.report.line">
                        <field name="name">Salgsmoms (udgående moms)</field>
                        <field name="code">DK_SALES_VAT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_sales_tax_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">UM</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_international_purchase_products" model="account.report.line">
                        <field name="name">Moms af varekøb i udlandet (både EU og lande uden for EU)</field>
                        <field name="code">REVERSE_CHARGE_MERCHANDISES_PURCHASED_OUT_DK</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_international_purchase_products_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">MVU</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_international_purchase_services" model="account.report.line">
                        <field name="name">Moms af ydelseskøb i udlandet med omvendt betalingspligt</field>
                        <field name="code">REVERSE_CHARGE_SERVICES_PURCHASED_OUT_DK</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_international_purchase_services_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">MYUOB</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_line_deduction_group" model="account.report.line">
                <field name="name">Fradrag</field>
                <field name="code">VAT_TO_DEDUCED</field>
                <field name="aggregation_formula">PURCHASE_VAT.balance + OIL_AND_BOTTLED_GAS_TAX.balance + ELECTRICITY_TAX.balance + NATURAL_AND_CITY_GAS_TAX.balance + COAL_TAX.balance + CO2_TAX.balance + WATER_TAX.balance</field>
                <field name="children_ids">
                    <record id="account_tax_report_line_deduction_purchase_tax" model="account.report.line">
                        <field name="name">Købsmoms (indgående moms)</field>
                        <field name="code">PURCHASE_VAT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_deduction_purchase_tax_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KM</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_deduction_oil_bottle_tax" model="account.report.line">
                        <field name="name">Olie- og flaskegasafgift</field>
                        <field name="code">OIL_AND_BOTTLED_GAS_TAX</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_deduction_oil_bottle_tax_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">OFA</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_deduction_electrical_tax" model="account.report.line">
                        <field name="name">Elafgift</field>
                        <field name="code">ELECTRICITY_TAX</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_deduction_electrical_tax_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">EA</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_deduction_gas_tax" model="account.report.line">
                        <field name="name">Naturgas- og bygasafgift</field>
                        <field name="code">NATURAL_AND_CITY_GAS_TAX</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_deduction_gas_tax_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">NOBA</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_deduction_coal_tax" model="account.report.line">
                        <field name="name">Kulafgift</field>
                        <field name="code">COAL_TAX</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_deduction_coal_tax_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">KA</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_deduction_co2_tax" model="account.report.line">
                        <field name="name">CO2-afgift</field>
                        <field name="code">CO2_TAX</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_deduction_co2_tax_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">CO2A</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_deduction_water_tax" model="account.report.line">
                        <field name="name">Vandafgift</field>
                        <field name="code">WATER_TAX</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_deduction_water_tax_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VA</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_line_vat_statement" model="account.report.line">
                <field name="name">Momsangivelse (positivt beløb = betale, negativt beløb = penge tilgode)</field>
                <field name="aggregation_formula">DUE_VAT.balance - VAT_TO_DEDUCED.balance</field>
            </record>
            <record id="account_tax_report_line_additional_info" model="account.report.line">
                <field name="name">Supplerende oplysninger</field>
                <field name="children_ids">
                    <record id="account_tax_report_line_section_a_products" model="account.report.line">
                        <field name="name">Rubrik A: varer (EU)</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_section_a_products_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">R-A-V</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_section_a_services" model="account.report.line">
                        <field name="name">Rubrik A = ydelser (EU)</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_section_a_services_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">R-A-Y</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_section_b_product_eu" model="account.report.line">
                        <field name="name">Rubrik B = varer (Indberettes til EU-salg uden moms)</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_section_b_product_eu_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">R-B-MR</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_section_b_products_non_eu" model="account.report.line">
                        <field name="name">Rubrik B = varer (Indberettes ikke til EU-salg uden moms)</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_section_b_products_non_eu_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">R-B-UR</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_section_b_services" model="account.report.line">
                        <field name="name">Rubrik B = ydelser - (EU-salg uden moms)</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_section_b_services_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">R-C-MR</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_section_c" model="account.report.line">
                        <field name="name">Rubrik C - Værdien af andre varer og ydelser (uanset landet)</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_section_c_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">R-C-UR</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
