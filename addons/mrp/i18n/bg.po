# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp
# 
# Translators:
# KeyVillage, 2023
# <PERSON> <<EMAIL>>, 2023
# kalat<PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# Весел Кара<PERSON>то<PERSON><PERSON>ов <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <igo<PERSON><EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# aleksanda<PERSON> i<PERSON>, 2023
# Al<PERSON><PERSON> <<EMAIL>>, 2023
# Ивайло М<PERSON><PERSON>инов <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <rumena.geor<PERSON>@gmail.com>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>garita <PERSON>eva, 2024
# <PERSON>na <PERSON><PERSON>, 2024
# <PERSON> <PERSON><PERSON><PERSON>eva <<EMAIL>>, 2025
# Emil Krastev, 2025
# <PERSON> <PERSON>vski, 2025
# Vese<PERSON> S<PERSON>kova, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 20:34+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: Veselina Slavkova, 2025\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_state
msgid ""
" * Draft: The MO is not confirmed yet.\n"
" * Confirmed: The MO is confirmed, the stock rules and the reordering of the components are trigerred.\n"
" * In Progress: The production has started (on the MO or on the WO).\n"
" * To Close: The production is done, the MO has to be closed.\n"
" * Done: The MO is closed, the stock moves are posted. \n"
" * Cancelled: The MO has been cancelled, can't be confirmed anymore."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid " <br/><br/> The components will be taken from <b>%s</b>."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__all_available
msgid " When all components are available"
msgstr "Когато всички компоненти са налични"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_count
msgid "# Bill of Material"
msgstr "# Фактура за покупка на материали"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__used_in_bom_count
msgid "# BoM Where Used"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_ready_count
msgid "# Read Work Orders"
msgstr "# Отчетете работни поръчки "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_count
msgid "# Work Orders"
msgstr "# Работни поръчки"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_template__used_in_bom_count
msgid "# of BoM Where is Used"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "%(qty)s %(measure)s unbuilt in %(order)s"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
#, python-format
msgid "%i work orders"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "%s (new) %s"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "%s Child MO's"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "%s cannot be deleted. Try to cancel them before."
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_bom_structure
msgid "'Bom Overview - %s' % object.display_name"
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_finished_product
msgid "'Finished products - %s' % object.name"
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_production_order
msgid "'Production Order - %s' % object.name"
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_workorder
msgid "'Work Order - %s' % object.name"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#: code:addons/mrp/models/stock_rule.py:0
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_leg_product_template
msgid "18″ x 2½″ Square Leg"
msgstr "18″ x 2½″ квадратен крак"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ": Insufficient Quantity To Unbuild"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-play\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your product\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span attrs=\"{'invisible': [('state', '=', 'draft')]}\">/</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"fw-bold text-nowrap\">To Produce</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Backorders</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Child MO</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Load</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Lost</span>"
msgstr "<span class=\"o_stat_text\">Загуба</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "<span class=\"o_stat_text\">Manufactured</span>"
msgstr "<span class=\"o_stat_text\">Произведено</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">OEE</span>"
msgstr ""
"<span class=\"o_stat_text\">Цялостна ефективност на оборудването - "
"OEE</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">Operations<br/>Performance</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Performance</span>"
msgstr "<span class=\"o_stat_text\">Производителност</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span class=\"o_stat_text\">Scraps</span>"
msgstr "<span class=\"o_stat_text\">Скрап</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Source MO</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Unbuilds</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid ""
"<span>\n"
"            Components\n"
"        </span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Actions</span>"
msgstr "<span>Действия</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "<span>Generate</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>New</span>"
msgstr "<span>Ново</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>Orders</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>PLAN ORDERS</span>"
msgstr "<span>ПЛАНИРАЙ ПОРЪЧКИ</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_report_delivery_no_kit_section
msgid "<span>Products not associated with a kit</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Отчети</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>WORK ORDERS</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>minutes</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<strong class=\"mr8 oe_inline\">to</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Actual Duration (minutes)</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Deadline:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Description:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Duration (minutes)</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Effectiveness Category: </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Finished Product:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Is a Blocking Reason? </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Manufacturing Order:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Operation</strong>"
msgstr "<strong>Операция</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Product:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity Producing:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity to Produce:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Reason: </strong>"
msgstr "<strong>Причина: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Responsible:</strong><br/>"
msgstr "<strong>Отговорен:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source:</strong><br/>"
msgstr "<strong>Източник:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Start Date: </strong>"
msgstr "<strong>Дата начало:</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>Дата спиране: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "<strong>Unit Cost</strong>"
msgstr "<strong>Единична цена</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr "<strong>Работен център</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Workcenter: </strong>"
msgstr "<strong>Работен център</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "A BoM of type kit is used to split the product into its components."
msgstr ""
"Спецификация от тип комплект се използва за разделянето й по компоненти."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "A Manufacturing Order is already done or cancelled."
msgstr "Производствена поръчка е вече изпълнена или отменена."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid ""
"A product with a kit-type bill of materials can not have a reordering rule."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__access_token
msgid "Access Token"
msgstr "Токен за достъп"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_rule__action
msgid "Action"
msgstr "Действие"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction
msgid "Action Needed"
msgstr "Необходимо Действие"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__active
#: model:ir.model.fields,field_description:mrp.field_mrp_document__active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__active
msgid "Active"
msgstr "Активно"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_ids
msgid "Activities"
msgstr "Дейности"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Декорация за изключение на дейност"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_state
msgid "Activity State"
msgstr "Състояние на Дейност"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_icon
msgid "Activity Type Icon"
msgstr "Икона за Вид Дейност"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Add a description..."
msgstr "Добавете описание ..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Add a line"
msgstr "Добави ред"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Add by-products to bills of materials. This can be used to get several "
"finished products as well. Without this option you only do: A + B = C. With "
"the option: A + B = C + D."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Add quality checks to your work orders"
msgstr "Добавете проверка качество към работни поръчки"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_tag
msgid "Add tag for the workcenter"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Additional time in minutes for the cleaning."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_start
msgid "Additional time in minutes for the setup."
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Administrator"
msgstr "Администратор"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "All"
msgstr "Всички"

#. module: mrp
#. odoo-python
#: code:addons/mrp/controller/main.py:0
#, python-format
msgid "All files uploaded"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_line_bom_qty_zero
msgid ""
"All product quantities must be greater or equal to 0.\n"
"Lines with 0 quantities can be used as optional lines. \n"
"You should install the mrp_byproduct module if you want to manage extra products on BoMs !"
msgstr ""
"Всички продуктови количества трябва да са по-големи от 0.\n"
"Редове с 0 количества са позволени за опционалните редове\n"
"Трябва да инсталирате модула mrp_byproduct, ако искате да управлявате доп. продукти в спецификациите!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Allocation"
msgstr "Разпределяне"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_reception_report
msgid "Allocation Report for Manufacturing Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__allow_workorder_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__allow_workorder_dependencies
msgid "Allow Work Order Dependencies"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_auto_consume_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_auto_consume_components_lots
msgid "Allow automatic consumption of tracked components that are reserved"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Allow manufacturing users to modify quantities to consume, without the need "
"for prior approval"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__flexible
msgid "Allowed"
msgstr "Позволен"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reserve_visible
msgid "Allowed to Reserve Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__warning
msgid "Allowed with warning"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid "Alternative Workcenters"
msgstr "Алтернативни работни центрове"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid ""
"Alternative workcenters that can be substituted to this one in order to "
"dispatch production"
msgstr ""
"Алтернативните работни центрове могат да бъдат замествани от този за да се "
"маршрутизира производството"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid ""
"An unbuild order is used to break down a finished product into its "
"components."
msgstr ""
"Разглобяване се използва за разделянето на краен продукт на компонентите му."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Apply"
msgstr "Приложи"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "Apply on Variants"
msgstr "Приложи върху вариантите"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Approve"
msgstr "Одобрете"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Archive Operation"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Archived"
msgstr "Архивирано"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_assign_serial_numbers_production
msgid "Assign Serial Numbers"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "At the creation of a Manufacturing Order."
msgstr "При създаване на Нареждане за производство."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "At the creation of a Stock Transfer."
msgstr "При създаване на Складово движение."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Attached To"
msgstr "Прикачено към"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_attachment_count
msgid "Attachment Count"
msgstr "Брой Прикачени Файлове"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__local_url
msgid "Attachment URL"
msgstr " URL на прикачен файл"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.js:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
#, python-format
msgid "Attachments"
msgstr "Прикачени файлове"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__attachments_count
msgid "Attachments Count"
msgstr "Брой прилoжения"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_variant_attributes
msgid "Attribute Values"
msgstr "Атрибутивни стойности"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#, python-format
msgid "Availabilities"
msgstr "Възможности"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#, python-format
msgid "Availabilities on products."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__availability
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "Availability"
msgstr "Наличност"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Availability Losses"
msgstr "Загуби на наличност"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__available
#, python-format
msgid "Available"
msgstr "На разположение"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "Avatar"
msgstr "Аватар"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_product__produce_delay
#: model:ir.model.fields,help:mrp.field_product_template__produce_delay
msgid ""
"Average lead time in days to manufacture this product. In the case of multi-"
"level BOM, the manufacturing lead times of the components will be added. In "
"case the product is subcontracted, this can be used to determine the date at"
" which components should be sent to the subcontractor."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__variant_bom_ids
msgid "BOM Product Variants"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "BOM Product Variants needed to apply this line."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_line_ids
msgid "BOM lines of the referred bom"
msgstr ""
"Редове на фактура за поръчка на материали за реферираната фактура за поръчка"
" на материали"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder_line
msgid "Backorder Confirmation Line"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_backorder_line_ids
msgid "Backorder Confirmation Lines"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Backorder MO"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Backorder MO's"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__backorder_sequence
msgid "Backorder Sequence"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__backorder_sequence
msgid ""
"Backorder sequence, if equals to 0 means there is not related backorder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Barcode"
msgstr "Баркод"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode_batch
msgid "Based on"
msgstr "Основан на"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Bill of Material"
msgstr "Фактура за покупка на материали"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Ред на Списък с материали"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "Bill of Material line"
msgstr "Ред на Списък с материали"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_bom_id
msgid "Bill of Material used on the Production Order"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_ids
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "Bill of Materials"
msgstr "Списък с материали"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__mo_bom_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_bom_id
msgid ""
"Bill of Materials allow you to define the list of required components to "
"make a finished product."
msgstr ""
"Списък с материали позволява да опишете необходимите компоненти за "
"създадаване на краен продукт."

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
msgid "Bills of Materials"
msgstr "Списъци с материали"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a manufacturing\n"
"                order or a pack of products."
msgstr ""
"Списък с материали позволява да опишете необходимите\n"
"                 компоненти за създадаване на краен продукт; чрез\n"
"                 нареждане за прозиводство или пакетиране на продукти."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block"
msgstr "Блокирайте"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter_wo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block Workcenter"
msgstr "Блокирайте работен център"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__blocked
msgid "Blocked"
msgstr "Блокиран"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__blocked_by_workorder_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Blocked By"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked Time"
msgstr "Блокирано време"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked hours over the last month"
msgstr "Блокирани часове през последния месец"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__name
msgid "Blocking Reason"
msgstr "Причина за блокиране"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__needed_by_workorder_ids
msgid "Blocks"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_id
msgid "BoM"
msgstr "Списък с материали"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_line_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "BoM Components"
msgstr "Компоненти на списъка с материали"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "BoM Cost"
msgstr "Разход по Списъка с материали"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__bom_line_id
msgid "BoM Line"
msgstr "Ред на Списъка с материали"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__bom_line_ids
msgid "BoM Lines"
msgstr "Редове на Списъка с материали"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mrp_bom
#: model:ir.actions.report,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Overview"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__type
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "BoM Type"
msgstr "Вид на Списъка от материали"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_bolt_product_template
msgid "Bolt"
msgstr "Болт"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#, python-format
msgid "By-Products"
msgstr "Вторични продукти"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_id
msgid "By-product"
msgstr "Вторичен продукт"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "By-product %s should not be the same as BoM product."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__byproduct_id
msgid "By-product line that generated the move in a manufacturing order"
msgstr ""
"Ред на вторичен продукт, който създаде движението в нареждането за "
"производство"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__byproduct_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__byproduct_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "By-products"
msgstr "Вторични продукти"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "By-products cost shares must be positive."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_byproduct
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_byproduct_form_view
msgid "Byproduct"
msgstr "Вторичен продукт"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#, python-format
msgid "Byproducts"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any production location."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Cancel"
msgstr "Отказ"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__cancel
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__cancel
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Cancelled"
msgstr "Отменен"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/product.py:0
#, python-format
msgid ""
"Cannot compute days to prepare due to missing route info for at least 1 "
"component or for the final product."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Cannot delete a manufacturing order in done state."
msgstr "Не мога да изтрия нареждане, което е в състояние 'изпълнено'"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__default_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__capacity
msgid "Capacity"
msgstr "Капацитет"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_positive_capacity
msgid "Capacity should be a positive number."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid "Carried Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__loss_type
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Category"
msgstr "Категория"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr "Промяна на кол. на продукта"

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Production Qty"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
msgid "Change Quantity To Produce"
msgstr "Промени количеството за производство"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Check availability"
msgstr "Провери наличност"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__checksum
msgid "Checksum/SHA1"
msgstr "Контролна сума/SHA1"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_stop
msgid "Cleanup Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Cleanup Time (minutes)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__code
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view_kanban
msgid "Code"
msgstr "Код"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__color
msgid "Color"
msgstr "Цвят"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__color
msgid "Color Index"
msgstr "Цветен индекс"

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr "Фирми"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_document__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr "Фирма"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Component"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability
msgid "Component Status"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
#, python-format
msgid "Component of Draft MO"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_raw_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Components"
msgstr "Компоненти"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability_state
msgid "Components Availability State"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_src_id
msgid "Components Location"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__priority
msgid ""
"Components will be reserved first for the MO with the highest priorities."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__auto
msgid "Compute based on tracked time"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "Compute from BoM"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid ""
"Compute the days required to resupply all components from BoM, by either "
"buying or manufacturing the components and/or subassemblies."
"                                       Also note that purchase security lead"
" times will be added when appropriate."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_computed_on
msgid "Computed on last"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_res_config_settings
msgid "Config Settings"
msgstr "Настройки"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Configuration"
msgstr "Конфигурация "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Confirm"
msgstr "Потвърждение"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__confirm_cancel
msgid "Confirm Cancel"
msgstr "Потвърдете отмяна"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Confirmed"
msgstr "Потвърдена"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "Consume"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_auto_consume_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_auto_consume_components_lots
msgid "Consume Reserved Lots/Serial Numbers automatically"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_consumed_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Consumed"
msgstr "Изразходван"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__consume_line_ids
msgid "Consumed Disassembly Lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__consume_unbuild_id
msgid "Consumed Disassembly Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__operation_id
msgid "Consumed in Operation"
msgstr "Изразходвани в процеса на работа"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_production__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__consumption
msgid "Consumption"
msgstr "Потребление"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_consumption_warning
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Consumption Warning"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_production__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Преобразуването на мерните единици може да възникне, само ако те принадлежат"
" към една и съща категория. Преобразуването ще се извърши въз основа на "
"съотношенията."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Copy Existing Operations"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_copy_to_bom_tree_view
msgid "Copy selected operations"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__cost_share
#: model:ir.model.fields,field_description:mrp.field_stock_move__cost_share
msgid "Cost Share (%)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__costs_hour
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__costs_hour
msgid "Cost per hour"
msgstr "Разход на час"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Costing Information"
msgstr "Информация за цеообразуването"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#, python-format
msgid "Costs"
msgstr "Разходи"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/views/fields/google_slides_viewer.js:0
#, python-format
msgid "Could not display the selected spreadsheet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_backorder_count
msgid "Count of linked backorder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Create Backorder"
msgstr "Създайте компенсираща поръчка"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create a Backorder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid ""
"Create a backorder if you expect to process the remaining products later. Do"
" not create a backorder if you will not process the remaining products."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_report
msgid "Create a new manufacturing order"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Create a new operation"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "Create a new work center"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_workcenter_report
msgid "Create a new work orders performance"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_product__days_to_prepare_mo
#: model:ir.model.fields,help:mrp.field_product_template__days_to_prepare_mo
msgid ""
"Create and confirm Manufacturing Orders this many days in advance, to have enough time to replenish components or manufacture semi-finished products.\n"
"Note that security lead times will also be considered when appropriate."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create backorder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid ""
"Create operation level dependencies that will influence both planning and "
"the status of work orders upon MO confirmation. If this feature is ticked, "
"and nothing is specified, Odoo will assume that all operations can be "
"started simultaneously."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__created_production_id
msgid "Created Production Order"
msgstr "Създадена производствена поръчка"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_uid
msgid "Created by"
msgstr "Създадено от"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Creates a new serial/lot number"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Creation"
msgstr "Създаване"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__currency_id
msgid "Currency"
msgstr "Валута"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Настоящо количество продукти.\n"
"В контекст на единичната стокова локация, това включва стоки, складирани в тази локация, или в някоя от нейните подлокации.\n"
"В контекста на единичния склад, това включва стоки, складирани в стоковата локация на въпросния склад или в някоя от неговите подлокации.\n"
"складирани в стоковата локация на склада на този магазин или в някоя от неговите подлокации.\n"
"В противен случай това включва стоки, складирани във всяка стокова локация от 'вътрешен' тип."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_producing
msgid "Currently Produced Quantity"
msgstr "Настоящо произведено количество"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_description_variants
msgid "Custom Description"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__db_datas
msgid "Database Data"
msgstr "Database Data"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Date"
msgstr "Дата"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_planned_finished
msgid "Date at which you plan to finish the production."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_planned_start
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_date
msgid "Date at which you plan to start the production."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_pdf_line
#, python-format
msgid "Days"
msgstr "Дни"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "Days to Supply Components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__days_to_prepare_mo
#: model:ir.model.fields,field_description:mrp.field_product_template__days_to_prepare_mo
msgid "Days to prepare Manufacturing Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_deadline
msgid "Deadline"
msgstr "Краен срок"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Default Duration"
msgstr "Времетраене"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__default_capacity
msgid ""
"Default number of pieces (in product UoM) that can be produced in parallel "
"(at the same time) at this work center. For example: the capacity is 5 and "
"you need to produce 10 units, then the operation time listed on the BOM will"
" be multiplied by two. However, note that both time before and after "
"production will only be counted once."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Единица по подразбиране за всички складови операции."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid ""
"Define the components and finished products you wish to use in\n"
"                bill of materials and manufacturing orders."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__consumption
msgid ""
"Defines if you can consume more or less components than the quantity defined on the BoM:\n"
"  * Allowed: allowed for all manufacturing users.\n"
"  * Allowed with warning: allowed for all manufacturing users with summary of consumption differences when closing the manufacturing order.\n"
"  Note that in the case of component Manual Consumption, where consumption is registered manually exclusively, consumption warnings will still be issued when appropriate also.\n"
"  * Blocked: only a manager can close a manufacturing order when the BoM consumption is not respected."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delay_alert_date
msgid "Delay Alert Date"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Delete"
msgstr "Изтриване"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delivery_count
msgid "Delivery Orders"
msgstr "Поръчки за доставка"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__description
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__description
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_note
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr "Описание"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description of the work center..."
msgstr "Описание на работен център"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_dest_id
msgid "Destination Location"
msgstr "Локация на местонахождение"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unbuild_id
msgid "Disassembly Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Discard"
msgstr "Отхвърлете"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_document__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__display_name
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_serial_mass_produce
msgid "Display the serial mass produce wizard action"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lot_ids
msgid "Display the serial number shortcut on the moves"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.xml:0
#, python-format
msgid "Display:"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Displays the consumed Lot/Serial Numbers."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "Do you confirm you want to unbuild"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Document"
msgstr "Документ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Documentation"
msgstr "Документация"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__is_done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Done"
msgstr "Извършен"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Download"
msgstr "Изтеглете от системата"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__draft
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__draft
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Draft"
msgstr "Чернова "

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_drawer_product_template
msgid "Drawer Black"
msgstr "Черно чекмедже"

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_case_product_template
msgid "Drawer Case Black"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_drawer_drawer_product_template
msgid "Drawer on casters for great usability."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Dropdown menu"
msgstr "Падащо меню"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "Duplicate Serial Numbers (%s)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__duration
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration"
msgstr "Продължителност"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Duration (minutes)"
msgstr "Времетраене (минути)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode
msgid "Duration Computation"
msgstr "Изчисли времетраене"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_percent
msgid "Duration Deviation (%)"
msgstr "Отклонение от продължителността (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_unit
msgid "Duration Per Unit"
msgstr "Продължителност на единица"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Edit"
msgstr "Редактирай"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_type
msgid "Effectiveness"
msgstr "Ефективност"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_type
msgid "Effectiveness Category"
msgstr "Категория ефективност"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_end
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_finished
msgid "End Date"
msgstr "Крайна Дата"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,help:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,help:mrp.field_mrp_workorder__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Проследете стоките в склада Ви."

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#, python-format
msgid "Estimated %s"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s) occurred on the manufacturing order(s):"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s):"
msgstr "Изключение(я):"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "Existing Serial Numbers (%s)"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Exp %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__expected
msgid "Expected"
msgstr "Очакван"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#, python-format
msgid "Expected %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_duration_expected
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_expected
msgid "Expected Duration"
msgstr "Очаквана продължителност"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Expected Duration (minutes)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__expected_qty
msgid "Expected Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__datas
msgid "File Content (base64)"
msgstr "Съдържание на файл (base64)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__raw
msgid "File Content (raw)"
msgstr "Съдържание на файл (raw)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__file_size
msgid "File Size"
msgstr "Размер на файл"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#, python-format
msgid "Files attached to the product."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Filters"
msgstr "Филтри"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__done
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Finished"
msgstr "Завършен"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__order_finished_lot_id
msgid "Finished Lot/Serial Number"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_finished_ids
msgid "Finished Moves"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__finished_move_line_ids
msgid "Finished Product"
msgstr "Готов продукт"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_finished_product
msgid "Finished Product Label (PDF)"
msgstr ""

#. module: mrp
#: model:ir.actions.report,name:mrp.label_manufacture_template
msgid "Finished Product Label (ZPL)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_finished_ids
msgid "Finished Products"
msgstr "Произведени продукти"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_dest_id
msgid "Finished Products Location"
msgstr "Място за готова продукция"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__consumption
msgid "Flexible Consumption"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_follower_ids
msgid "Followers"
msgstr "Последователи"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_partner_ids
msgid "Followers (Partners)"
msgstr "Последователи (партньори)"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Икона, примерно fa-tasks"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Force"
msgstr "Сила"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#, python-format
msgid "Forecast Report"
msgstr "Отчет за прогнозата"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Прогнозно количество (изчислено като 'в наличност' - изходящо + входящо)\n"
"В контекста на единичната стокова локация това включва стоки, складирани в тази локация или в някоя от нейните подлокации.\n"
"В контекста на единичния склад това включва стоки, складирани в стоковата локация на този склад или в някоя от неговите подлокации.\n"
"В противен случай това включва стоки, складирани в която и да е стокова локация от 'вътрешен' тип."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Forecasted"
msgstr "прогнозирано"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__forecasted_issue
msgid "Forecasted Issue"
msgstr "Прогнозиран проблем"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#, python-format
msgid "Free to Use"
msgstr "Свободен за използване"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "Free to Use / On Hand"
msgstr "Свободно за използване / в наличност"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "From"
msgstr "От"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Fully Productive"
msgstr "Напълно продуктивен"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Future Activities"
msgstr "Бъдещи дейности"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr "Обща информация"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Generate Serial Numbers"
msgstr "Генериране на серийни номера"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
msgid "Get statistics about the work orders duration related to this routing."
msgstr ""
"Получаване на статистика за продължителността на работните поръчки, свързани"
" с тази маршрутизация."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__sequence
msgid ""
"Gives the sequence order when displaying a list of routing Work Centers."
msgstr ""
"Задава реда на последователност, когато показва списък с работни центрове за"
" маршрутизация."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__sequence
msgid "Gives the sequence order when displaying a list of work centers."
msgstr ""
"Дава реда на подледователност, когато показвасписъка с работните центрове."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__sequence
msgid "Gives the sequence order when displaying."
msgstr "Задава реда на последователност, когато показва"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__google_slide
msgid "Google Slide"
msgstr "Google Slide"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Google Slide Link"
msgstr "Google Slide Link"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/views/fields/google_slides_viewer.js:0
#, python-format
msgid "Google Slides Viewer"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Group By"
msgstr "Групиране по"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr "Групиране по..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Group by..."
msgstr "Групиране по ..."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_produced
msgid "Has Been Produced"
msgstr "Е произведено"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__has_kits
msgid "Has Kits"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_production__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_message
msgid "Has Message"
msgstr "Има съобщение"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__2
msgid "High"
msgstr "Високо"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "History"
msgstr "История"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__costs_hour
msgid "Hourly processing cost."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Hours"
msgstr "Часове"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_document__id
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__id
msgid "ID"
msgstr "ID"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon"
msgstr "Икона"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Икона за обозначаване на дейност с изключение."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_id
msgid ""
"If a product variant is defined the BOM is available only for this product."
msgstr ""
"Ако продуктовият вариант е определен, фактурата за покупка на материали е "
"налична само за този продукт."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ако е отметнато, новите съобщения ще изискват внимание."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ако е отметнато, някои съобщения имат грешка при доставката."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__propagate_cancel
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr ""
"Ако е отметнато при предишно преместване на движението (създадено от "
"следващата доставка), е отменено или разделено, движението, създадено от "
"това преместване, също ще се извърши."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Ако активното поле е настроено на 'Грешно', това ще позволи да скриете "
"ресурсния запис без да го отстранявате."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_height
msgid "Image Height"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_src
msgid "Image Src"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_width
msgid "Image Width"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Image is a link"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_immediate_production
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__immediate_production_id
msgid "Immediate Production"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_immediate_production_line
msgid "Immediate Production Line"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__immediate_production_line_ids
msgid "Immediate Production Lines"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Immediate Production?"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Immediate production?"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Impacted Transfer(s):"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "Import Template for Bills of Materials"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"Impossible to plan the workorder. Please check the workcenter "
"availabilities."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__progress
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__progress
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "In Progress"
msgstr "В процес"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__index_content
msgid "Indexed Content"
msgstr "Индексирано съдържание"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_deadline
msgid ""
"Informative date allowing to define when the manufacturing order should be "
"processed at the latest to fulfill delivery on time."
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_moves
msgid "Inventory Moves"
msgstr "Движение на инвентара"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr ""
"Движения в инвентара, голям брой от които трябва да сканирате в тази работна"
" поръчка"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_is_follower
msgid "Is Follower"
msgstr "е последовател"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__is_kits
#: model:ir.model.fields,field_description:mrp.field_product_template__is_kits
msgid "Is Kits"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_locked
msgid "Is Locked"
msgstr "Заключен"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__manual
msgid "Is a Blocking Reason"
msgstr "Е причина за блокиране"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "Is a Blocking Reason?"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__public
msgid "Is public document"
msgstr "е документ - обществено достояние"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_user_working
msgid "Is the Current User Working"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "It has already been unblocked."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"It is not possible to unplan one single Work Order. You should unplan the "
"Manufacturing Order instead in order to unplan all the linked operations."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_planned
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_planned
msgid "Its Operations are Planned"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__json_popover
msgid "JSON data for the popover widget"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__key
msgid "Key"
msgstr "Ключ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__description_bom_line
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__phantom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Kit"
msgstr "Комплект"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_document____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder____last_update
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild____last_update
msgid "Last Modified on"
msgstr "Последна промяна на"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_uid
msgid "Last Updated by"
msgstr "Последно актуализирано от"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_date
msgid "Last Updated on"
msgstr "Последно актуализирано на"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__last_working_user_id
msgid "Last user that worked on this work order."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__late
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Late"
msgstr "Последен"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Activities"
msgstr "Последни дейности"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late MO or Late delivery of components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__components_availability
msgid ""
"Latest component availability status for this MO. If green, then the MO's "
"readiness status is ready, as per BOM configuration."
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_ply_product_template
msgid "Layers that are stick together to assemble wood panels."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "Lead Time"
msgstr "Време на лийда"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#, python-format
msgid "Lead Times"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__leave_id
msgid "Leave"
msgstr "Напуснете"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning_line
msgid "Line of issue consumption"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__location_id
msgid "Location"
msgstr "Локация"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_id
msgid "Location where the product you want to unbuild is."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_src_id
msgid "Location where the system will look for components."
msgstr "Място, където системата ще търси компоненти."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "Локацията, където системата ще складира готовите продукти."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_dest_id
msgid ""
"Location where you want to send the components resulting from the unbuild "
"order."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lock"
msgstr "Заключете"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Lock the manufacturing order to prevent changes to what has been consumed or"
" produced."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_id
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Loss Reason"
msgstr "Причина за загуби"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_lot
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Lot/Serial"
msgstr "Партиден/сериен"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__lot_producing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__finished_lot_id
msgid "Lot/Serial Number"
msgstr "Партиден/сериен номер"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lot/Serial Numbers"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_traceability
msgid "Lots/Serial Numbers"
msgstr "Партидни/серийни номера"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__1
msgid "Low"
msgstr "Ниско"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_backorder_id
msgid "MO Backorder"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "MO Generated by %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reservation_state
msgid "MO Readiness"
msgstr ""

#. module: mrp
#: model:ir.actions.client,name:mrp.mrp_reception_action
msgid "MRP Reception Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss_type
msgid "MRP Workorder productivity losses"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_main_attachment_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_main_attachment_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основен прикачен Файл"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "Направете по поръчка"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Make sure enough quantities of these components are reserved to do the "
"production:\n"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr "Управлявайте операциите по работни поръчки"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__manual_consumption
#: model:ir.model.fields,field_description:mrp.field_stock_move__manual_consumption
msgid "Manual Consumption"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__manual_consumption_readonly
msgid "Manual Consumption Readonly"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid "Manual Duration"
msgstr "Механична продължителност"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "Manuf. Lead Time"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_steps
#: model:ir.model.fields.selection,name:mrp.selection__stock_rule__action__manufacture
#: model:stock.route,name:mrp.route_warehouse0_manufacture
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_stock_rule
#, python-format
msgid "Manufacture"
msgstr "Производство"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__mrp_one_step
#, python-format
msgid "Manufacture (1 step)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_mto_pull_id
msgid "Manufacture MTO Rule"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_pull_id
msgid "Manufacture Rule"
msgstr "Правила за производство"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "Manufacture Security Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__normal
msgid "Manufacture this product"
msgstr "Производител на продукта"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_to_resupply
msgid "Manufacture to Resupply"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__mrp_product_qty
#: model:ir.model.fields,field_description:mrp.field_product_template__mrp_product_qty
msgid "Manufactured"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "Manufactured Products"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Manufactured in the last 365 days"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__code__mrp_operation
#: model:ir.ui.menu,name:mrp.menu_mrp_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#, python-format
msgid "Manufacturing"
msgstr "Производство"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_buttons.xml:0
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_buttons.xml:0
#, python-format
msgid "Manufacturing Forecast"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#: model:ir.model.fields,field_description:mrp.field_product_product__produce_delay
#: model:ir.model.fields,field_description:mrp.field_product_template__produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__manufacturing_lead
#, python-format
msgid "Manufacturing Lead Time"
msgstr "Време на производствен лийд"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manu_type_id
msgid "Manufacturing Operation Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__production_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Manufacturing Order"
msgstr "Производствена поръчка "

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production_workcenter
#: model:ir.actions.act_window,name:mrp.action_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_picking_deshboard
#: model:ir.actions.act_window,name:mrp.mrp_production_report
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model:ir.ui.menu,name:mrp.menu_mrp_production_report
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr "Производствена поръчка "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are in confirmed state."
msgstr "Производствени поръчки в състояние 'потвърдени'."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__ready_to_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manufacturing Readiness"
msgstr "Производствена готовност"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr "Производствена референция"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Manufacturing Visibility Days"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                They can be defined via the configuration menu."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reservation_state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_availability
msgid ""
"Manufacturing readiness for this MO, as per bill of material configuration:\n"
"            * Ready: The material is available to start the production.\n"
"            * Waiting: The material is not available to start the production.\n"
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_mark_done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mark as Done"
msgstr "Маркиране като извършено"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mass Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr "Главен производствен график"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Material Availability"
msgstr "Наличност на материала"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_merge
msgid "Merge"
msgstr "Обединяване"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error
msgid "Message Delivery error"
msgstr "Грешка при доставката на съобщение"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_ids
msgid "Messages"
msgstr "Съобщения"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__mimetype
msgid "Mime Type"
msgstr "Вид Mime"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Правило за минимален инвентар"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#, python-format
msgid "Minutes"
msgstr "Минути"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Miscellaneous"
msgstr "Разнородни"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__mo_ids
msgid "Mo"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_byproduct_ids
msgid "Move Byproduct"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "Движения за проследяване"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_consumption_warning_line_ids
msgid "Mrp Consumption Warning Line"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_procurement_group__mrp_production_ids
msgid "Mrp Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_count
msgid "Mrp Production Count"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__multiple_lot_components_names
msgid "Multiple Lot Components Names"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Краен срок за моята дейност"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__name
msgid "Name"
msgstr "Име"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "New"
msgstr "Нов"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Next Activity"
msgstr "Следваща дейност"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Следващото събитие от календара на дейностите"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Краен срок на следващо действие"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_summary
msgid "Next Activity Summary"
msgstr "Обобщение на следваща дейност"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_id
msgid "Next Activity Type"
msgstr "Вид на следващо действие"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "No Backorder"
msgstr "Няма компенсираща поръчка"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "No bill of materials found. Let's create one!"
msgstr "Не е намерена спецификация на материалите. Нека да създадем една!"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "No data available."
msgstr "Няма налични данни."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_report
msgid "No data yet!"
msgstr "Все още няма данни!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "No manufacturing order found. Let's create one."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid "No product found. Let's create one!"
msgstr "Не е намерен продукт. Нека създадем един!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_blocked
msgid "No productivity loss for this equipment"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "No unbuild order found"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid "No work orders to do!"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid ""
"No workorder currently in progress. Click to mark work center as blocked."
msgstr ""
"Понастоящем няма работна поръчка в ход. Натиснете, за да маркирате работния "
"център като блокиран."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__normal
msgid "Normal"
msgstr "Нормален"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#, python-format
msgid "Not Available"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid ""
"Note that archived work center(s): '%s' is/are still linked to active Bill "
"of Materials, which means that operations can still be planned on it/them. "
"To prevent this, deletion of the work center is recommended instead."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Note that components"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/product.py:0 code:addons/mrp/models/product.py:0
#, python-format
msgid ""
"Note that product(s): '%s' is/are still linked to active Bill of Materials, "
"which means that the product can still be used on it/them."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction_counter
msgid "Number of Actions"
msgstr "Брой действия"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_late
msgid "Number of Manufacturing Orders Late"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_waiting
msgid "Number of Manufacturing Orders Waiting"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_todo
msgid "Number of Manufacturing Orders to Process"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_count
msgid "Number of Unbuilds"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error_counter
msgid "Number of errors"
msgstr "Брой грешки"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_child_count
msgid "Number of generated MO"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Брой съобщения, изискващи действие"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Брой съобщения с грешка при доставка"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__capacity
msgid "Number of pieces that can be produced in parallel for this product."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_source_count
msgid "Number of source MO"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "OEE"
msgstr "Цялостна ефективност на оборудването - OEE"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee_target
msgid "OEE Target"
msgstr "Цел на OEE "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee
msgid "Oee"
msgstr "Цялостна ефективност на оборудването - OEE"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "On Hand"
msgstr "В наличност"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Only manufacturing orders in either a draft or confirmed state can be %s."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Only manufacturing orders with a Bill of Materials can be %s."
msgstr ""
"Само производствени поръчки със спецификация на материалите могат да бъдат "
"%s."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Open Work Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Operation"
msgstr "Операция"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid "Operation Dependencies"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__operation_id
msgid "Operation To Consume"
msgstr "Операция за изразходване"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__picking_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_type_id
msgid "Operation Type"
msgstr "Тип операция"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Operation define that need to be done to realize a Work Order.\n"
"                Each operation is done at a specific Work Center and has a specific duration."
msgstr ""

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__allowed_operation_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_calendar
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
#, python-format
msgid "Operations"
msgstr "Операции"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Done"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Planned"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Operations Search Filters"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
msgid "Operations that cannot start before this operation is completed."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
msgid "Operations that need to be completed before this operation can start."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__orderpoint_id
msgid "Orderpoint"
msgstr "точка за заявка"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__order_ids
msgid "Orders"
msgstr "Поръчки"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_production
msgid "Original Production Quantity"
msgstr "Първоначално производствено количество"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee_target
msgid "Overall Effective Efficiency Target in percentage"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_oee
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_report
msgid "Overall Equipment Effectiveness"
msgstr "Цялостна ефективност на оборудването - OEE"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee
msgid "Overall Equipment Effectiveness, based on the last month"
msgstr "Цялостна ефективност на оборудването въз основа на последния месец"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_oee
msgid "Overall Equipment Effectiveness: no working or blocked time"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Overview"
msgstr "Общ преглед"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_id
msgid "Parent BoM"
msgstr "Основна фактура за покупка на материали"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__parent_product_tmpl_id
msgid "Parent Product Template"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_consumption_warning_id
msgid "Parent Wizard"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr ""
"Поставете URL адреса в Google Slide. Уверете се, че достъпът до документа е "
"публичен."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Pause"
msgstr "Пауза"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Pending"
msgstr "Чакащ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__performance
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__performance
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Performance"
msgstr "Производителност"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Performance Losses"
msgstr "Загуба на производителност"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__performance
msgid "Performance over the last month"
msgstr "Производителност през последния месец"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pick Components"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pick components and then manufacture"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm
msgid "Pick components and then manufacture (2 steps)"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm_sam
#, python-format
msgid "Pick components, manufacture and then store products (3 steps)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_mto_pull_id
msgid "Picking Before Manufacturing MTO Rule"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_type_id
msgid "Picking Before Manufacturing Operation Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_route_id
msgid "Picking Before Manufacturing Route"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking_type
msgid "Picking Type"
msgstr "Вид пикинг"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_ids
msgid "Picking associated to this manufacturing order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_loc_id
msgid "Picking before Manufacturing Location"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Plan"
msgstr "Планирайте"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Plan Orders"
msgstr "Планирайте поръчки"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Plan manufacturing or purchase orders based on forecasts"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planned"
msgstr "Планиран"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Planned Date"
msgstr "Планирана дата"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Planned at the same time as other workorder(s) at %s"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.mrp_planning_menu_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Planning"
msgstr "Планиране"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planning Issues"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_plastic_laminate_product_template
msgid "Plastic Laminate"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_ply_product_template
msgid "Ply Layer"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_ply_veneer_product_template
msgid "Ply Veneer"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__json_popover
msgid "Popover Data JSON"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__possible_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__possible_bom_product_template_attribute_value_ids
msgid "Possible Product Template Attribute Value"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Post-Production"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pre-Production"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#, python-format
msgid "Print"
msgstr "Печат"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#, python-format
msgid "Print All Variants"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__priority
#: model:ir.model.fields,field_description:mrp.field_mrp_production__priority
msgid "Priority"
msgstr "Приоритет"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process operations at specific work centers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__produce_line_ids
msgid "Processed Disassembly Lines"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_group
#: model:ir.model.fields,field_description:mrp.field_mrp_production__procurement_group_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Procurement Group"
msgstr "Група за доставки"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_steps
msgid ""
"Produce : Move the components to the production location        directly and start the manufacturing process.\n"
"Pick / Produce : Unload        the components from the Stock to Input location first, and then        transfer it to the Production location."
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_byproducts
msgid "Produce residual products"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Produce residual products (A + B -&gt; C + D)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "Produced"
msgstr "Произведен"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__produced_qty
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Produced Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__serial_numbers
msgid "Produced Serial Numbers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__operation_id
msgid "Produced in Operation"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model,name:mrp.model_product_template
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#, python-format
msgid "Product"
msgstr "Продукт"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Product Attachments"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__capacity_ids
msgid "Product Capacities"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "Product Cost"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_virtual_available
msgid "Product Forecasted Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr "Управление на жизнения цикъл на продукта (PLM)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Product Moves"
msgstr "Движения по продукт"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_qty_available
msgid "Product On Hand Quantity"
msgstr "Продукт Налично количество"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Product Quantity"
msgstr "Продуктово количество"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tmpl_id
msgid "Product Template"
msgstr "Шаблон за продукт "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_uom_id
msgid "Product Unit of Measure"
msgstr "Продуктова мерна единица"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Product UoM"
msgstr "Мерна единица на продукт"

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_id
msgid "Product Variant"
msgstr "Продуктов вариант"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_product_variant_action
#: model:ir.ui.menu,name:mrp.product_variant_mrp
msgid "Product Variants"
msgstr "Продуктови варианти"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_unique_product
msgid "Product capacity should be unique for each workcenter."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__production_id
msgid "Production"
msgstr "Продукция"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_capacity
msgid "Production Capacity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_date
msgid "Production Date"
msgstr "Дата на производство"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_document
msgid "Production Document"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Production Information"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_location_id
msgid "Production Location"
msgstr "Локация на продукция"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_production_order
#: model:ir.model,name:mrp.model_mrp_production
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__production_id
msgid "Production Order"
msgstr "Производствена поръчка"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__raw_material_production_id
msgid "Production Order for components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__production_id
msgid "Production Order for finished products"
msgstr "Производствена поръчка за готови продукти"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_state
msgid "Production State"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Production Workcenter"
msgstr "Прозводствен работен център"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
#, python-format
msgid "Production of Draft MO"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Production started late"
msgstr "Производството започна късно"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__production_ids
msgid "Productions To Split"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__productive
msgid "Productive"
msgstr "Продуктивен"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__productive_time
msgid "Productive Time"
msgstr "Продуктивно време"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__productive_time
msgid "Productive hours over the last month"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Productivity"
msgstr "Продуктивност"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_blocked
msgid "Productivity Losses"
msgstr "Производствени загуби"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
msgid "Products"
msgstr "Продукти"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__progress
msgid "Progress Done (%)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__propagate_cancel
msgid "Propagate cancel and split"
msgstr "Разпрострете отмяна и разделяне"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__quality
msgid "Quality"
msgstr "Качество"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Quality Losses"
msgstr "Качествени загуби"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quant_ids
msgid "Quant"
msgstr "Кол-во"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_produced
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quantity
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "Quantity"
msgstr "Количество"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Quantity Produced"
msgstr "Произведено количество"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_producing
msgid "Quantity Producing"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_remaining
msgid "Quantity To Be Produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__should_consume_qty
msgid "Quantity To Consume"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__quantity
msgid "Quantity To Produce"
msgstr "Количество за производство"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,help:mrp.field_mrp_production_split__production_capacity
msgid "Quantity that can be produced with the current stock of components"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#, python-format
msgid "Quantity:"
msgstr "Количество:"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_quant
msgid "Quants"
msgstr "Количества"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_raw_ids
msgid "Raw Moves"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__assigned
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__ready
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Ready"
msgstr "Готово"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "Ready to Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_real_duration
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration
msgid "Real Duration"
msgstr "Действително времетраене"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#, python-format
msgid "Reception time estimation."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"Recursion error!  A product with a Bill of Material should not have itself "
"in its BoM or child BoMs!"
msgstr ""
"Рекурсивна грешка!  Продукт с фактура за покупка на материали не би трябвало"
" да съдържа в себе си фактура за покупка на материали или второстепенни "
"фактури за покупка на материали!"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__code
#: model:ir.model.fields,field_description:mrp.field_mrp_production__name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__name
msgid "Reference"
msgstr "Индетификатор"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_name_uniq
msgid "Reference must be unique per Company!"
msgstr "Референцията трябва да е уникална за всяка компания!"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__origin
msgid ""
"Reference of the document that generated this production order request."
msgstr ""
"Референция на документа, който създава тази заявка за производствена "
"поръчка."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "Reference:"
msgstr "Референция:"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__ir_attachment_id
msgid "Related attachment"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Remaining Quantity"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
#, python-format
msgid "Replan"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_reporting
msgid "Reporting"
msgstr "Отчитане"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
#, python-format
msgid "Reserve"
msgstr "Резервирайте"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Reserved"
msgstr "Резервиран"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_id
msgid "Resource"
msgstr "Ресурс"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_field
msgid "Resource Field"
msgstr "Ресурсно поле"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_id
msgid "Resource ID"
msgstr "ИН на ресурс"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_model
msgid "Resource Model"
msgstr "Модел на ресурс"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_name
msgid "Resource Name"
msgstr "Име на ресурс"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__user_id
msgid "Responsible"
msgstr "Отговорно лице"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_user_id
msgid "Responsible User"
msgstr "Отговорник"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#, python-format
msgid "Resupply lead time."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_quant.py:0
#, python-format
msgid "Return to Inventory"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "Route"
msgstr "Маршрут"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__routing_line_ids
msgid "Routing Lines"
msgstr "Редове на маршрутизация"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr "Работни центрове за маршрутизация"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_procurement_compute_mrp
msgid "Run Scheduler"
msgstr "Пуснете планьора"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS грешка при доставка"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__date
msgid "Schedule Date"
msgstr "Назначете дата"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule manufacturing orders earlier to avoid delays"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_planned_start
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Scheduled Date"
msgstr "Насрочена дата"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Scheduled Date by Month"
msgstr "Насрочена дата по месец"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Scheduled Date: Last 365 Days"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_planned_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_planned_finished
msgid "Scheduled End Date"
msgstr "Насрочена крайна дата"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_planned_start
msgid "Scheduled Start Date"
msgstr "Насрочена начална дата"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"Scheduled before the previous work order, planned from %(start)s to %(end)s"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
#, python-format
msgid "Scheduling Information"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
#: model:ir.model,name:mrp.model_stock_scrap
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_scrap
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#, python-format
msgid "Scrap"
msgstr "Скрап"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_count
msgid "Scrap Move"
msgstr "Движение на скрап"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_ids
msgid "Scraps"
msgstr "Скрап"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_screw_product_template
msgid "Screw"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Search"
msgstr "Потърсете"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr "Потърсете фактура за покупка на материали"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr "Потърсете производство"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Search Work Orders"
msgstr "Потърсете на работни поръчки"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr "Потърсете работен център за Планиране на производствения ресурс - MRP"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Security Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,help:mrp.field_res_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr "Дни за безопасност за всяка производствена операция."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
#, python-format
msgid "Select Operations to Copy"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__sequence
msgid "Sequence"
msgstr "Последователност"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence picking before manufacturing"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence production"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence stock after manufacturing"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Serial Mass Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Set Quantities & Validate"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__manual
msgid "Set duration manually"
msgstr "Настройте времетраенето механично"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Set the order that work orders should be processed in. Activate the feature "
"within each BoM's Miscellaneous tab"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model:ir.ui.menu,name:mrp.menu_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Settings"
msgstr "Настройки"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_start
msgid "Setup Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_start
msgid "Setup Time (minutes)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_allocation
msgid "Show Allocation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__show_apply
msgid "Show Apply"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__show_backorders
msgid "Show Backorders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__show_bom
msgid "Show BoM column"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Show Details"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_final_lots
msgid "Show Final Lots"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lock
msgid "Show Lock/unlock buttons"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__show_json_popover
msgid "Show Popover?"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__show_productions
msgid "Show Productions"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Показване на всички записи, на които следващата дата на действие е преди "
"днес"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__show_backorder_lines
msgid "Show backorder lines"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/views/fields/google_slides_viewer.xml:0
#, python-format
msgid "Slides viewer"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__leave_id
msgid "Slot into workcenter calendar once planned"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_head_product_template
msgid "Solid wood is a durable natural material."
msgstr "Масивното дърво е издръжлив естествен материал."

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_product_template
msgid "Solid wood table."
msgstr "Маса от масивна дървесина"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"Some of your byproducts are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct byproducts."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"Some of your components are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct components."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Some product moves have already been confirmed, this manufacturing order "
"can't be completely cancelled. Are you still sure you want to process ?"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Some work orders are already done, you cannot unplan this manufacturing "
"order."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Some work orders have already started, you cannot unplan this manufacturing "
"order."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__origin
msgid "Source"
msgstr "Източник"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_id
msgid "Source Location"
msgstr "Локация на източник"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Specific Capacities"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__capacity_ids
msgid ""
"Specific number of pieces that can be produced in parallel per product."
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_split
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
msgid "Split"
msgstr "Разделете"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__counter
msgid "Split #"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_detailed_vals_ids
msgid "Split Details"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__mrp_production_split_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Production"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_line
msgid "Split Production Detail"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_split_multi_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Productions"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split
msgid "Split production"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split_multi
msgid "Split productions"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_screw_product_template
msgid "Stainless steel screw"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_bolt_product_template
msgid "Stainless steel screw full (dia - 5mm, Length - 10mm)"
msgstr "Винт от неръждаема стомана (диаметър - 5мм, дължина - 10мм)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Starred"
msgstr "Отбелязано със звездичка"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Start"
msgstr "Стартирайте"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_start
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Start Date"
msgstr "Начална Дата"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__state
msgid "State"
msgstr "Област"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__state
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Status"
msgstr "Състояние"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус според дейностите\n"
"Пресрочени: Крайната дата е в миналото\n"
"Днес: Дейности с дата на изпълнение днес \n"
"Планирани: Бъдещи дейности."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_type_id
msgid "Stock After Manufacturing Operation Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_rule_id
msgid "Stock After Manufacturing Rule"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_assign_serial
msgid "Stock Assign Serial Numbers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_availability
msgid "Stock Availability"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr "Движение в склада"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_dest_ids
msgid "Stock Movements of Produced Goods"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_unbuild_moves
msgid "Stock Moves"
msgstr "Складови движения"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_rule
msgid "Stock Rule"
msgstr "Правило зс наличност"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_loc_id
msgid "Stock after Manufacturing Location"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Отчет Правила за наличност"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Store Finished Product"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__store_fname
msgid "Stored Filename"
msgstr "Stored Filename"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_bom_id
msgid "Sub BoM"
msgstr "Субфактура за покупка на материали"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Subcontract the production of some products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_subcontracting
msgid "Subcontracting"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_product_template
msgid "Table"
msgstr "Таблица"

#. module: mrp
#: model:product.template,name:mrp.product_product_table_kit_product_template
msgid "Table Kit"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_leg_product_template
msgid "Table Leg"
msgstr "Долна част на таблица"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_head_product_template
msgid "Table Top"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_table_kit_product_template
msgid "Table kit"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tag_ids
msgid "Tag"
msgstr "Маркер"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__name
msgid "Tag Name"
msgstr "Име на маркер"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr "Техническо поле за проверка кога можем да отрезервираме"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__text
msgid "Text"
msgstr "Текст"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0 code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The Product Unit of Measure you chose has a different category than in the "
"product form."
msgstr ""
"Избраната от Вас мерна единица има различна категория от тази в продуктовата"
" форма."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "The Workorder (%s) cannot be started twice!"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The attribute value %(attribute)s set on product %(product)s does not match "
"the BoM product %(bom_product)s."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "The capacity must be strictly positive."
msgstr "Капацитетът трябва да е строго положителен."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "The component %s should not be the same as the product to produce."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The current configuration is incorrect because it would create a cycle "
"between these products: %s."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_orderpoint.py:0
#, python-format
msgid "The following replenishment order has been generated"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_produced
msgid "The number of products already handled by this work order"
msgstr "Броят продукти, които вече се обработват по тази работна поръчка "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__operation_id
msgid ""
"The operation where the components are consumed, or the finished products "
"created."
msgstr ""
"Операцията, при която се изразходват компонентите, или се създават готовите "
"продукти."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__cost_share
msgid ""
"The percentage of the final production cost for this by-product line "
"(divided between the quantity produced).The total of all by-products' cost "
"share must be less than or equal to 100."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__cost_share
msgid ""
"The percentage of the final production cost for this by-product. The total "
"of all by-products' cost share must be smaller or equal to 100."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"The planned end date of the work order cannot be prior to the planned start "
"date, please correct this to save the work order."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The product has already been used at least once, editing its structure may "
"lead to undesirable behaviours. You should rather archive the product and "
"create a new one with a new bill of materials."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid ""
"The quantity already produced awaiting allocation in the backorders chain."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_qty_positive
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_qty_positive
#, python-format
msgid "The quantity to produce must be positive!"
msgstr "Произведеното количество трябва да е положително!"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The serial number %(number)s used for byproduct %(product_name)s has already"
" been produced"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The serial number %(number)s used for component %(component)s has already "
"been consumed"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_tag_tag_name_unique
msgid "The tag name must be unique."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "The total cost share for a BoM's by-products cannot exceed 100."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The total cost share for a manufacturing order's by-products cannot exceed "
"100."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "The work order should have already been processed."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__theme_template_id
msgid "Theme Template"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "There are more Serial Numbers than the Quantity to Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"There are no components to consume. Are you still sure you want to continue?"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid "There's no product move yet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Това поле се използва, за да се определи в коя часова зона ще работят "
"ресурсите."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#, python-format
msgid "This is a BoM of type Kit!"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#, python-format
msgid ""
"This is the cost based on the BoM of the product. It is computed by summing "
"the costs of the components and operations needed to build the product."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#, python-format
msgid "This is the cost defined on the product."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                You can filter on the product to see all the past movements for the product."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "This production has been merge in %s"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "This production order has been created from Replenishment Report."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "This serial number for product %s has already been produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_qty
msgid ""
"This should be the smallest quantity that this product can be produced in. "
"If the BOM contains operations, make sure the work center capacity is "
"accurate."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__time_ids
msgid "Time"
msgstr "Време"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_efficiency
msgid "Time Efficiency"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_ids
msgid "Time Logs"
msgstr "Времеви регистри"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Time Tracking"
msgstr "Времево проследяване"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Time Tracking: %(user)s"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid ""
"Time in minutes:- In manual mode, time used- In automatic mode, supposed "
"first time when there aren't any work orders yet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tz
msgid "Timezone"
msgstr "Часови пояс"

#. module: mrp
#: model:digest.tip,name:mrp.digest_tip_mrp_0
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid "Tip: Use tablets in the shop to control manufacturing"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "To"
msgstr "До"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__to_backorder
msgid "To Backorder"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__to_close
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Close"
msgstr "За затваряне"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_expected_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "To Consume"
msgstr "За изразходване"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Do"
msgstr "За извършване"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "To Launch"
msgstr "За лансиране"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__to_immediate
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "To Process"
msgstr "За обработка"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "To Produce"
msgstr "За производство"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today Activities"
msgstr "Днешни дейности"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_wear_product_template
msgid "Top layer of a wood panel."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Total Duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_late_count
msgid "Total Late Orders"
msgstr "Общо последни поръчки"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_pending_count
msgid "Total Pending Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr "Общо количество"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_qty
msgid "Total Quantity"
msgstr "Общо количество"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_progress_count
msgid "Total Running Orders"
msgstr "Общо изпълняващи се поръчки"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Total To Consume"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Total duration"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total expected duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_duration_expected
msgid "Total expected duration (in minutes)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total real duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_real_duration
msgid "Total real duration (in minutes)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Traceability"
msgstr "Проследимост"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_traceability_report
msgid "Traceability Report"
msgstr "Отчет на проследяемост"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_tracking
msgid "Tracking"
msgstr "Проследяване"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking
msgid "Transfer"
msgstr "Прехвърлете"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Transfers"
msgstr "Трансфери"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__type
msgid "Type"
msgstr "Вид"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Вид операция"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Вид на изключение на дейност в базата."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Unable to split with more than the quantity to produce."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Unblock"
msgstr "Отблокирайте"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__unbuild_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild"
msgstr "Унищожете"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild Order"
msgstr "Поръчка за унищожение"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "Unbuild Order product quantity has to be strictly positive."
msgstr ""
"Продуктовото количество в поръчката за унищожаване да е строго положително."

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_unbuild
#: model:ir.ui.menu,name:mrp.menu_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Orders"
msgstr "Поръчки за унищожаване"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Unbuild: %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_ids
msgid "Unbuilds"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Unbuilt"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#, python-format
msgid "Unfold"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#, python-format
msgid "Unit Cost"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unit_factor
msgid "Unit Factor"
msgstr "Фактор на единица"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_uom_name
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Unit of Measure"
msgstr "Мерна единица"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_id
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for the "
"inventory control"
msgstr ""
"Мерната единица (мерна единица) е единицата за измерване контрол на "
"инвентара"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unlock"
msgstr "Отключете"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_unlocked_by_default
msgid "Unlock Manufacturing Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Unlock the manufacturing order to adjust what has been consumed or produced."
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_unlocked_by_default
msgid "Unlocked by default"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unplan"
msgstr "Откажете планиране"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
#: model:ir.actions.server,name:mrp.mrp_production_action_unreserve_tree
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#, python-format
msgid "Unreserve"
msgstr "Отрезервирайте"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "UoM"
msgstr "Мерна единица"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/views/mrp_documents_kanban/mrp_documents_kanban_controller.xml:0
#: code:addons/mrp/static/src/xml/mrp_document_template.xml:0
#, python-format
msgid "Upload"
msgstr "Качване"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Upload your PDF file."
msgstr "Качи твоя PDF файл."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__1
msgid "Urgent"
msgstr "Спешно"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__url
msgid "Url"
msgstr "URL адрес"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_workorder_dependencies
msgid "Use Operation Dependencies"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_reception_report
msgid "Use Reception Report with Manufacturing Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Used In"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__user_id
#: model:res.groups,name:mrp.group_mrp_user
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "User"
msgstr "Потребител"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Using a MPS report to schedule your reordering and manufacturing operations "
"is useful if you have long lead time and if you produce based on sales "
"forecasts."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__valid_details
msgid "Valid"
msgstr "Валидно"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Validate"
msgstr "Валидирай"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_consumption_warning.py:0
#, python-format
msgid ""
"Values cannot be set and validated because a Lot/Serial Number needs to be specified for a tracked product that is having its consumed amount increased:\n"
"- "
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#, python-format
msgid "Variant:"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__3
msgid "Very High"
msgstr "Много високо"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "View WorkOrder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "View and allocate manufactured quantities"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Visibility Days applied on the manufacturing routes."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Waiting"
msgstr "Изчакващ"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__waiting
msgid "Waiting Another Operation"
msgstr "Изчакващ друга операция"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Waiting Availability"
msgstr "Изчакващ наличност"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__pending
msgid "Waiting for another WO"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__waiting
msgid "Waiting for components"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Waiting the previous work order, planned from %(start)s to %(end)s"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse
#: model:ir.model.fields,field_description:mrp.field_mrp_production__warehouse_id
msgid "Warehouse"
msgstr "Склад"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#, python-format
msgid "Warehouse:"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warn_insufficient_qty_unbuild
msgid "Warn Insufficient Unbuild Quantity"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0 code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/stock_scrap.py:0
#, python-format
msgid "Warning"
msgstr "Внимание"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Warnings"
msgstr "Предупреждения"

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_wear_product_template
msgid "Wear Layer"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__website_id
msgid "Website"
msgstr "Уебсайт"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__website_message_ids
msgid "Website Messages"
msgstr "Съобщения в уебсайт"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__website_message_ids
msgid "Website communication history"
msgstr "История на комуникацията на уебсайт"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__picking_type_id
msgid ""
"When a procurement has a ‘produce’ route with a operation type set, it will "
"try to create a Manufacturing Order for that product using a BoM of the same"
" operation type. That allows to define stock rules which trigger different "
"manufacturing orders with different BoMs."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__manual_consumption
#: model:ir.model.fields,help:mrp.field_stock_move__manual_consumption
msgid ""
"When activated, then the registration of consumption for that component is recorded manually exclusively.\n"
"If not activated, and any of the components consumption is edited manually on the manufacturing order, Odoo assumes manual consumption also."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__asap
msgid "When components for 1st operation are available"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_to_resupply
msgid ""
"When products are manufactured, they can be manufactured in this warehouse."
msgstr ""
"Когато продуктите се произвеждат, те могат да бъдат произведени в този "
"склад."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> a manufacturing order is "
"created to fulfill the need."
msgstr ""

#. module: mrp
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid ""
"With the Odoo work center control panel, your worker can start work orders "
"in the shop and follow instructions of the worksheet. Quality tests are "
"perfectly integrated into the process. Workers can trigger feedback loops, "
"maintenance alerts, scrap products, etc."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_multi
msgid "Wizard to Split Multiple Productions"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split
msgid "Wizard to Split a Production"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_panel_product_template
msgid "Wood Panel"
msgstr "Дървена плоскост"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Work Center"
msgstr "Работен център"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_capacity
msgid "Work Center Capacity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workcenter_load
msgid "Work Center Load"
msgstr "Натоварване на работен център"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Work Center Loads"
msgstr "Натоварване на работен център"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center Name"
msgstr "Име на работен център"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Използване на работен център"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
msgid "Work Center load"
msgstr "Натоварване на работен център"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Centers"
msgstr "Работни центрове"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_kanban_action
msgid "Work Centers Overview"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Work Instruction"
msgstr "Работна институция"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_workorder
#: model:ir.model,name:mrp.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workorder_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__name
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__workorder_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__workorder_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Work Order"
msgstr "Работна поръчка"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_workorder_dependencies
msgid "Work Order Dependencies"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Work Order Operations allow you to create and manage the manufacturing "
"operations that should be followed within your work centers in order to "
"produce a product. They are attached to bills of materials that will define "
"the required components."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__workorder_id
msgid "Work Order To Consume"
msgstr "Работна поръчка за изразходване"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_routing_time
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production_specific
#: model:ir.actions.act_window,name:mrp.action_work_orders
#: model:ir.actions.act_window,name:mrp.mrp_workorder_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_workorder_report
#: model:ir.actions.act_window,name:mrp.mrp_workorder_todo
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_workorder
#: model:ir.ui.menu,name:mrp.menu_mrp_work_order_report
#: model:ir.ui.menu,name:mrp.menu_mrp_workorder_todo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Orders"
msgstr "Работни поръчки"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_workcenter_report
msgid "Work Orders Performance"
msgstr "Производителност на работни поръчки"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_workcenter
msgid "Work Orders Planning"
msgstr "Планиране на работни поръчки"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Work Sheet"
msgstr "Работен график"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Work center"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"                    Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"Работните поръчки са операции за извършване като част от производствена поръчка. \n"
"            Операциите са дефинирани в спецификацията на материалите или са добавени директно към производствената поръчка."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Work orders in progress. Click to block work center."
msgstr "Работни поръчки в ход. Натиснете, за да блокирате работен център."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Workcenter"
msgstr "Работен център"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "Workcenter %s cannot be an alternative of itself."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Workcenter Productivity"
msgstr "Производителност на работен център"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Регистър за производителност на работен център"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Workcenter Productivity Loss"
msgstr "Загуба на производителност на работен център"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss
msgid "Workcenter Productivity Losses"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__working_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_state
msgid "Workcenter Status"
msgstr "Състояние Работен център"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Workcenter blocked, click to unblock."
msgstr "Блокиран работен център, натиснете, за да отблокирате."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Working Hours"
msgstr "Работни часове"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_user_ids
msgid "Working user on this work order."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_type
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet
msgid "Worksheet"
msgstr "Работен график"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_type
msgid "Worksheet Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_google_slide
msgid "Worksheet URL"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_lot.py:0
#, python-format
msgid ""
"You are not allowed to create or edit a lot or serial number for the "
"components with the operation type \"Manufacturing\". To change this, go on "
"the operation type and tick the box \"Create New Lots/Serial Numbers for "
"Components\"."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Можете или да качите файл от компютъра си, или да копирате/поставите "
"интернет линк във файла си."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not create a kit-type bill of materials for products that have at "
"least one reordering rule."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr ""
"Не можете да изтривате фактура за покупка на материали с производствена поръчка, която се изпълнява.\n"
"Моля, първо я затворете или отменете."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"You can only merge manufacturing orders of identical products with same BoM."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"You can only merge manufacturing orders with no additional components or by-"
"products."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You can only merge manufacturing with the same operation type"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You can only merge manufacturing with the same state."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You cannot change the workcenter of a work order that is in progress or "
"done."
msgstr ""
"Не можете да променяте работния център за работна порчка, ако тя е в процес "
"на изпълнение или вече изпълнена."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "You cannot create a new Bill of Material from here."
msgstr "Не можете да създавате нов списък с материали оттук."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You cannot create cyclic dependency."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You cannot delete an unbuild order if the state is 'Done'."
msgstr ""
"Не можете да поръчка за разглобяване, ако състоянието й е 'изпълнено'."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You cannot have %s  as the finished product and in the Byproducts"
msgstr "Не можете да получите %s  хем като краен, хем като вторичен продукт"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You cannot link this work order to another manufacturing order."
msgstr ""
"Не можете да свържете тази работна поръчка с друго нареждане за "
"производство."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You cannot move a manufacturing order once it is cancelled or done."
msgstr ""
"Не можете да местите нареждане за производство, което е отменено или "
"изпълнено."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You cannot produce the same serial number twice."
msgstr "Не можжете да произведете същия сериен номер два пъти."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You cannot unbuild a undone manufacturing order."
msgstr "Не можете да разглобите нареждане за производство, което е отменено."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You cannot use the 'Apply on Variant' functionality and simultaneously "
"create a BoM for a specific variant."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid ""
"You consumed a different quantity than expected for the following products.\n"
"                        <b attrs=\"{'invisible': [('consumption', '=', 'strict')]}\">\n"
"                            Please confirm it has been done on purpose.\n"
"                        </b>\n"
"                        <b attrs=\"{'invisible': [('consumption', '!=', 'strict')]}\">\n"
"                            Please review your component consumption or ask a manager to validate \n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '!=', 1)]}\">this manufacturing order</span>\n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '=', 1)]}\">these manufacturing orders</span>.\n"
"                        </b>"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/change_production_qty.py:0
#, python-format
msgid ""
"You have already processed %(quantity)s. Please input a quantity higher than"
" %(minimum)s "
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid ""
"You have entered less serial numbers than the quantity to produce.<br/>\n"
"                        Create a backorder if you expect to process the remaining quantities later.<br/>\n"
"                        Do not create a backorder if you will not process the remaining products."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid ""
"You have not recorded <i>produced</i> quantities yet, by clicking on "
"<i>apply</i> Odoo will produce all the finished products and consume all "
"components."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"You must indicate a non-zero amount consumed for at least one of your "
"components"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You need at least two production orders to merge them."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"Трябва да определите поне една загуба на производителност в категорията "
"'Производителност'. Създайте такава от приложението 'Производство', менюто: "
"Конфигурация/загуби на производителност."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one productivity loss in the category "
"'Productivity'. Create one from the Manufacturing app, menu: Configuration /"
" Productivity Losses."
msgstr ""
"Трябва да определите поне една загуба на производителност в категорията "
"'Продуктивност'. Създайте такава от приложението 'Производство', менюто: "
"Конфигурация/загуби на производителност."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid ""
"You need to define at least one unactive productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"Трябва да определите поне една неактивна загуба на производителност в "
"категорията 'Производителност'. Създайте такава от приложението "
"'Производство', менюто: Конфигурация/загуби на производителност.."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You need to provide a lot for the finished product."
msgstr "Трябва да посочите партида за крайния продукт."

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_immediate_production.py:0
#, python-format
msgid "You need to supply Lot/Serial Number for products:"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_backorder
msgid "You produced less than initial demand"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You should provide a lot number for the final product."
msgstr "Трябва да предоставите партиден номер за крайния продукт"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_quant.py:0
#, python-format
msgid ""
"You should update the components quantity instead of directly updating the "
"quantity of the kit product."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "and build finished products using"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "bills of materials"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "cancelled"
msgstr "отказано"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "components"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "copy paste a list and/or use Generate"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "days"
msgstr "дни"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "expected duration"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "from location"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid ""
"have multiple lot reservations.<br/>\n"
"                        Do you want to confirm anyway ?"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "last"
msgstr "последен"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "manufacturing order"
msgstr "нареждане за производство"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "merged"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#, python-format
msgid "minutes"
msgstr "минути"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "of"
msgstr "от"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "on"
msgstr "на"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "ordered instead of"
msgstr "поръчани вместо"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "per workcenter"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "quantity has been updated."
msgstr "количеството е обновено"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "real duration"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "split"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "work orders"
msgstr "работни поръчки"
