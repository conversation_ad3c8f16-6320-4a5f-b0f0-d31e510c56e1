# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_address_extended
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.2\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-11 14:34+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "<span> - </span>"
msgstr ""
"```html\n"
"<span> - </span>\n"
"```"

#. module: base_address_extended
#: model:ir.model.fields,help:base_address_extended.field_res_country__enforce_cities
#: model:ir.model.fields,help:base_address_extended.field_res_partner__country_enforce_cities
#: model:ir.model.fields,help:base_address_extended.field_res_users__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""
"کادر را علامت بزنید تا اطمینان حاصل کنید که هر آدرسی که در آن کشور ایجاد "
"می‌شود، دارای یک 'شهر' انتخاب‌شده در لیست شهرهای آن کشور است."

#. module: base_address_extended
#: model:ir.actions.act_window,name:base_address_extended.action_res_city_tree
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_country_city_extended_form
msgid "Cities"
msgstr "شهرها"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_city
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_city_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_filter
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_tree
msgid "City"
msgstr "شهر"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__city_id
msgid "City ID"
msgstr "شهر شناسه"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_partner
msgid "Contact"
msgstr "مخاطب"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_country
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__country_id
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Country"
msgstr "کشور"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: base_address_extended
#: model_terms:ir.actions.act_window,help:base_address_extended.action_res_city_tree
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"                your partner records. Note that an option can be set on each country separately\n"
"                to enforce any address of it to have a city in this list."
msgstr ""
"نمایش و مدیریت فهرست تمام شهرهایی که می‌توان به\n"
"                رکوردهای شریک شما اختصاص داد. توجه داشته باشید که گزینه‌ای می‌تواند به صورت جداگانه برای هر کشور تنظیم شود\n"
"                تا هر آدرسی در آن الزاماً شامل یک شهر از این فهرست باشد."

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number2
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number2
msgid "Door"
msgstr "درب"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Door #"
msgstr "در #"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_country__enforce_cities
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__country_enforce_cities
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__country_enforce_cities
msgid "Enforce Cities"
msgstr "اعمال شهرها"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number
msgid "House"
msgstr "منزل"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "House #"
msgstr "شماره خانه#"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__id
msgid "ID"
msgstr "شناسه"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city____last_update
msgid "Last Modified on"
msgstr "آخرین اصلاح در"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__write_uid
msgid "Last Updated by"
msgstr "آخرین تغییر توسط"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__name
msgid "Name"
msgstr "نام"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_filter
msgid "Search City"
msgstr "جستجوی شهر"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__state_id
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "State"
msgstr "استان"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street"
msgstr "آدرس"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street 2..."
msgstr "آدرس 2 ..."

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_name
msgid "Street Name"
msgstr "نام خیابان"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street..."
msgstr "آدرس ..."

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "ZIP"
msgstr "کد پستی"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__zipcode
msgid "Zip"
msgstr "کدپستی"
