<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="iva23" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA23</field>
            <field name="description">IVA23 (taxa normal Portugal Continental)</field>
            <field name="amount">23</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_iva_23"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_vendas_faturas_normal_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_vendas_faturas_normal_tag')],
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_vendas_notas_credito_normal_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_vendas_notas_credito_normal_tag')],
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
        </record>
        <record id="iva22" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA22</field>
            <field name="description">IVA22 (taxa normal Madeira)</field>
            <field name="amount">22</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="tax_group_id" ref="tax_group_iva_22"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
        </record>
        <record id="iva16" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA16</field>
            <field name="description">IVA16 (taxa normal Açores)</field>
            <field name="amount">16</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="tax_group_id" ref="tax_group_iva_16"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
        </record>
        <record id="iva13" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA13</field>
            <field name="description">IVA13 (taxa intermédia Portugal Continental)</field>
            <field name="amount">13</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_iva_13"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_vendas_faturas_intermedia_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_vendas_faturas_intermedia_tag')],
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_vendas_notas_credito_intermedia_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_vendas_notas_credito_intermedia_tag')],
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
        </record>
        <record id="iva12" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA12</field>
            <field name="description">IVA12 (taxa intermédia Madeira)</field>
            <field name="amount">12</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="tax_group_id" ref="tax_group_iva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
        </record>
        <record id="iva9" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA9</field>
            <field name="description">IVA9 (taxa intermédia Açores)</field>
            <field name="amount">9</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="tax_group_id" ref="tax_group_iva_9"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
        </record>
        <record id="iva6" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA6</field>
            <field name="description">IVA6 (taxa reduzida Portugal Continental)</field>
            <field name="amount">6</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_iva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_vendas_faturas_reduzida_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_vendas_faturas_reduzida_tag')],
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_vendas_notas_credito_reduzida_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_vendas_notas_credito_reduzida_tag')],
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
        </record>
        <record id="iva5" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA5</field>
            <field name="description">IVA5 (taxa reduzida Madeira)</field>
            <field name="amount">5</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="tax_group_id" ref="tax_group_iva_5"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
        </record>
        <record id="iva4" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA4</field>
            <field name="description">IVA4 (taxa reduzida Açores)</field>
            <field name="amount">4</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="tax_group_id" ref="tax_group_iva_4"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
        </record>
        <record id="iva0" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA0</field>
            <field name="description">IVA0</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_iva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_vendas_faturas_isento_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_vendas_faturas_isento_tag')],
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_vendas_notas_credito_isento_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_vendas_notas_credito_isento_tag')],
                    'account_id': ref('chart_2433'),
                }),
            ]"/>
        </record>
        <record id="compiva23" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA23 compra</field>
            <field name="description">IVA23 compra (taxa normal Portugal Continental)</field>
            <field name="amount">23</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_23"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_despesas_faturas_normal_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_despesas_faturas_normal_tag')],
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_despesas_notas_credito_normal_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_despesas_notas_credito_normal_tag')],
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
        </record>
        <record id="compiva22" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA22 compra</field>
            <field name="description">IVA22 compra (taxa normal Madeira)</field>
            <field name="amount">22</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_22"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
        </record>
        <record id="compiva16" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA16 compra</field>
            <field name="description">IVA16 compra (taxa normal Açores)</field>
            <field name="amount">16</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_16"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
        </record>
        <record id="compiva13" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA13 compra</field>
            <field name="description">IVA13 compra (taxa intermédia Portugal Continental)</field>
            <field name="amount">13</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_13"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_despesas_faturas_intermedia_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_despesas_faturas_intermedia_tag')],
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_despesas_notas_credito_intermedia_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_despesas_notas_credito_intermedia_tag')],
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
        </record>
        <record id="compiva12" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA12 compra</field>
            <field name="description">IVA12 compra (taxa intermédia Madeira)</field>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
        </record>
        <record id="compiva9" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA9 compra</field>
            <field name="description">IVA9 compra (taxa intermédia Açores)</field>
            <field name="amount">9</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_9"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
        </record>
        <record id="compiva6" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA6 compra</field>
            <field name="description">IVA6 compra (taxa reduzida Portugal Continental)</field>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_despesas_faturas_reduzida_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_despesas_faturas_reduzida_tag')],
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_despesas_notas_credito_reduzida_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_despesas_notas_credito_reduzida_tag')],
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
        </record>
        <record id="compiva5" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA5 compra</field>
            <field name="description">IVA5 compra (taxa reduzida Madeira)</field>
            <field name="amount">5</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_5"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
        </record>
        <record id="compiva4" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA4 compra</field>
            <field name="description">IVA4 compra (taxa reduzida Açores)</field>
            <field name="amount">4</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_4"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),
                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
        </record>
        <record id="compiva0" model="account.tax.template">
            <field name="chart_template_id" ref="pt_chart_template"/>
            <field name="name">IVA0 compra</field>
            <field name="description">IVA0 compra</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_despesas_faturas_isento_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_despesas_faturas_isento_tag')],
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'repartition_type': 'base',
                    'plus_report_expression_ids': [ref('tax_report_pt_base_despesas_notas_credito_isento_tag')],
                }),
                (0,0, {
                    'repartition_type': 'tax',
                    'plus_report_expression_ids': [ref('tax_report_pt_tax_despesas_notas_credito_isento_tag')],
                    'account_id': ref('chart_2432'),
                }),
            ]"/>
        </record>
    </data>
</odoo>
