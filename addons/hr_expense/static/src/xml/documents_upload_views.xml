<?xml version="1.0" encoding="UTF-8"?>

<templates>
    <t t-name="hr.expense.DocumentsHiddenUploadForm">
        <div class="d-none o_expense_documents_upload">
            <t t-call="HiddenInputFile">
                <t t-set="multi_upload" t-value="true"/>
                <t t-set="fileupload_id" t-value="widget.fileUploadID"/>
                <t t-set="fileupload_action" t-translation="off">/web/binary/upload_attachment</t>
                <input type="hidden" name="model" t-att-value="'hr.expense'"/>
                <input type="hidden" name="id" t-att-value="0"/>
            </t>
        </div>
    </t>

    <t t-name="hr.expense.DocumentDropZone">
        <div class="o_drop_area d-none">
            <i class="fa fa-upload fa-10x"></i>
        </div>
    </t>

    <t t-extend="ListView.buttons" t-name="ExpensesListView.buttons">
        <t t-jquery="button.o_list_button_add" t-operation="after">
            <button type="button" t-att-class="'d-none d-md-block btn' + (!widget.isMobile ? ' btn-secondary' : '') + ' o_button_upload_expense'">
                Scan
            </button>
        </t>

        <t t-jquery="button.o_list_button_add" t-operation="before">
            <button type="button" t-att-class="'d-block d-md-none btn' + (widget.isMobile ? ' btn-primary' : '') + ' o_button_upload_expense'">
                Scan
            </button>
        </t>

        <!-- hr.expense buttons -->
        <t t-jquery="button.o_list_button_add" t-operation="after">
            <button type="button" t-att-class="'btn btn-secondary' + (widget.isExpense ? '' : ' d-none') + ' o_button_create_report'">
                Create Report
            </button>
        </t>

        <t t-jquery="button.o_list_button_add" t-operation="replace">
            <button type="button" t-att-class="'btn' + (widget.isMobile ? ' btn-secondary' : ' btn-primary') + ' o_list_button_add'" title="Create record" accesskey="c">
                Create
            </button>
        </t>
    </t>
</templates>
