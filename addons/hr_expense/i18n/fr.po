# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-06 20:35+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON> Ron<PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "%(date_from)s - %(date_to)s"
msgstr "%(date_from)s - %(date_to)s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"%(user)s confirms this expense is not a duplicate with similar expense."
msgstr ""
"%(user)s confirme que cette dépense n'est pas un doublon avec une dépense "
"similaire."

#. module: hr_expense
#: model:ir.actions.report,print_report_name:hr_expense.action_report_hr_expense_sheet
msgid ""
"'Expenses - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"
msgstr ""
"'Dépenses - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "1 %(exp_cur)s = %(rate)s %(comp_cur)s"
msgstr "1 %(exp_cur)s = %(rate)s %(comp_cur)s"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"<span class=\"d-inline-block\"><i class=\"text-muted\">Use this reference as"
" a subject prefix when submitting by email.</i></span>"
msgstr ""
"<span class=\"d-inline-block\"><i class=\"text-muted\">Utilisez cette "
"référence comme préfixe de sujet lors de la soumission par "
"e-mail.</i></span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Les valeurs définies ici sont"
" propres à l'entreprise.\"/>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Company Expense Journal</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">Journal de dépenses (payées par la société)</span>\n"
"<span class=\"fa fa-lg fa-building-o\" title=\"Les valeurs définies ici sont spécifiques à l'entreprise.\" role=\"img\" aria-label=\"Les valeurs définies ici sont spécifiques à l'entreprise.\" groups=\"base.group_multi_company\"/>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Employee Expense Journal</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">Journal de dépenses (payées par un employé)</span>\n"
"<span class=\"fa fa-lg fa-building-o\" title=\"Les valeurs définies ici sont spécifiques à l'entreprise.\" role=\"img\" aria-label=\"Les valeurs définies ici sont spécifiques à l'entreprise.\" groups=\"base.group_multi_company\"/>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "<span class=\"oe_inline o_form_label ms-1 me-1\"> ( </span>"
msgstr "<span class=\"oe_inline o_form_label ms-1 me-1\"> ( </span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "<span class=\"oe_inline o_form_label ms-1 me-3\"> ) </span>"
msgstr "<span class=\"oe_inline o_form_label ms-1 me-3\"> ) </span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "<span>@</span>"
msgstr "<span>@</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Date:</strong>"
msgstr "<strong>Date :</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Description:</strong>"
msgstr "<strong>Description :</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Employee:</strong>"
msgstr "<strong>Employé :</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Payment By:</strong>"
msgstr "<strong>Paiement par :</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Validated By:</strong>"
msgstr "<strong>Validée par :</strong>"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__account_id
msgid "Account"
msgstr "Compte"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Accounting"
msgstr "Comptabilité"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__accounting_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__accounting_date
msgid "Accounting Date"
msgstr "Date comptable"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_state
msgid "Activity State"
msgstr "Status de l'activité"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.mail_activity_type_action_config_hr_expense
#: model:ir.ui.menu,name:hr_expense.hr_expense_menu_config_activity_type
msgid "Activity Types"
msgstr "Types d'activités"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_manager
msgid "Administrator"
msgstr "Administrateur"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Alias"
msgstr "Alias"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_user
msgid "All Approver"
msgstr "Approbateur de tout"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_all
msgid "All Expense Reports"
msgstr "Tous les rapports de dépenses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all
msgid "All Reports"
msgstr "Tous les rapports"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__amount_residual
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__amount_residual
msgid "Amount Due"
msgstr "Montant dû"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/mixins/document_upload.js:0
#, python-format
msgid "An error occurred during the upload"
msgstr "Une erreur est survenue lors du chargement"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__account_id
msgid "An expense account is expected"
msgstr "Un compte de frais est attendu"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/expense_form_view.js:0
#, python-format
msgid "An expense of same category, amount and date already exists."
msgstr "Une dépense de même catégorie, montant et date existe déjà."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "An expense report must contain only lines from the same company."
msgstr ""
"Un rapport de dépenses doit seulement contenir des lignes de la même "
"société."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_distribution
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__analytic_distribution
msgid "Analytic"
msgstr "Analytique"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_analytic_account
msgid "Analytic Account"
msgstr "Compte analytique"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_distribution_search
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "Recherche de répartition analytique"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Applicabilités du plan analytique"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_precision
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__analytic_precision
msgid "Analytic Precision"
msgstr "Précision analytique"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Apple App Store"
msgstr "App Store d'Apple"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_date
msgid "Approval Date"
msgstr "Date de validation"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#, python-format
msgid "Approve"
msgstr "Approuver"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
#, python-format
msgid "Approve Report"
msgstr "Approuver le rapport"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__approved
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__approve
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr "Approuvé"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_by
msgid "Approved By"
msgstr "Approuvé par"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_on
msgid "Approved On"
msgstr "Approuvé le"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Archived"
msgstr "Archivé"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#, python-format
msgid "Attach Receipt"
msgstr "Joindre le reçu"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Attach a receipt - usually an image or a PDF file."
msgstr "Joindre un reçu - normalement une image ou un fichier PDF."

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_ir_attachment
msgid "Attachment"
msgstr "Pièce jointe"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_attachment_count
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__attachment_ids
msgid "Attachments"
msgstr "Pièces jointes"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__attachment_ids
msgid "Attachments of expenses"
msgstr "Pièces jointes de dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "Bank Journal"
msgstr "Journal de banque"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__reference
msgid "Bill Reference"
msgstr "Référence de la facture"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_ids
msgid ""
"Both price-included and price-excluded taxes will behave as price-included "
"taxes for expenses."
msgstr ""
"Les taxes incluses dans le prix et les taxes exclues du prix se comporteront"
" comme des taxes incluses dans le prix pour les dépenses."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_approve
msgid "Can Approve"
msgstr "Peut approuver"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_reset
msgid "Can Reset"
msgstr "Peut réinitialiser"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,field_description:hr_expense.field_product_template__can_be_expensed
#: model_terms:ir.ui.view,arch_db:hr_expense.product_template_search_view_inherit_hr_expense
msgid "Can be Expensed"
msgstr "Peut être inséré dans une dépense"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
msgid "Cancel"
msgstr "Annuler"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__product_ids
msgid "Categories"
msgstr "Catégories"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Category"
msgstr "Catégorie"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category:"
msgstr "Catégorie :"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category: not found"
msgstr "Catégorie : introuvable"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Certified honest and conform,<br/>(Date and signature).<br/><br/>"
msgstr "Certifié conforme sur l'honneur,<br/>(Date et signature).<br/><br/>"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_communication_product_template
msgid "Communication"
msgstr "Communication"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__company_id
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__company_account
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Company"
msgstr "Société"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr "Configuration"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Il est possible de convertir deux unités de mesures si elles appartiennent à"
" la même catégorie. Cette conversion utilise les ratios définis pour ces "
"unités."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Create"
msgstr "Créer"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#, python-format
msgid "Create Report"
msgstr "Créer le rapport"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
msgid "Create a new expense report"
msgstr "Créer un nouveau rapport de dépenses"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Create a report to submit one or more expenses to your manager."
msgstr ""
"Créer un rapport pour soumettre une ou plusieurs dépenses à votre manager."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Create expenses from incoming emails"
msgstr "Créer des dépenses provenant des e-mails entrants"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Create new expenses to get statistics."
msgstr "Créez de nouvelles dépenses pour obtenir des statistiques."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Create record"
msgstr "Créer un enregistrement"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__create_date
msgid "Created on"
msgstr "Créé le"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__currency_id
msgid "Currency"
msgstr "Devise"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_rate
msgid "Currency Rate"
msgstr "Taux de la devise"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Date"
msgstr "Date"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Dear"
msgstr "Cher"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_alias_prefix
msgid "Default Alias Name for Expenses"
msgstr "Alias par défaut pour les dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__company_expense_journal_id
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__company_expense_journal_id
msgid "Default Company Expense Journal"
msgstr "Journal par défaut pour les dépenses de la société"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__expense_journal_id
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_journal_id
msgid "Default Expense Journal"
msgstr "Journal par défaut pour les dépenses"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Default Journals"
msgstr "Journaux par défaut"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Default accounting journal for expenses paid by employees."
msgstr ""
"Journal comptable par défaut pour les dépenses payées par les employés."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Default accounting journal for expenses paid by the company."
msgstr "Journal comptable par défaut pour les dépenses payées par la société."

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Department"
msgstr "Département"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__name
msgid "Description"
msgstr "Description"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Did you try the mobile app?"
msgstr "Avez-vous essayé l'application mobile ?"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Digitalize your receipts with OCR and Artificial Intelligence"
msgstr "Numériser vos reçus avec l'OCR et l'Intelligence Artificielle"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: hr_expense
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid ""
"Do not keep your expense tickets in your pockets any longer. Just snap a "
"picture of your receipt and let Odoo digitalizes it for you. The OCR and "
"Artificial Intelligence will fill the data automatically."
msgstr ""
"Ne gardez plus vos tickets dans vos poches. Prenez simplement une photo de "
"votre reçu et laissez Odoo le numériser pour vous. L'OCR et l'Intelligence "
"Artificielle rempliront les données automatiquement."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domaine"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__done
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__done
msgid "Done"
msgstr "Fait"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/mixins/qrcode.js:0
#, python-format
msgid "Download our App"
msgstr "Télécharger notre app"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__draft
msgid "Draft"
msgstr "Brouillon"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Drag and drop files to create expenses"
msgstr "Glissez et déposez des fichiers pour créer des dépenses"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/wizard/hr_expense_approve_duplicate.py:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__duplicate_expense_ids
#, python-format
msgid "Duplicate Expense"
msgstr "Dupliquer la dépense"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Employee"
msgstr "Employé"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__own_account
msgid "Employee (to reimburse)"
msgstr "Employé (à rembourser)"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_account
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_account_employee_expenses
msgid "Employee Expenses"
msgstr "Dépenses de l'employé"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__address_id
msgid "Employee Home Address"
msgstr "Adresse personnelle de l'employé"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Enter a name then choose a category and configure the amount of your "
"expense."
msgstr ""
"Saisissez un nom, puis choisissez une catégorie et configurez le montant de "
"votre dépense."

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_move_line__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__expense_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_res_users__expense_manager_id
#: model:ir.model.fields.selection,name:hr_expense.selection__account_analytic_applicability__business_domain__expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expense"
msgstr "Dépense"

#. module: hr_expense
#: model:mail.activity.type,name:hr_expense.mail_act_expense_approval
msgid "Expense Approval"
msgstr "Validation de dépenses"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_approve_duplicate
msgid "Expense Approve Duplicate"
msgstr "Dépense dupliquée"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_employee_tree_inherit_expense
msgid "Expense Approver"
msgstr "Approbateur des dépenses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Expense Categories"
msgstr "Catégories de dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__date
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expense Date"
msgstr "Date de la dépense"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expense Digitalization (OCR)"
msgstr "Numérisation des dépenses (OCR)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__journal_id
msgid "Expense Journal"
msgstr "Journal des dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__expense_line_ids
msgid "Expense Lines"
msgstr "Ligne de dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_editable
msgid "Expense Lines Are Editable By Current User"
msgstr "Les lignes de dépenses sont modifiables par l'utilisateur actuel"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__expense_manager_id
msgid "Expense Manager"
msgstr "Manager des dépenses"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Expense Refuse Reason Wizard"
msgstr "Assistant des raisons de refus d'une dépense"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sheet_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_move_form_inherit_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_payment_form_inherit_expense
msgid "Expense Report"
msgstr "Rapport de dépense"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__name
msgid "Expense Report Summary"
msgstr "Résumé de la dépense"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_report
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Reports"
msgstr "Rapports de dépense"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_filtered
msgid "Expense Reports Analysis"
msgstr "Analyse des dépenses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_to_approve
msgid "Expense Reports to Approve"
msgstr "Dépenses à approuver"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_bank_statement_line__expense_sheet_id
#: model:ir.model.fields,field_description:hr_expense.field_account_move__expense_sheet_id
#: model:ir.model.fields,field_description:hr_expense.field_account_payment__expense_sheet_id
msgid "Expense Sheet"
msgstr "Dépense"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_split
msgid "Expense Split"
msgstr "Fractionnement des dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__expense_split_line_ids
msgid "Expense Split Line"
msgstr "Ligne de fractionnement de dépense"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_split_wizard
msgid "Expense Split Wizard"
msgstr "Assistant fractionnement de dépense"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid "Expense Validate Duplicate"
msgstr "Dépense dupliquée"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Expense categories can be reinvoiced to your customers."
msgstr "Les catégories de dépenses peuvent être refacturées à vos clients."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_move.py:0
#, python-format
msgid "Expense entry Created"
msgstr "Écriture de dépense créée"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr "Motif de refus de la dépense"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense report approved"
msgstr "Dépense approuvée"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_paid
msgid "Expense report paid"
msgstr "Dépense remboursée"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense report refused"
msgstr "Dépense refusée"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid ""
"Expense reports regroup all the expenses incurred during a specific event."
msgstr ""
"Les rapports de dépenses regroupent l'ensemble des dépenses engagées lors "
"d'un événement précis."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Expense split"
msgstr "Fractionnement de la dépense"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#, python-format
msgid "Expenses"
msgstr "Dépenses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_all_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_pivot
msgid "Expenses Analysis"
msgstr "Analyse des dépenses"

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_hr_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Expenses Report"
msgstr "Rapport de dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_department__expense_sheets_to_approve_count
msgid "Expenses Reports to Approve"
msgstr "Rapports de dépenses à approuver"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses by Date"
msgstr "Dépenses par date"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Expenses must have the same To Reimburse status."
msgstr "Les dépenses doivent avoir le même statut 'À rembourser'."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expenses of Your Team Member"
msgstr "Dépenses des membres de votre équipe"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.account_journal_dashboard_kanban_view_inherit_hr_expense
msgid "Expenses to Process"
msgstr "Dépenses à traiter"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_refused
msgid "Explicitly Refused by manager or accountant"
msgstr "Refusé explicitement par le manager ou le comptable"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_follower_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_partner_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome par ex. fa-tasks"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Former Employees"
msgstr "Anciens employés"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Future Activities"
msgstr "Activités futures"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "General Information"
msgstr "Informations générales"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Generate Expenses"
msgstr "Générer des dépenses"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_gift_product_template
msgid "Gifts"
msgstr "Cadeaux"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_gift_product_template
msgid "Gifts to customers or vendors"
msgstr "Cadeaux aux clients ou aux fournisseurs"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Google Play Store"
msgstr "Google Play Store"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Group By"
msgstr "Regrouper par"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_multiple_currency
msgid "Handle lines with different currencies"
msgstr "Gérer des lignes avec des devises différentes"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__has_message
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__has_message
msgid "Has Message"
msgstr "A un message"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_travel_accommodation_product_template
msgid "Hotel, plane ticket, taxi, etc."
msgstr "Hôtel, billet d'avion, taxi, etc."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_ids
msgid "Hr Expense"
msgstr "Dépense RH"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_sheet_id
msgid "Hr Expense Sheet"
msgstr "Dépense RH"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "In Payment"
msgstr "En paiement"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_ids
msgid "Included taxes"
msgstr "Taxes comprises"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Incoming Emails"
msgstr "E-mails entrants"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_categories_tree_view
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Internal Note"
msgstr "Note interne"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__description
msgid "Internal Notes"
msgstr "Notes internes"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Invalid attachments!"
msgstr "Pièces jointes invalides !"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_editable
msgid "Is Editable By Current User"
msgstr "Est éditable par l'utilisateur actuel"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_is_follower
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__same_currency
msgid "Is currency_id different from the company_currency_id"
msgstr "Est-ce que currency_id est différent de company_currency_id"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_cost
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_has_cost
msgid "Is product with non zero cost selected"
msgstr "Est un produit avec un coût sélectionné"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "It all begins here - let's go!"
msgstr "Tout commence ici - allons-y !"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_journal
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__journal_displayed_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Journal"
msgstr "Journal"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__account_move_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_convert_rate
msgid "Label Convert Rate"
msgstr "Taux de conversion (texte)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Late Activities"
msgstr "Activités en retard"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__use_mailgateway
msgid "Let your employees record expenses by email"
msgstr "Laisser vos employés enregistrer des dépenses par e-mail"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Let's check out where you can manage all your employees expenses"
msgstr "Allons voir où vous pouvez gérer toutes les dépenses de vos employés"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Let's go back to your expenses."
msgstr "Retournons voir vos dépenses."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__user_id
msgid "Manager"
msgstr "Manager"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Managers can approve the report here, then an accountant can post the "
"accounting entries."
msgstr ""
"Les managers peuvent approuver le rapport ici, puis le comptable peut "
"publier les écritures comptables."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Managers can inspect all expenses from here."
msgstr "Les managers peuvent examiner toutes les dépenses d'ici."

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_meal_product_template
msgid "Meals"
msgstr "Repas"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_ids
msgid "Messages"
msgstr "Messages"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_mileage_product_template
msgid "Mileage"
msgstr "Kilométrage"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_all
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Expenses"
msgstr "Mes dépenses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "My Reports"
msgstr "Mes rapports"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Team"
msgstr "Mon équipe"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Name"
msgstr "Nom"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "New Expense Report"
msgstr "Nouveau rapport de dépenses"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "New Expense Report, paid by %(paid_by)s"
msgstr "Nouvelle dépense, payée par %(paid_by)s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "New Expense Reports"
msgstr "Nouveaux rapports de dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_summary
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé d'activité suivant"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No Home Address found for the employee %s, please configure one."
msgstr ""
"Aucune adresse personnelle de l'employé %s trouvée, veuillez la définir."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No attachment was provided"
msgstr "Aucune pièce jointe n'a été fournie"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_department_filtered
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "No data yet!"
msgstr "Pas encore de données !"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "No expense categories found. Let's create one!"
msgstr "Aucune catégorie de dépenses trouvée. Créons-en un !"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid "No expense report found. Let's create one!"
msgstr "Aucun rapport de dépenses trouvé. Créons-en un !"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid "No expense reports found. Let's create one!"
msgstr "Aucun rapport de dépenses trouvé. Créons-en un !"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Notes..."
msgstr "Notes..."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__attachment_number
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__attachment_number
msgid "Number of Attachments"
msgstr "Nombre de pièces jointes"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__expense_number
msgid "Number of Expenses"
msgstr "Nombre de dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/components/expense_dashboard.xml:0
#, python-format
msgid "Numbers computed from your personal expenses."
msgstr "Chiffres calculés à partir de vos dépenses personnelles."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Odoo"
msgstr "Odoo"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr ""
"Après avoir créé une dépense, soumettez-la pour validation à votre manager."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Once your <b>Expense Report</b> is ready, you can submit it to your manager "
"and wait for approval."
msgstr ""
"Dès que votre <b>rapport de dépenses</b> est prêt, vous pouvez le soumettre "
"à votre manager et attendre sa validation."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only HR Officers or the concerned employee can reset to draft."
msgstr ""
"Seul un responsable RH ou l'employé concerné peuvent remettre la demande en "
"brouillon. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only Managers and HR Officers can approve expenses"
msgstr "Seul un manager ou un responsable RH peut approuver des dépenses"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only Managers and HR Officers can refuse expenses"
msgstr "Seul un manager ou un responsable RH peut refuser des dépenses"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Or"
msgstr "Ou"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
msgid "Original Amount"
msgstr "Montant original"

#. module: hr_expense
#: model:product.template,name:hr_expense.product_product_no_cost_product_template
msgid "Others"
msgstr "Autres"

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_paid
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Paid"
msgstr "Payé"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__payment_mode
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_mode
msgid "Paid By"
msgstr "Payé par"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Partial"
msgstr "Partiel"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_state
msgid "Payment Status"
msgstr "Statut de paiement"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
#, python-format
msgid "Payment created for: %s"
msgstr "Paiement créé pour : %s"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_payment
msgid "Payments"
msgstr "Paiements"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_communication_product_template
msgid "Phone bills, postage, etc."
msgstr "Factures de téléphone, frais de port, etc."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Please specify a bank journal in order to generate accounting entries."
msgstr ""
"Veuillez indiquer un journal de banque afin de générer des écritures "
"comptables."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Please specify an expense journal in order to generate accounting entries."
msgstr ""
"Veuillez indiquer un journal des dépenses afin de générer des écritures "
"comptables."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
#, python-format
msgid "Post Entries"
msgstr "Comptabiliser les écritures"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Post Journal Entries"
msgstr "Comptabiliser les pièces"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__post
msgid "Posted"
msgstr "Comptabilisé"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Powered by"
msgstr "Généré par"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price"
msgstr "Prix"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price in Company Currency"
msgstr "Prix dans la devise de la société"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Price:"
msgstr "Prix :"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_id
msgid "Product"
msgstr "Produit"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_description
msgid "Product Description"
msgstr "Description du produit"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Product Name"
msgstr "Nom du produit"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_tree_view
msgid "Product Variants"
msgstr "Variantes de produit"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_public
msgid "Public Employee"
msgstr "Fonctionnaire"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Qty"
msgstr "Qté"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__quantity
msgid "Quantity"
msgstr "Quantité"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Ready? You can save it manually or discard modifications from here. You "
"don't <em>need to save</em> - Odoo will save eveyrthing for you when you "
"navigate."
msgstr ""
"Prêt ? Vous pouvez l'enregistrer manuellement ou ignorer les modifications "
"d'ici. Vous n'avez pas <em>besoin de l'enregistrer</em> - Odoo enregistrera "
"tout pour vous quand vous naviguez. "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__reason
msgid "Reason"
msgstr "Motif"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Reason :"
msgstr "Motif :"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason to refuse Expense"
msgstr "Motif du refus de la dépense"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Ref."
msgstr "Réf."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_ref_editable
msgid "Reference Is Editable By Current User"
msgstr "La référence est éditable par l'utilisateur actuel"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Refuse"
msgstr "Refuser"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr "Refuser la dépense"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__refused
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__cancel
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused"
msgstr "Refusée"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused Expenses"
msgstr "Dépenses refusées"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#: model:ir.model,name:hr_expense.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#, python-format
msgid "Register Payment"
msgstr "Enregistrer un paiement"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_payroll_expense
msgid "Reimburse Expenses in Payslip"
msgstr "Rembourser les dépenses sur la fiche de paie"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse expenses in payslips"
msgstr "Rembourser les dépenses sur les fiches de paie"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse in Payslip"
msgstr "Rembourser sur la fiche de paie"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Report"
msgstr "Rapport"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_currency_id
msgid "Report Company Currency"
msgstr "Rapport dans la devise de la société"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_reports
msgid "Reporting"
msgstr "Analyse"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reset to Draft"
msgstr "Remettre en brouillon"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_user_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_meal_product_template
msgid "Restaurants, business lunches, etc."
msgstr "Restaurants, repas d'affaires, etc."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_categories_tree_view
msgid "Sales Price"
msgstr "Prix de vente"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sample
msgid "Sample"
msgstr "Échantillon"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Scan"
msgstr "Scanner"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/components/qrcode_action.xml:0
#: code:addons/hr_expense/static/src/xml/expense_qr_modal_template.xml:0
#, python-format
msgid "Scan this QR code to get the Odoo app:"
msgstr "Scannez ce QR code pour obtenir l'application Odoo :"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,help:hr_expense.field_res_users__expense_manager_id
msgid ""
"Select the user responsible for approving \"Expenses\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"Sélectionnez l'utilisateur responsable de la validation des \"Dépenses\" de cet employé.\n"
"Si vide, la validation est faite par un administrateur ou un approbateur (déterminé dans les paramètres/utilisateurs)."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Selected Unit of Measure for expense %(expense)s does not belong to the same"
" category as the Unit of Measure of product %(product)s."
msgstr ""
"L'unité de mesure sélectionnée pour la dépense %(expense)s n'appartient pas "
"à la même catégorie que l'unité de mesure du produit %(product)s."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"Send an email to this email alias with the receipt in attachment to create "
"an expense in one click. If the first word of the mail subject contains the "
"category's internal reference or the category name, the corresponding "
"category will automatically be set. Type the expense amount in the mail "
"subject to set it on the expense too."
msgstr ""
"Envoyez un e-mail à cet alias avec le reçu en pièce jointe pour créer une "
"dépense en un clic. Si le premier mot de l'objet de l'e-mail contient la "
"référence interne de la catégorie ou le nom de la catégorie, la catégorie "
"correspondante sera automatiquement définie. Tapez le montant de la dépense "
"dans l'objet de l'e-mail pour le définir également sur la dépense."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_expense_extract
msgid "Send bills to OCR to generate expenses"
msgstr "Envoyer des factures à l'OCR pour générer des charges"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_configuration
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_global_settings
msgid "Settings"
msgstr "Paramètres"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Setup your domain alias"
msgstr "Configurer votre domaine d'alias"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__sheet_ids
msgid "Sheet"
msgstr "Feuille"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sheet_is_editable
msgid "Sheet Is Editable"
msgstr "Feuille est modifiable"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Montrez tous les enregistrements pour lesquels la date des prochaines "
"actions est pour aujourd'hui ou avant"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid ""
"Snap pictures of your receipts and let Odoo<br> automatically create "
"expenses for you."
msgstr ""
"Prenez une photo de vos tickets de caisse et laissez <br>Odoo "
"automatiquement créer des dépenses pour vous."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,help:hr_expense.field_product_template__can_be_expensed
msgid "Specify whether the product can be selected in an expense."
msgstr "Indiquez si le produit peut être sélectionné dans une dépense."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Split Expense"
msgstr "Fractionner la dépense"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/wizard/hr_expense_split_wizard.py:0
#, python-format
msgid "Split Expenses"
msgstr "Fractionner les dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__split_possible
msgid "Split Possible"
msgstr "Fractionnement possible"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product__standard_price_update_warning
msgid "Standard Price Update Warning"
msgstr "Avertissement pour la mise à jour du prix standard"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__state
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Status"
msgstr "Statut"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
#, python-format
msgid "Submit"
msgstr "Soumettre"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#, python-format
msgid "Submit to Manager"
msgstr "Soumettre au manager"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__reported
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__submit
msgid "Submitted"
msgstr "Soumis"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__tax_ids
msgid "Tax"
msgstr "Taxe"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Tax 15%"
msgstr "Taxe 15%"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__amount_tax_company
msgid "Tax amount"
msgstr "Montant de la taxe"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__amount_tax
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__amount_tax
msgid "Tax amount in Currency"
msgstr "Montant de la taxe dans la devise"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__amount_tax_company
msgid "Tax amount in company currency"
msgstr "Montant de la taxe dans la devise de la société"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__amount_tax
msgid "Tax amount in currency"
msgstr "Montant de la taxe dans la devise"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_amount_taxes
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__total_amount_taxes
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Taxes"
msgstr "Taxes"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_team_approver
msgid "Team Approver"
msgstr "Approbateur de l'équipe"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_res_company__company_expense_journal_id
#: model:ir.model.fields,help:hr_expense.field_res_config_settings__company_expense_journal_id
msgid "The company's default journal used when a company expense is created."
msgstr ""
"Le journal de l'entreprise utilisé par défaut pour les dépenses payées par "
"la société."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_res_company__expense_journal_id
#: model:ir.model.fields,help:hr_expense.field_res_config_settings__expense_journal_id
msgid ""
"The company's default journal used when an employee expense is created."
msgstr ""
"Le journal de l'entreprise utilisé par défaut pour les dépenses payées par "
"un employé."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The current user has no related employee. Please, create one."
msgstr "L'utilisateur actuel n'a pas d'employé associé. Veuillez en créer un."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The expense reports were successfully approved."
msgstr "Les rapports des dépenses ont été approuvés avec succès."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid ""
"The first word of the email subject did not correspond to any category code."
" You'll have to set the category manually on the expense."
msgstr ""
"Le premier mot de l'objet de l'e-mail ne correspond à aucun code de "
"catégorie. Vous devrez définir la catégorie manuellement sur la dépense."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid ""
"The following approved expenses have similar employee, amount and category "
"than some expenses of this report. Please verify this report does not "
"contain duplicates."
msgstr ""
"Les dépenses approuvées suivantes ont un employé, un montant et une "
"catégorie similaires à certaines dépenses de ce rapport. Veuillez vérifier "
"que ce rapport ne contient pas de doublons."

#. module: hr_expense
#: model:ir.model.constraint,message:hr_expense.constraint_hr_expense_sheet_journal_id_required_posted
msgid "The journal must be set on posted expense"
msgstr "Le journal doit être défini sur une dépense comptabilisée"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__journal_id
msgid "The journal used when the expense is done."
msgstr "Le journal utilisé lorsque la dépense est terminée."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "The payment method used when the expense is paid by the company."
msgstr ""
"Le mode de paiement utilisé lorsque la dépense est payée par la société."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"The private address of the employee is required to post the expense report. "
"Please add it on the employee form."
msgstr ""
"L'adresse privée de l'employé est requise pour enregistrer la dépense. "
"Veuillez l'ajouter à la fiche de l'employé."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "The status of all your current expenses is visible from here."
msgstr "Le statut de toutes vos dépenses actuelles est visible d'ici."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_split_wizard__split_possible
msgid "The sum of after split shut remain the same"
msgstr "La somme après le fractionnement reste identique"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "There are no expense reports to approve."
msgstr "Il n'y a aucun rapport de dépenses à approuver."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/product_product.py:0
#, python-format
msgid ""
"There are unsubmitted expenses linked to this category. Updating the "
"category cost will change expense amounts. Make sure it is what you want to "
"do."
msgstr ""
"Des dépenses non soumises sont liées à cette catégorie. La mise à jour du "
"coût de la catégorie modifiera les montants des dépenses. Assurez-vous que "
"c'est bien ce que vous voulez faire."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "There you go - expense management in a nutshell!"
msgstr "Voilà - la gestion des dépenses en un mot !"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"This note will be shown to users when they select this expense product."
msgstr ""
"Cette note sera présentée aux utilisateurs lorsqu'ils sélectionneront ce "
"produit de dépense."

#. module: hr_expense
#: model:digest.tip,name:hr_expense.digest_tip_hr_expense_0
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid "Tip: Snap pictures of your receipts with the remote app"
msgstr "Astuce : prenez des photos de vos reçus avec l'application à distance"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To Report"
msgstr "À rapporter"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__draft
msgid "To Submit"
msgstr "À soumettre"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Today Activities"
msgstr "Activités du jour"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount_company
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Total"
msgstr "Total"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_amount
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__total_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Total Amount"
msgstr "Montant total"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
msgid "Total Company Currency"
msgstr "Total dans la devise de la société"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__total_amount
msgid "Total In Currency"
msgstr "Total dans la devise"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__untaxed_amount
msgid "Total Untaxed Amount In Currency"
msgstr "Montant total hors taxes dans la devise"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_split_wizard__total_amount_original
msgid "Total amount of the original Expense that we are splitting"
msgstr "Montant total de la dépense initiale que nous divisons"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__total_amount_original
msgid "Total amount original"
msgstr "Montant total initial"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_travel_accommodation_product_template
msgid "Travel & Accommodation"
msgstr "Voyage & Hébergement"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__unit_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Unit Price"
msgstr "Prix unitaire"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__unit_amount_display
msgid "Unit Price Display"
msgstr "Affichage du prix unitaire"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_id
msgid "Unit of Measure"
msgstr "Unité de mesure"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__untaxed_amount
msgid "Untaxed Amount"
msgstr "Montant hors taxes"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_category_id
msgid "UoM Category"
msgstr "Catégorie d'UdM"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
#, python-format
msgid "Upload"
msgstr "Télécharger"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Use the breadcrumbs to go back to the list of expenses."
msgstr "Utilisez le fil d'Ariane pour retourner à la liste des dépenses."

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_users
msgid "User"
msgstr "Utilisateur"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_approve_duplicate_action
msgid "Validate Duplicate Expenses"
msgstr "Valider les dépenses en double"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "View Attachments"
msgstr "Voir les pièces jointes"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "View Expense"
msgstr "Voir la dépense"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "View Report"
msgstr "Voir le rapport"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Wasting time recording your receipts? Let’s try a better way."
msgstr "Vous perdez du temps à enregistrer vos reçus ? Changeons de méthode."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"When the cost of an expense product is different than 0, then the user using"
" this product won't be able to change the amount of the expense, only the "
"quantity. Use a cost different than 0 for expense categories funded by the "
"company at fixed cost like allowances for mileage, per diem, accommodation "
"or meal."
msgstr ""
"Lorsque le coût d'un produit de dépense est différent de 0, alors "
"l'utilisateur utilisant ce produit ne pourra pas modifier le montant de la "
"dépense, seulement la quantité. Utilisez un coût différent de 0 pour les "
"catégories de dépenses financées par l'entreprise à coût fixe comme les "
"indemnités de kilométrage, les indemnités journalières, l'hébergement ou les"
" repas."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_tax
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_has_tax
msgid "Whether tax is defined on a selected product"
msgstr "Si la taxe est définie sur un produit sélectionné"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__wizard_id
msgid "Wizard"
msgstr "Assistant"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit the reference of this expense report."
msgstr ""
"Vous n'êtes pas autorisé à modifier la référence de ce rapport de dépenses."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit this expense report."
msgstr "Vous n'êtes pas autorisé à modifier ce rapport de dépenses."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can not create report without category."
msgstr "Vous ne pouvez pas créer de rapport sans catégorie."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "You can now submit it to the manager from the following link."
msgstr ""
"Vous pouvez maintenant le soumettre au manager à partir du lien suivant."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only approve your department expenses"
msgstr "Vous pouvez seulement approuver les dépenses de votre département."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only generate accounting entry for approved expense(s)."
msgstr ""
"Des écritures comptables ne peuvent être générées que pour des dépenses "
"approuvées."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only refuse your department expenses"
msgstr "Vous pouvez seulement refuser les dépenses de votre département"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can't mix sample expenses and regular ones"
msgstr ""
"Vous ne pouvez pas mélanger des exemples de dépenses et des dépenses "
"régulières"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot add expenses of another employee."
msgstr "Vous ne pouvez pas ajouter des dépenses pour un autre employé."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot approve your own expenses"
msgstr "Vous ne pouvez pas approuver vous-même vos propres dépenses"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or approved expense."
msgstr "Vous ne pouvez pas supprimer une dépense comptabilisée ou approuvée."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or paid expense."
msgstr "Vous ne pouvez pas supprimer une dépense comptabilisée ou payée."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/analytic.py:0
#, python-format
msgid "You cannot delete an analytic account that is used in an expense."
msgstr ""
"Vous ne pouvez pas supprimer un compte analytique utilisé dans une dépense."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
#, python-format
msgid ""
"You cannot do this modification since the payment is linked to an expense "
"report."
msgstr ""
"Vous ne pouvez pas effectuer cette modification, puisque le paiement est lié"
" à une dépense."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot refuse your own expenses"
msgstr "Vous ne pouvez pas refuser vos propres dépenses"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report expenses for different companies in the same report."
msgstr ""
"Vous ne pouvez pas déclarer des dépenses de différentes sociétés dans le "
"même rapport."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report expenses for different employees in the same report."
msgstr ""
"Vous ne pouvez pas déclarer les dépenses de différents employés dans le même"
" rapport."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report the expenses without amount!"
msgstr "Vous ne pouvez pas déclarer les dépenses sans montant !"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report twice the same line!"
msgstr "Vous ne pouvez pas ajouter deux fois la même ligne !"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"You don't have the rights to bypass the validation process of this expense "
"report."
msgstr ""
"Vous n'avez pas le droit de contourner le processus de validation de cette "
"dépense."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"You don't have the rights to bypass the validation process of this expense."
msgstr ""
"Vous n'avez pas le droit de contourner le processus de validation de ces "
"dépenses."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You have no expense to report"
msgstr "Vous n'avez pas de dépense à déclarer"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You need to add a manual payment method on the journal (%s)"
msgstr "Vous devez ajouter un mode de paiement manuel au journal (%s)"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"You need to have at least one category that can be expensed in your database"
" to proceed!"
msgstr ""
"Vous devez avoir au moins une catégorie qui peut être passée en dépenses "
"dans votre base de données pour continuer !"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Your Expense"
msgstr "Votre dépense"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Your expense has been successfully registered."
msgstr "Votre dépense a été enregistrée avec succès."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Lunch"
msgstr "par ex. Déjeuner"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "e.g. Lunch with Customer"
msgstr "par ex. Déjeuner avec un Client"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "e.g. Trip to NY"
msgstr "e.g. Voyage à New York"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "has been refused"
msgstr "est refusé"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to be reimbursed"
msgstr "à rembourser"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to report"
msgstr "à rapporter"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "under validation"
msgstr "en cours de validation"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "use OCR to fill data from a picture of the bill"
msgstr ""
"utiliser l'OCR pour remplir les données à partir d'une image de la facture"
