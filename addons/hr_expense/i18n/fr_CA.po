# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_expense
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: French (Canada) (https://www.transifex.com/odoo/teams/41243/fr_CA/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr_CA\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "(e.g. <EMAIL>)"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "(if the expense has been paid by the employee directly);"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "(only such email addresses are authorized)"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "(used for reimbursement)"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ". Here are some advises to avoid conflictual situations:"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "2 ways to record expenses:"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"<i class=\"text-muted oe_edit_only\">Use [Reference] as a subject prefix for"
" incoming receipts</i>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:48
#, python-format
msgid ""
"<p>Approve the sheet here.</p><p>Tip: if you refuse, don’t forget to give "
"the reason thanks to the hereunder message tool</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:21
#, python-format
msgid "<p>Click here to create your first expense and add it under here.</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:44
#, python-format
msgid ""
"<p>Click on <b> Action Submit To Manager </b> to submit selected expenses to"
" your manager</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:31
#, python-format
msgid "<p>Once your Expense is ready, you can save it.</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:39
#, python-format
msgid "<p>Select expenses to submit them to your manager</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:52
#, python-format
msgid ""
"<p>The accountant receive approved expense reports.</p><p>He can post "
"journal entries in one click if taxes and accounts are right.</p>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>By email (advised)</strong><br/>\n"
"                                        <span class=\"small\">Send a snapshot whenever you get an expense</span>\n"
"                                    </span>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Centralized</strong><br/>\n"
"                                        <span class=\"small\">HR officer, manager</span>\n"
"                                    </span>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Classic encoding</strong><br/>\n"
"                                        <span class=\"small\">Log in in the system</span>\n"
"                                    </span>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Decentralized</strong><br/>\n"
"                                        <span class=\"small\">Employee’s team manager</span>\n"
"                                    </span>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>In batch (one report)</strong><br/>\n"
"                                        <span class=\"small\">Advised if lots of expenses</span>\n"
"                                    </span>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>One at a time</strong><br/>\n"
"                                        <span class=\"small\">Advised if few expenses</span>\n"
"                                    </span>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Date:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Description:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<strong>Employee(s) record expenses, </strong>and send expense reports to "
"their manager;"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Employee:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<strong>Hotel</strong><br/>\n"
"                                    Cost: 0.00 <i>(the cost of the ticket will be recorded on every expense)</i>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "<strong>Manager(s) approve or refuse expense reports;</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<strong>Others</strong><br/>\n"
"                                    Cost: 0.00"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Payment By:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<strong>Restaurant</strong><br/>\n"
"                                    Cost: 0.00 <i>(the cost of the ticket will be recorded on every expense)</i>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<strong>The accounting department posts journal entries and reimburses "
"employees </strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"<strong>Travel with Personal Car</strong><br/>\n"
"                                    Cost: 0.30 <i>(the price per mile reimbursed by the company is fixed)</i>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Validated By:</strong>"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/wizard/hr_expense_sheet_register_payment.py:83
#, python-format
msgid ""
"A payment of %s %s with the reference <a href='/mail/view?%s'>%s</a> related"
" to your expense %s has been made."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_account_id
msgid "Account"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_accountant
msgid "Accountant"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Accounting"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Accounting: Accountant or Adviser"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Actual expense sheets, not the refused ones"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Agree in advance with the customer:"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.air_ticket
#: model:product.template,name:hr_expense.air_ticket_product_template
msgid "Air Flight"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_all
msgid "All Expense Reports"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_all
msgid "All Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_account_id
msgid "An expense account is expected"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_analytic_account_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Analytic Account"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Approve"
msgstr ""

#. module: hr_expense
#: selection:hr.expense.sheet,state:0
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Approved Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "As employees, there are"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Ask your expert accountant what tax must be claimed to your customer"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:35
#, python-format
msgid "Attach your receipt here."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_bank_journal_id
msgid "Bank Journal"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_reference
msgid "Bill Reference"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Billing"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Business trip"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product_can_be_expensed
#: model:ir.model.fields,field_description:hr_expense.field_product_template_can_be_expensed
msgid "Can be Expensed"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_register_payment_view_form
msgid "Cancel"
msgstr "Annuler"

#. module: hr_expense
#: model:product.product,name:hr_expense.car_travel
#: model:product.template,name:hr_expense.car_travel_product_template
msgid "Car Travel Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Certified honest and conform,<br/>(Date and signature).<br/><br/>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid "Click here to create a new expense report."
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_approve_expense_sheet
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_to_pay_expense_sheet
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_to_post_expense_sheet
msgid "Click here to create new expenses."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:343
#, python-format
msgid "Click to add a new expense,"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Click to create a new expense category."
msgstr ""

#. module: hr_expense
#: selection:hr.expense,payment_mode:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_company_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Company"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet_register_payment_wizard_company_id
msgid "Company related to this journal"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Configure expense types"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Confirmed Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Congratulations, you're done!"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Create a new expense.<br>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Create employees with a Work Email"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_create_uid
msgid "Created by"
msgstr "Créé par"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_create_date
msgid "Created on"
msgstr "Créé le"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_currency_id
msgid "Currency"
msgstr "Devise"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings_module_sale_management
msgid "Customer Billing"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_accounting_date
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Date"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings_expense_alias_prefix
msgid "Default Alias Name for Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Department"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Depending on your company structure, the"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_description
msgid "Description"
msgstr "Description"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Documents"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Don't hesitate to"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_register_payment_view_form
msgid "Draft Payment"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Email Alias"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Emails"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Employee"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,payment_mode:0
msgid "Employee (to reimburse)"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_address_id
msgid "Employee Home Address"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "End"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Enjoy your Odoo experience,"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_move_line_expense_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Expense"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_date
msgid "Expense Date"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_name
msgid "Expense Description"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Expense Flow"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_journal_id
msgid "Expense Journal"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_expense_line_ids
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expense Lines"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Expense Products"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_id
msgid "Expense Report"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet_register_payment_wizard
msgid "Expense Report Register Payment wizard"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet_state
msgid "Expense Report State"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_name
msgid "Expense Report Summary"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Reports"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_filtered
msgid "Expense Reports Analysis"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_approve
msgid "Expense Reports To Approve"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_pay
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_pay
msgid "Expense Reports To Pay"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_post
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_post
msgid "Expense Reports To Post"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_approve
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_to_approve
msgid "Expense Reports to Approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Sheet"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Expense Types"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Expense refuse Reason wizard"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense report approved"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_paid
msgid "Expense report paid"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense report refused"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_confirmed
msgid "Expense report submitted, waiting approval"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_account_move_line_expense_id
msgid "Expense where the move line come from"
msgstr ""

#. module: hr_expense
#: model:ir.actions.server,name:hr_expense.hr_expense_submit_action_server
msgid "Expense: Submit To Manager"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
#: model:product.category,name:hr_expense.cat_expense
#: model:product.product,name:hr_expense.product_product_fixed_cost
#: model:product.template,name:hr_expense.product_product_fixed_cost_product_template
msgid "Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_action
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_all_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_pivot
msgid "Expenses Analysis"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Expenses Month"
msgstr ""

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_hr_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Expenses Report"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_all
msgid "Expenses Reports Analysis"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_department_expense_sheets_to_approve_count
msgid "Expenses Reports to Approve"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_department_expense_confirmed
msgid "Expenses To Approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Expenses by Month"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Expenses follow this flow:"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:438
#, python-format
msgid "Expenses must belong to the same Employee."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:566
#, python-format
msgid ""
"Expenses must have an expense journal specified to generate accounting "
"entries."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:442
#, python-format
msgid "Expenses must have been paid by the same entity (Company or employee)"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Expenses of Your Team Member"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Expenses to Invoice"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_to_submit
msgid "Expenses to Submit"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Expenses will go through these steps:"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Expenses: Manager"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_is_refused
msgid "Explicitely Refused by manager or acccountant"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Former Employees"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_approved_expense
msgid ""
"From here the accountant will be able to approve as well as refuse the "
"expenses which are verified by the HR Manager."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "General Information"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:16
#, python-format
msgid "Go to the expense to attach a receipt."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Grant such accountants with following access rights:"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Grant such users with “Officer” rights for Expense app"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Grant this user with “Manager” rights for Expense app"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Group By"
msgstr "Grouper par"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
msgid "HR Department"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Have a wonderful day,<br/>- The Odoo Team"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_hide_payment_method
msgid "Hide Payment Method"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.hotel_rent
#: model:product.template,name:hr_expense.hotel_rent_product_template
msgid "Hotel Accommodation"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_hr_expense_ids
msgid "Hr Expense"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_hr_expense_sheet_id
msgid "Hr Expense Sheet"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_id_7700
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_id
msgid "ID"
msgstr "Identifiant"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "If you track expenses on customer projects, you can charge them"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Install Analytic Accounting add-on"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Install Sales app"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Invoice Customers"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Invoicing at cost vs. at negotiated price"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_account_move_id
msgid "Journal Entry"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense___last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard___last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet___last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard___last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings_use_mailgateway
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Let your employees record expenses by email"
msgstr ""

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_manager
msgid "Manager"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:60
#, python-format
msgid "Managers can get all reports to approve from this menu."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_communication
msgid "Memo"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "My Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_unsubmitted
msgid "My Expenses to Submit"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_refused
msgid "My Refused Reports"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_all
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "My Reports"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "My Team Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "My Team Reports"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Name"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "New Expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "New Mail"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:266
#, python-format
msgid ""
"No Expense account found for the product %s (or for its category), please "
"configure one."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:231
#, python-format
msgid "No Home Address found for the employee %s, please configure one."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:210
#, python-format
msgid "No credit account found for the %s journal, please configure one."
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_refused
msgid "No refused reports to display."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Not Refused"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
msgid "Notes..."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_attachment_number
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_attachment_number
msgid "Number of Attachments"
msgstr ""

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_user
msgid "Officer"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Once a month"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Once approved, your expense report, goes to the accounting,"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Once approved, your expenses report goes to the accounting,"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"Once expense reports approved by managers, the accounting department checks "
"accounts, products and taxes used. Then they"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"Once setup, you get an efficient expenses tracking process for all your "
"employees."
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_approve_expense_sheet
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_to_pay_expense_sheet
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_to_post_expense_sheet
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:543
#, python-format
msgid "Only HR Officers can approve expenses"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:534
#, python-format
msgid "Only HR Officers can refuse expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"Optionally, <strong>expenses are invoiced to the customer "
"afterwards.</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Other Info"
msgstr ""

#. module: hr_expense
#: selection:hr.expense.sheet,state:0
#: model:mail.message.subtype,name:hr_expense.mt_expense_paid
msgid "Paid"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_partner_id
msgid "Partner"
msgstr "Partenaire"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_amount
msgid "Payment Amount"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_payment_mode
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_payment_mode
msgid "Payment By"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_payment_date
msgid "Payment Date"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_journal_id
msgid "Payment Method"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard_payment_method_id
msgid "Payment Type"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Periodically, you submit expenses report to your manager,"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_web_planner
msgid "Planner"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:271
#, python-format
msgid ""
"Please configure Default Expense account for Product expense: "
"`property_account_expense_categ_id`."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Populate your list of employees"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Post Journal Entries"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,state:0 selection:hr.expense.sheet,state:0
msgid "Posted"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_product_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Product"
msgstr "Produit"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Product Name"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
msgid "Product Template"
msgstr "Modèle de produit"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"Provide your customer with detailed reports (along with copies of receipts)"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Qty"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_quantity
msgid "Quantity"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Quicker to get reimbursed"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_reason
msgid "Reason"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Reason :"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason to refuse Expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Record a first expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Recording"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Recording Mode"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Ref."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Refuse"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,state:0 selection:hr.expense.sheet,state:0
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
msgid "Refused"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Refused Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports_refused
msgid "Refused Reports"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_sheet_register_payment_wizard_action
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_register_payment_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Register Payment"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Reimbursement"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Report"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,state:0
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Reported"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_reports
msgid "Reporting"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_request_approve_expense_sheet
msgid "Reports to Approve"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_request_to_pay_expense_sheet
msgid "Reports to Pay"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_request_to_post_expense_sheet
msgid "Reports to Post"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Resubmit"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Review expenses to approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "See how to invoice expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "See how to manage payables"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:93
#, python-format
msgid ""
"Selected Unit of Measure does not belong to the same category as the product"
" Unit of Measure"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Sell services and invoice expenses to customer"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Send an expense by email"
msgstr ""

#. module: hr_expense
#: model:web.planner,tooltip_planner:hr_expense.planner_hr_expense
msgid "Send receipts by email to create expenses efficiently."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Set Home Address to employees"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Set a manager in the employee form"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Set an email alias"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Set invoicing method on expense products"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_configuration
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_global_settings
msgid "Settings"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Setup your domain alias"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_product_can_be_expensed
#: model:ir.model.fields,help:hr_expense.field_product_template_can_be_expensed
msgid "Specify whether the product can be selected in an HR expense."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "State"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_state
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Status"
msgstr "Statut"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_state
msgid "Status of the expense."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Submit for Approval"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
msgid "Submit to Manager"
msgstr ""

#. module: hr_expense
#: selection:hr.expense.sheet,state:0
#: model:mail.message.subtype,name:hr_expense.mt_expense_confirmed
msgid "Submitted"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_untaxed_amount
msgid "Subtotal"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_tax_ids
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Taxes"
msgstr "Taxes"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet_register_payment_wizard_hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:56
#, python-format
msgid ""
"The accountant can register a payment to reimburse the employee directly."
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "The accountant validates entries and reimburse you."
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "The accountant validates journal entries and reimburse you."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "The first step if to"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet_journal_id
msgid "The journal used when the expense is done."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "The next step is to settle an internal policy to"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/wizard/hr_expense_sheet_register_payment.py:35
#, python-format
msgid "The payment amount must be strictly positive."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet_bank_journal_id
msgid "The payment method used when the expense is paid by the company."
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "The steps to process expenses are:"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "This guide helps you start with Odoo Expenses."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"This installs the Sales Management app.                             Expenses"
" can be invoiced to customers from sales orders."
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_to_approve
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "To Approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "To Pay"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "To Post"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,state:0
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "To Submit"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "To do:"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_total_amount
msgid "Total"
msgstr "Total"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_total_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Total Amount"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_unit_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Unit Price"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_product_uom_id
msgid "Unit of Measure"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_approved_expense
msgid "Unreported Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid ""
"Use this app to track expenses done by employees and reimbursed by the "
"company, or paid with a company credit cards directly. If a vendor bill is "
"sent to the company for payment, it’s better to use Vendor Bills instead of "
"expenses in the Accounting app."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_register_payment_view_form
msgid "Validate"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_responsible_id
msgid "Validation By"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
msgid "View Attached Documents"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "View Attachments"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
msgid "View Report"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "We hope this feature helped you manage your expenses efficiently."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "Welcome"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "What it covers and what are the limits"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "When you charge"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:26
#, python-format
msgid "Write the name of the product, or select an existing one."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:563
#, python-format
msgid "You can only generate accounting entry for approved expense(s)."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"You can setup a generic email alias to create incoming expenses easily. "
"Write an email with the receipt in attachment to create an expense line in "
"one click. If the mail subject contains the product's internal reference "
"between brackets, the product will be set automatically. Type the expense "
"amount in the mail subject to set it on the expense too."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:598
#, python-format
msgid "You cannot add expense lines of another employee."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:315
#, python-format
msgid "You cannot delete a posted expense."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:469
#, python-format
msgid "You cannot delete a posted or paid expense."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:111
#, python-format
msgid "You cannot report expenses for different employees in the same report!"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:109
#, python-format
msgid "You cannot report twice the same line!"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "You record expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "You record expenses,"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Your Expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "and"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "approval process"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "back to your customers automatically"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "can be:"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "configure the expense types"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Lunch"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
msgid "e.g. Lunch with Customer"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "e.g. Trip to NY"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "has been refused"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:344
#, python-format
msgid "or send receipts by email to %s."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "post them into the books"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "proceed with the employee reimbursement"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "send us an email"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "submit expenses to managers"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "that your company allows. Here are some examples:"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_planner
msgid "to describe<br/> your experience or to suggest improvements!"
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"The private address of the employee is required to post the expense report. "
"Please add it on the employee form."
msgstr ""
"L'adresse privée de l'employé est nécessaire pour afficher la note de frais. Veuillez l'ajouter sur le formulaire de l'employé."
