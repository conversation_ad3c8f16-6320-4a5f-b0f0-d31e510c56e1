<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="website.slide.upload.modal">
        <div>
            <div class="o_w_slide_upload_modal_container">
                <t t-call="{{widget.get('defaultTemplate')}}"/>
            </div>
        </div>
    </t>

    <!--
        Slide Category Selection template
    -->
    <t t-name="website.slide.upload.modal.select">
        <div class="row p-1 mt-4 mb-2">
            <div t-foreach="widget.slide_category_data" t-as="slide_category" class="col-6 col-md-4">
                <t t-set="category_data" t-value="widget.slide_category_data[slide_category]"/>

                <a href="#" t-att-data-slide-category="slide_category"
                    class="content-type d-flex flex-column align-items-center mb-4 o_wslides_select_category btn rounded border text-600 p-3">
                    <i t-attf-class="fa #{category_data['icon']} mb-2 fa-3x"/>
                    <t t-esc="category_data['label']"/>
                </a>
            </div>
        </div>
        <t t-if="widget.modulesToInstall">
            <t t-foreach="widget.modulesToInstall" t-as="module_info">
                <a class="o_wslides_js_upload_install_button w-100 text-center mb-4 btn rounded border text-600 p-3"
                    href="#" t-att-title="module_info['name']"
                    t-att-data-module-id="module_info['id']">
                    <i class="fa fa-trophy"></i> <t t-out="module_info['motivational']"/><span class="text-primary"> Install the <t t-out="module_info['name']"/> app.</span>
                </a>
            </t>
        </t>
    </t>

    <!--
        Uploading template
    -->
    <t t-name="website.slide.upload.modal.uploading">
        <div class="text-center" role="status">
            <div class="fa-3x">
                <i class="fa fa-circle-o-notch fa-spin"></i>
            </div>
            <h4>Uploading document ...</h4>
        </div>
    </t>

    <!--
        Import module template
    -->
    <t t-name="website.slide.upload.modal.import">
        <p id="o_wslides_install_module_text"/>
    </t>

    <!--
        Slide Category common form part template
    -->
    <t t-name="website.slide.upload.modal.common">
        <div class="mb-3">
            <label for="name" class="col-form-label">Title</label>
            <input id="name" name="name" placeholder="Title" class="form-control" required="required" autocomplete="off"/>
        </div>
        <div t-if="!widget.defaultCategoryID" class="mb-3">
            <label for="category_id" class="col-form-label">Section</label>
            <input class="form-control" id="category_id"/>
        </div>
        <div class="mb-3">
            <label for="tag_ids" class="col-form-label">Tags</label>
            <input id="tag_ids" name="tag_ids" type="hidden"/>
        </div>
        <div class="mb-3">
            <label for="duration" class="col-form-label">Estimated Completion Time</label>
            <div class="input-group">
                <input type="number" id="duration" min="0" name="duration" placeholder='e.g. "15"' class="form-control" autocomplete="off"/>
                <span class="input-group-text">Minutes</span>
            </div>
        </div>
    </t>

    <!--
        Slide Category templates
    -->
    <t t-name="website.slide.upload.modal.document">
        <div>
            <form class="clearfix">
                <div class="row">
                    <div id="o_wslides_js_slide_upload_left_column" class="col-md-6">
                        <div class="mb-3">
                            <label for="source_type" class="col-form-label">Document Source</label><br/>
                            <div class="form-check ms-2">
                                <input class="form-check-input" type="radio" name="source_type" id="source_type_local_file" data-value="local_file" checked="checked"/>
                                <label class="form-check-label fw-normal" for="source_type_local_file">
                                    Upload from Device
                                </label>
                            </div>
                            <div class="form-check ms-2">
                                <input class="form-check-input" type="radio" name="source_type" id="source_type_external" data-value="external"/>
                                <label class="form-check-label fw-normal" for="source_type_external">
                                    Retrieve from Google Drive
                                </label>
                            </div>
                        </div>
                        <div class="mb-3 o_wslides_js_slide_upload_local_file">
                            <label for="upload" class="col-form-label">Choose a PDF</label>
                            <input id="upload" name="file" class="form-control h-100" accept="application/pdf" type="file" required="required"/>
                        </div>
                        <div class="mb-3 o_wslides_js_slide_upload_external d-none">
                            <label for="document_google_url" class="col-form-label">Document Link</label>
                            <input id="document_google_url" name="document_google_url" class="form-control h-100" autocomplete="off"
                                placeholder='e.g "https://drive.google.com/file/..."'/>
                        </div>
                        <canvas id="data_canvas" class="d-none"></canvas>
                        <t t-call="website.slide.upload.modal.common"/>
                    </div>
                    <div id="o_wslides_js_slide_upload_preview_column" class="col-md-6">
                        <div class="img-thumbnail h-100">
                            <div class="o_slide_tutorial p-3">
                                <div class="h5">How do I add new content?</div>
                                <div>
                                    <span>You can either upload a file from your computer or insert a Google Drive link.</span><br/>
                                </div>
                                <div class="o_wslides_js_slide_upload_local_file">
                                    <div class="h5 mt-3">What types of documents do we support?</div>
                                    <div>
                                        <span>When using local files, we only support PDF files.</span><br/>
                                        <span>If you want to use other types of files, you may want to use an external source (Google Drive) instead.</span>
                                    </div>
                                    <div class="h5 mt-3">How to upload your PowerPoint Presentations or Word Documents?</div>
                                    <div>
                                        <span>Save your presentations or documents as PDF files and upload them.</span>
                                    </div>
                                </div>
                                <div class="o_wslides_js_slide_upload_external d-none">
                                    <div class="h5 mt-3">What types of documents do we support?</div>
                                    <div>
                                        Through Google Drive, we support most common types of documents.
                                        Including regular documents (Google Doc, .docx), Sheets (Google Sheet, .xlsx), PowerPoints, ...
                                    </div>
                                    <div class="h5 mt-3">How to use Google Drive?</div>
                                    <div>
                                        <span>First, upload the file on your Google Drive account.</span><br/>
                                        <span>Then, go into the file permissions and set it as "Anyone with the link".</span><br/>
                                        <span>The Google Drive link to use here can be obtained by clicking the "Share" button in the Google interface.</span><br/>
                                        <span>It should look similar to
                                        <span class="fst-italic">https://drive.google.com/file/d/ABC/view?usp=sharing</span></span>
                                    </div>
                                </div>
                            </div>
                            <div class="o_slide_preview d-none h-100 flex-column justify-content-center">
                                <img referrerPolicy="no-referrer" src="/website_slides/static/src/img/document.png" id="slide-image" title="Content Preview" alt="Content Preview" class="img-fluid"/>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </t>

    <t t-name="website.slide.upload.modal.infographic">
        <div>
            <form class="clearfix">
                <div class="row">
                    <div id="o_wslides_js_slide_upload_left_column" class="col-md-6">
                        <div class="mb-3">
                            <label for="source_type" class="col-form-label">Image Source</label><br/>
                            <div class="form-check ms-2">
                                <input class="form-check-input" type="radio" name="source_type" id="source_type_local_file" data-value="local_file" checked="checked"/>
                                <label class="form-check-label fw-normal" for="source_type_local_file">
                                    Upload from Device
                                </label>
                            </div>
                            <div class="form-check ms-2">
                                <input class="form-check-input" type="radio" name="source_type" id="source_type_external" data-value="external"/>
                                <label class="form-check-label fw-normal" for="source_type_external">
                                    Retrieve from Google Drive
                                </label>
                            </div>
                        </div>
                        <div class="mb-3 o_wslides_js_slide_upload_local_file">
                            <label for="upload" class="col-form-label">Choose an Image</label>
                            <input id="upload" name="file" class="form-control h-100" accept="image/*" type="file" required="required"/>
                        </div>
                        <div class="mb-3 o_wslides_js_slide_upload_external d-none">
                            <label for="image_google_url" class="col-form-label">Image Link</label>
                            <input id="image_google_url" name="image_google_url" class="form-control h-100" autocomplete="off"
                                placeholder='e.g "https://drive.google.com/file/..."'/>
                        </div>
                        <canvas id="data_canvas" class="d-none"></canvas>
                        <t t-call="website.slide.upload.modal.common"/>
                    </div>
                    <div id="o_wslides_js_slide_upload_preview_column" class="col-md-6">
                        <div class="img-thumbnail h-100">
                            <div class="o_slide_tutorial p-3">
                                <div class="h5">How do I add new content?</div>
                                <div>
                                    <span>You can either upload a file from your computer or insert a Google Drive link.</span><br/>
                                    <span>The Google Drive link can be obtained by using the 'share' button in the Google interface.</span><br/>
                                    <span>It should look similar to
                                    <span class="fst-italic">https://drive.google.com/file/d/ABC/view?usp=sharing</span></span>
                                </div>
                            </div>
                            <div class="o_slide_preview d-none h-100 flex-column justify-content-center">
                                <img referrerPolicy="no-referrer" src="/website_slides/static/src/img/document.png" id="slide-image" title="Content Preview" alt="Content Preview" class="img-fluid"/>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </t>

    <t t-name="website.slide.upload.modal.article">
        <div>
            <form class="clearfix">
                <div class="row">
                    <div id="o_wslides_js_slide_upload_left_column" class="col-md-6">
                        <canvas id="data_canvas" class="d-none"></canvas>
                        <t t-call="website.slide.upload.modal.common"/>
                    </div>
                    <div id="o_wslides_js_slide_upload_preview_column" class="col-md-6">
                        <div class="img-thumbnail h-100">
                            <div class="o_slide_tutorial p-3">
                                <div class="h5">How to create a Lesson as an Article?</div>
                                <div>First, create your lesson, then edit it with the website builder. You'll be able to drop building blocks on your page and edit them.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </t>

    <t t-name="website.slide.upload.modal.video">
        <div>
            <form class="clearfix">
                <div class="row">
                    <div id="o_wslides_js_slide_upload_left_column" class="col-md-6">
                        <div class="mb-3">
                            <label for="video_url" class="col-form-label">Video Link</label>
                            <input id="video_url" name="video_url" class="form-control" autocomplete="off"
                                placeholder='e.g "https://www.youtube.com/watch?v=ebBez6bcSEc"' required="required"/>
                        </div>
                        <canvas id="data_canvas" class="d-none"></canvas>
                        <t t-call="website.slide.upload.modal.common"/>
                    </div>
                    <div id="o_wslides_js_slide_upload_preview_column" class="col-md-6">
                        <div class="img-thumbnail h-100">
                            <div class="o_slide_tutorial p-3">
                                <div class="h5">How to upload your videos ?</div>
                                <div class="h6">On YouTube</div>
                                <div>First, upload your videos on YouTube and mark them as <strong>unlisted</strong>. This way, they will be secured.</div>
                                <div>What does <strong>unlisted</strong> means? The YouTube "unlisted" means it is a video which can be viewed only by the users with the link to it. Your video will never come up in the search results nor on your channel.</div>
                                <div><a href="https://support.google.com/youtube/answer/157177" target="_blank" >Change video privacy settings</a></div>
                                <br/>
                                <div class="h6">On Vimeo</div>
                                <div>
                                    <span>First, upload your videos on Vimeo and mark them as <strong>Unlisted (paid account)</strong>. This way, they will be secured.</span><br/>
                                    <span>What does <strong>Unlisted</strong> mean? The Vimeo "Unlisted" privacy setting means it is a video which can be viewed only by the users with the link to it.
                                    Your video will never come up in the search results nor on your channel.</span><br/>
                                    <span><a href="https://vimeo.zendesk.com/hc/en-us/articles/*********-Changing-the-privacy-settings-of-your-videos" target="_blank" >Change video privacy settings</a></span><br/><br/>
                                    <span>The video link to input here can be obtained by using the 'Share link' button in the Vimeo interface.</span><br/>
                                    <span>It should look similar to</span>
                                    <span class="fst-italic">https://vimeo.com/*********/30da9ff3d8</span>
                                    <span>for 'Private' videos and similar to</span>
                                    <span class="fst-italic">https://vimeo.com/558907555</span>
                                    <span>for public ones.</span>
                                </div>
                                <br/>
                                <div class="h6">On Google Drive</div>
                                <div>
                                    <span>The Google Drive link can be obtained by using the 'share' button in the Google interface.</span><br/>
                                    <span>It should look similar to
                                    <span class="fst-italic">https://drive.google.com/file/d/ABC/view?usp=sharing</span></span>
                                </div>
                            </div>
                            <div class="o_slide_preview d-none h-100 flex-column justify-content-center">
                                <img referrerPolicy="no-referrer" src="/website_slides/static/src/img/document.png" id="slide-image" title="Content Preview" alt="Content Preview" class="img-fluid"/>
                                <div id="slide-video-title" class="d-none mt-1"/>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </t>

    <t t-name="website.slide.upload.quiz">
        <div>
            <form class="clearfix">
                <div class="row">
                    <div id="o_wslides_js_slide_upload_left_column" class="col-md-6">
                        <canvas id="data_canvas" class="d-none"></canvas>
                        <t t-call="website.slide.upload.modal.common"/>
                    </div>
                    <div id="o_wslides_js_slide_upload_preview_column" class="col-md-6">
                        <div class="img-thumbnail h-100">
                            <div class="o_slide_tutorial p-3">
                                <div class="h5">Test your students with small Quizzes</div>
                                <div>With Quizzes you can keep your students focused and motivated by answering some questions and gaining some karma points</div>
                                <img src="/website_slides/static/src/img/onboarding-quiz.png" title="Quiz Demo Data" class="img-fluid"/>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </t>

    <!-- Misc -->
    <t t-name="website.slide.upload.modal.loading">
        <div class="o_wslides_slide_upload_loading position-absolute h-100 w-100 text-center d-flex flex-column justify-content-center">
            <h2 class="text-white">
                <span class="fa fa-spinner fa-pulse"></span>
                <span>Loading content...</span>
            </h2>
        </div>
    </t>
</templates>
