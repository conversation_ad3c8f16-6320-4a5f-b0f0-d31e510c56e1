# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_sale_expense
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> Col<PERSON>t <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:44+0000\n"
"PO-Revision-Date: 2022-12-16 09:48+0000\n"
"Last-Translator: Manon Rondou, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "# Expenses"
msgstr "# Dépenses"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Category"
msgstr "Catégorie"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Employee"
msgstr "Employé"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expense"
msgstr "Dépense"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses"
msgstr "Dépenses"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis"
msgstr "Analyse des dépenses"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis by Customer to Reinvoice"
msgstr "Analyse des dépenses par client à refacturer"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - Expenses"
msgstr "KPI - Dépenses"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To reimburse"
msgstr "KPI - À rembourser"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To report"
msgstr "KPI - À signaler"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To validate"
msgstr "KPI - À valider"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Order"
msgstr "Commande"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Période"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Produit"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To reimburse"
msgstr "À rembourser"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To report"
msgstr "À signaler"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To validate"
msgstr "À valider"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Categories"
msgstr "Meilleures catégories"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Employees"
msgstr "Meilleurs employés"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Expenses"
msgstr "Meilleures dépenses"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Reinvoiced Orders"
msgstr "Melleures commandes refacturées"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Total"
msgstr "Total"
