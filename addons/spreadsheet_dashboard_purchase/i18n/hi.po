# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_purchase
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-25 10:43+0000\n"
"PO-Revision-Date: 2022-09-29 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid " days"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Amount"
msgstr "रकम"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Buyer"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Country"
msgstr "देश"

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Current"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Days to receive"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "KPI"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Lines"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Order"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Ordered"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Orders"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Period"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Previous"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Partner Country"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Purchase Representative"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Purchase Analysis by Vendor"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Purchase Orders by Untaxed Amount"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Purchased"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Qty ordered"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Quantity ordered"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Source"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Sourcing by Country"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Top Buyers"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Top Orders"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Top Vendors"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Top Vendors by Amount"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Untaxed total"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "Vendor"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "last period"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "since last period"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "stats - current"
msgstr ""

#. module: spreadsheet_dashboard_purchase
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_purchase/data/files/vendors_dashboard.json:0
#, python-format
msgid "stats - previous"
msgstr ""
