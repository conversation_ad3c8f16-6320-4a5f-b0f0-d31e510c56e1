# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_skills
# 
# Translators:
# j<PERSON><PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-27 15:13+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment_skills
#: model:ir.model,name:hr_recruitment_skills.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__applicant_id
msgid "Applicant"
msgstr "Candidat"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__create_date
msgid "Created on"
msgstr "Creat el"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant__is_interviewer
msgid "Is Interviewer"
msgstr "És intervisor"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__level_progress
msgid "Progress"
msgstr "Progrés"

#. module: hr_recruitment_skills
#: model:ir.model.fields,help:hr_recruitment_skills.field_hr_applicant_skill__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr "Progrés des del coneixement zero (0%) al domini complet (100%)."

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant__skill_ids
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__skill_id
msgid "Skill"
msgstr "Habilitat"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__skill_level_id
msgid "Skill Level"
msgstr "Nivell d'habilitat"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant_skill__skill_type_id
msgid "Skill Type"
msgstr "Tipus d'habilitat"

#. module: hr_recruitment_skills
#: model:ir.model,name:hr_recruitment_skills.model_hr_applicant_skill
msgid "Skill level for an applicant"
msgstr "Nivell d'habilitat per a un sol·licitant"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant__applicant_skill_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_applicant_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_applicant_view_search_bis
msgid "Skills"
msgstr "Habilitats"

#. module: hr_recruitment_skills
#: code:addons/hr_recruitment_skills/models/hr_applicant_skill.py:0
#, python-format
msgid "The skill %(name)s and skill type %(type)s doesn't match"
msgstr "L'habilitat %(name)s i tipus d'habilitat %(type)s no coincideix"

#. module: hr_recruitment_skills
#: code:addons/hr_recruitment_skills/models/hr_applicant_skill.py:0
#, python-format
msgid "The skill level %(level)s is not valid for skill type: %(type)s"
msgstr ""
"El nivell d'habilitat %(level)s no és vàlid per al tipus "
"d'habilitat:%(type)s"

#. module: hr_recruitment_skills
#: model:ir.model.constraint,message:hr_recruitment_skills.constraint_hr_applicant_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr "No es permeten dos nivells per a la mateixa habilitat"
