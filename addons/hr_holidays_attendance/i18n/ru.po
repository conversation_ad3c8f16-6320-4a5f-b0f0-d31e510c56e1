# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_attendance
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>, 2023
# <PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-03 09:16+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_type.py:0
#, python-format
msgid "%s hours available"
msgstr "%s часов в наличии"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_attendance
msgid "Attendance"
msgstr "Присутствие"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Available"
msgstr "Доступно"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__hr_attendance_overtime
msgid "Count Extra Hours"
msgstr "Учет дополнительных часов"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Days"
msgstr "Дни"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.res_users_view_form
msgid "Deduct Extra Hours"
msgstr "Вычесть дополнительные часы"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Discard"
msgstr "Отменить"

#. module: hr_holidays_attendance
#: model:hr.leave.type,name:hr_holidays_attendance.holiday_status_extra_hours
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_id
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_id
msgid "Extra Hours"
msgstr "Дополнительные часы"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_attendance_holidays_hr_leave_allocation_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_view_form
msgid "Extra Hours Available"
msgstr "Доступны дополнительные часы"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Hours"
msgstr "Часы"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__hr_attendance_overtime
msgid "Hr Attendance Overtime"
msgstr "Час Посещаемость Сверхурочные"

#. module: hr_holidays_attendance
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_action
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_manager_action
msgid "New Allocation Request"
msgstr "Новый запрос на отпуск"

#. module: hr_holidays_attendance
#: model:ir.model.fields,help:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
msgid ""
"Once a time off of this type is approved, extra hours in attendances will be"
" deducted."
msgstr ""
"После одобрения такого отгула дополнительные часы будут вычтены из "
"посещаемости."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Only an Officer or Administrator is allowed to edit the allocation duration "
"in this status."
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_deductible
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_deductible
msgid "Overtime Deductible"
msgstr "Вычет за сверхурочную работу"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_res_users__request_overtime
msgid "Request Overtime"
msgstr "Запрос на сверхурочную работу"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Save"
msgstr "Сохранить"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough extra hours to extend this allocation."
msgstr ""
"У сотрудника нет достаточного количества дополнительных часов, чтобы "
"продлить это распределение."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "The employee does not have enough extra hours to extend this leave."
msgstr ""
"У сотрудника нет достаточного количества дополнительных часов, чтобы "
"продлить этот отпуск."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough extra hours to request this allocation."
msgstr ""

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "The employee does not have enough extra hours to request this leave."
msgstr ""
"У сотрудника нет достаточного количества дополнительных часов, чтобы "
"запросить этот отпуск."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough overtime hours to request this leave."
msgstr ""
"У сотрудника недостаточно сверхурочных часов, чтобы запросить этот отпуск."

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave
msgid "Time Off"
msgstr "Отсутствие"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "Запрос на отгул"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_type
msgid "Time Off Type"
msgstr "Тип отгула"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__employee_overtime
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__employee_overtime
msgid "Total Overtime"
msgstr "Итого сверхурочные"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_users
msgid "User"
msgstr "Пользователь"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "You do not have enough extra hours to request this leave"
msgstr ""
"У вас нет достаточного количества дополнительных часов, чтобы попросить этот"
" отпуск"
