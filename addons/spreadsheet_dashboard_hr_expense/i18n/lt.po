# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_hr_expense
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:44+0000\n"
"PO-Revision-Date: 2022-09-29 09:44+0000\n"
"Last-Translator: <PERSON>as Versada <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "# Expenses"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Category"
msgstr "Kategorija"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Employee"
msgstr "Darbuotojas"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expense"
msgstr "Išlaida"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses"
msgstr "Išlaidos"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis"
msgstr "Išlaidų analizė"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - Expenses"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To reimburse"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To report"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To validate"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Laikotarpis"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Produktas"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To reimburse"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To report"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To validate"
msgstr "Reikia patvirtinti"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Categories"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Employees"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Expenses"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Total"
msgstr "Suma"
