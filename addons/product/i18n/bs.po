# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-08-24 09:23+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: product
#: selection:product.pricelist.item,applied_on:0
msgid " Product Category"
msgstr "Kategorija proizvoda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_count
msgid "# Product Variants"
msgstr "# "

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_count
msgid "# Products"
msgstr "# Proizvoda"

#. module: product
#: code:addons/product/models/product_pricelist.py:462
#, python-format
msgid "%s %% discount"
msgstr "%s %% popust"

#. module: product
#: code:addons/product/models/product_pricelist.py:464
#, python-format
msgid "%s %% discount and %s surcharge"
msgstr "%s %% popusta i %s doplate"

#. module: product
#: code:addons/product/models/product_template.py:357
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "1 year"
msgstr ""

#. module: product
#: model:product.product,description_sale:product.product_product_4
#: model:product.product,description_sale:product.product_product_4b
#: model:product.product,description_sale:product.product_product_4c
#: model:product.product,description_sale:product.product_product_4d
#: model:product.template,description_sale:product.product_product_4b_product_template
msgid "160x80cm, with large legs."
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_6
msgid "2 year"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<span attrs=\"{'invisible':[('base', '!=', 'list_price')]}\">Public Price  -  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', 'standard_price')]}\">Cost  -  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', 'pricelist')]}\">Other Pricelist  -  </span>"
msgstr ""
"<span attrs=\"{'invisible':[('base', '!=', 'list_price')]}\">Javna cijena  -  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', 'standard_price')]}\">Nabavna cijena  -  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', 'pricelist')]}\">Drugi cijenovnik -  </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "<span class=\"o_form_label\">New Price =</span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "<span class=\"o_stat_text\"> Products</span>"
msgstr "<span class=\"o_stat_text\"> Proizvodi</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>kg</span>"
msgstr "<span>kg</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>m³</span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Currency</strong>:<br/>"
msgstr "<strong>Valuta</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Description</strong>"
msgstr "<strong>Opis</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Price List Name</strong>:<br/>"
msgstr "<strong>Naziv cijenovnika</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_simple_label
msgid "<strong>Price:</strong>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Print date</strong>:<br/>"
msgstr "<strong>Datum štampanja</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "<strong>Qty: </strong>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                        will delete and recreate existing variants and lead\n"
"                        to the loss of their possible customizations."
msgstr ""

#. module: product
#: sql_constraint:product.product:0
msgid "A barcode can only be assigned to one product !"
msgstr "Bar kod može biti dodjeljen samo jednom proizvodu !"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__description_sale
#: model:ir.model.fields,help:product.field_product_template__description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price is a set of sales prices or rules to compute the price of sales order lines based on products, product categories, dates and ordered quantities.\n"
"                This is the perfect tool to handle several pricings, seasonal discounts, etc."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__type
#: model:ir.model.fields,help:product.field_product_template__type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_25
#: model:product.template,name:product.product_product_25_product_template
msgid "Acoustic Bloc Screens"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__active
#: model:ir.model.fields,field_description:product.field_product_product__active
#: model:ir.model.fields,field_description:product.field_product_template__active
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Active"
msgstr "Aktivan"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_ids
#: model:ir.model.fields,field_description:product.field_product_template__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_state
#: model:ir.model.fields,field_description:product.field_product_template__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: product
#: model:product.category,name:product.product_category_all
msgid "All"
msgstr "Sve"

#. module: product
#: code:addons/product/models/product_pricelist.py:457
#, python-format
msgid "All Products"
msgstr "Svi proizvodi"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "All general settings about this product are managed on"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings__group_sale_pricelist
msgid ""
"Allows to manage different prices based on rules per category of customers.\n"
"                Example: 10% for retailers, promotion of 5 EUR on this product, etc."
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
#: model:product.template.attribute.value,name:product.product_template_attribute_value_1
#: model:product.template.attribute.value,name:product.product_template_attribute_value_2
msgid "Aluminium"
msgstr ""

#. module: product
#: selection:product.attribute,create_variant:0
msgid "Always"
msgstr "Uvijek"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Applicable On"
msgstr "Primjenjivo na"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__applied_on
msgid "Apply On"
msgstr "Primjeni na"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Archived"
msgstr "Arhivirano"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__sequence
msgid "Assigns the priority to the list of product vendor."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_id
msgid "Attribute"
msgstr "Atribut"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Attribute Name"
msgstr "Naziv atributa"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__price_extra
msgid "Attribute Price Extra"
msgstr "Ekstra cijena atributa"

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_attribute_value_id
msgid "Attribute Value"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.variants_action
#: model:ir.model.fields,field_description:product.field_product_product__attribute_value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Attribute Values"
msgstr "Vrijednost atributa"

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Attributes"
msgstr "Atributi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__barcode
#: model:ir.model.fields,field_description:product.field_product_product__barcode
#: model:ir.model.fields,field_description:product.field_product_template__barcode
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
#: model_terms:ir.ui.view,arch_db:product.report_simple_barcode
#: model_terms:ir.ui.view,arch_db:product.report_simple_label
msgid "Barcode"
msgstr "Barkod"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__barcode
msgid "Barcode used for packaging identification."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__base
msgid ""
"Base price for computation.\n"
"Public Price: The base price will be the Sale/public Price.\n"
"Cost Price : The base price will be the cost price.\n"
"Other Pricelist : Computation of the base price based on another Pricelist."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base
msgid "Based on"
msgstr "Bazirano na"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image
msgid "Big-sized image"
msgstr "Velika slika"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
msgid "Black"
msgstr "Crna"

#. module: product
#: model:product.product,name:product.product_product_10
#: model:product.template,name:product.product_product_10_product_template
msgid "Cabinet with Doors"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Calculate Product Price per Unit Based on Pricelist Version."
msgstr ""
"Izračunaj cijenu proizvoda po jedinici bazirano na verziji cjenovnika."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__purchase_ok
#: model:ir.model.fields,field_description:product.field_product_template__purchase_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Purchased"
msgstr "Može se kupovati"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__rental
#: model:ir.model.fields,field_description:product.field_product_template__rental
msgid "Can be Rent"
msgstr "Može se iznajmiti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__sale_ok
#: model:ir.model.fields,field_description:product.field_product_template__sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Sold"
msgstr "Može se prodati"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Cancel"
msgstr "Otkaži"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category name"
msgstr "Naziv kategorije"

#. module: product
#: code:addons/product/models/product_pricelist.py:451
#, python-format
msgid "Category: %s"
msgstr "Kategorija: %s"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__create_variant
msgid "Check this if you want to create multiple variants for this attribute."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__child_id
msgid "Child Categories"
msgstr "Podkategorije"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Choose the unit to measure weight"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "Šifre"

#. module: product
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr "Boja"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__color
#: model:ir.model.fields,field_description:product.field_product_template__color
msgid "Color Index"
msgstr "Indeks boje"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Common Product Catalog"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__company_id
#: model:ir.model.fields,field_description:product.field_product_product__company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__company_id
#: model:ir.model.fields,field_description:product.field_product_template__company_id
msgid "Company"
msgstr "Kompanija"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__complete_name
msgid "Complete Name"
msgstr "Puni naziv"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__compute_price
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Compute Price"
msgstr "Izračunaj cijenu"

#. module: product
#: model:product.product,name:product.product_product_11
#: model:product.product,name:product.product_product_11b
#: model:product.template,name:product.product_product_11b_product_template
msgid "Conference Chair"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Configure Variants"
msgstr ""

#. module: product
#: model:product.category,name:product.product_category_consumable
#: selection:product.template,type:0
msgid "Consumable"
msgstr "Potrošni"

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__qty
msgid "Contained Quantity"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Contained quantity"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_13
#: model:product.template,name:product.product_product_13_product_template
msgid "Corner Desk Black"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_5
#: model:product.template,name:product.product_product_5_product_template
msgid "Corner Desk Right Sit"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history__cost
#: model:ir.model.fields,field_description:product.field_product_product__standard_price
#: model:ir.model.fields,field_description:product.field_product_template__standard_price
#: selection:product.pricelist.item,base:0
msgid "Cost"
msgstr "Cijena (Koštanje)"

#. module: product
#: model:ir.model.fields,help:product.field_product_template__standard_price
msgid ""
"Cost used for stock valuation in standard price and as a first price to set "
"in average/FIFO."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__standard_price
msgid ""
"Cost used for stock valuation in standard price and as a first price to set "
"in average/fifo. Also used as a base price for pricelists. Expressed in the "
"default unit of measure of the product."
msgstr ""

#. module: product
#: model:ir.model,name:product.model_res_country_group
msgid "Country Group"
msgstr "Grupa zemalja"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__country_group_ids
msgid "Country Groups"
msgstr "Grupe zemalja"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_variant
msgid "Create Variants"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Create a new pricelist"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_all
msgid "Create a new product"
msgstr "Kreirajte novi proizvod"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Create a new product variant"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_category__create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__create_uid
#: model:ir.model.fields,field_description:product.field_product_price_history__create_uid
#: model:ir.model.fields,field_description:product.field_product_price_list__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_uid
#: model:ir.model.fields,field_description:product.field_product_product__create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_uid
#: model:ir.model.fields,field_description:product.field_product_template__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_date
#: model:ir.model.fields,field_description:product.field_product_category__create_date
#: model:ir.model.fields,field_description:product.field_product_packaging__create_date
#: model:ir.model.fields,field_description:product.field_product_price_history__create_date
#: model:ir.model.fields,field_description:product.field_product_price_list__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_date
#: model:ir.model.fields,field_description:product.field_product_product__create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_date
#: model:ir.model.fields,field_description:product.field_product_template__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__currency_id
#: model:ir.model.fields,field_description:product.field_product_product__currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__currency_id
#: model:ir.model.fields,field_description:product.field_product_template__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__partner_ref
msgid "Customer Ref"
msgstr "Ref. kupca"

#. module: product
#: model:product.product,name:product.product_product_4
#: model:product.product,name:product.product_product_4b
#: model:product.product,name:product.product_product_4c
#: model:product.product,name:product.product_product_4d
#: model:product.template,name:product.product_product_4b_product_template
msgid "Customizable Desk"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history__datetime
msgid "Date"
msgstr "Datum"

#. module: product
#: model:product.product,uom_name:product.expense_hotel
#: model:product.template,uom_name:product.expense_hotel_product_template
msgid "Day(s)"
msgstr "Dan(i)"

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "Decimal Precision"
msgstr "Decimalna preciznost"

#. module: product
#: code:addons/product/models/res_company.py:59
#, python-format
msgid "Default %(currency)s pricelist for %(company)s"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,help:product.field_product_product__uom_id
#: model:ir.model.fields,help:product.field_product_template__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__uom_po_id
#: model:ir.model.fields,help:product.field_product_template__uom_po_id
msgid ""
"Default unit of measure used for purchase orders. It must be in the same "
"category as the default unit of measure."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__seller_ids
#: model:ir.model.fields,help:product.field_product_template__seller_ids
msgid "Define vendor pricelists."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__delay
msgid "Delivery Lead Time"
msgstr "Vrijeme trajanja isporuke"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description
#: model:ir.model.fields,field_description:product.field_product_template__description
msgid "Description"
msgstr "Opis"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Description for Customers"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_3
#: model:product.template,name:product.product_product_3_product_template
msgid "Desk Combination"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_22
#: model:product.template,name:product.product_product_22_product_template
msgid "Desk Stand with Screen"
msgstr ""

#. module: product
#: model:product.product,description_sale:product.product_product_3
#: model:product.template,description_sale:product.product_product_3_product_template
msgid "Desk combination, black-brown: chair + desk + drawer."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__sequence
#: model:ir.model.fields,help:product.field_product_attribute_value__sequence
#: model:ir.model.fields,help:product.field_product_template_attribute_value__sequence
msgid "Determine the display order"
msgstr "Odredi redosljed prikaza"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_product_category__display_name
#: model:ir.model.fields,field_description:product.field_product_packaging__display_name
#: model:ir.model.fields,field_description:product.field_product_price_history__display_name
#: model:ir.model.fields,field_description:product.field_product_price_list__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__display_name
#: model:ir.model.fields,field_description:product.field_product_product__display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__display_name
#: model:ir.model.fields,field_description:product.field_product_template__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: product
#: model:product.product,name:product.product_product_27
#: model:product.template,name:product.product_product_27_product_template
msgid "Drawer"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_16
#: model:product.template,name:product.product_product_16_product_template
msgid "Drawer Black"
msgstr ""

#. module: product
#: model:product.product,description:product.product_product_27
#: model:product.template,description:product.product_product_27_product_template
msgid "Drawer with two routing possiblities."
msgstr ""

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Duration"
msgstr "Trajanje"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_03
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid ""
"Dvorak keyboard\n"
"left-handed mouse"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_end
msgid "End Date"
msgstr "Datum Završetka"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_end
msgid "End date for this vendor price"
msgstr "Datum kraja cijene dobavljača"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_end
msgid "Ending valid for the pricelist item validation"
msgstr ""

#. module: product
#: code:addons/product/models/product.py:300
#, python-format
msgid ""
"Error! It is not allowed to choose more than one value for a given "
"attribute."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__exclude_for
msgid "Exclude for"
msgstr ""

#. module: product
#: model:product.category,name:product.cat_expense
msgid "Expenses"
msgstr "Troškovi"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__name
#: model:ir.model.fields,help:product.field_product_pricelist_item__price
msgid "Explicit rule name for this pricelist line."
msgstr "Eksplicitno ime pravila za ovu stavku cjenovnika."

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Fix Price"
msgstr "Fiksna cijena"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__fixed_price
msgid "Fixed Price"
msgstr "Fiksna cijena"

#. module: product
#: model:product.product,name:product.product_product_20
#: model:product.template,name:product.product_product_20_product_template
msgid "Flipover"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_channel_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Formula"
msgstr "Formula"

#. module: product
#: model:product.product,name:product.consu_delivery_03
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Four Person Desk"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Future Activities"
msgstr "Buduće aktivnosti"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "Opšte informacije"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__packaging_ids
#: model:ir.model.fields,help:product.field_product_template__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__sequence
#: model:ir.model.fields,help:product.field_product_template__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr ""

#. module: product
#: selection:product.pricelist.item,applied_on:0
msgid "Global"
msgstr "Opšte"

#. module: product
#: code:addons/product/models/product_template.py:32
#, python-format
msgid "Go to Internal Categories"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Group By"
msgstr "Grupiši po"

#. module: product
#: model:product.product,name:product.expense_hotel
#: model:product.template,name:product.expense_hotel_product_template
msgid "Hotel Accommodation"
msgstr "Smještaj u hotelu"

#. module: product
#: model:product.product,uom_name:product.product_product_1
#: model:product.product,uom_name:product.product_product_2
#: model:product.template,uom_name:product.product_product_1_product_template
#: model:product.template,uom_name:product.product_product_2_product_template
msgid "Hour(s)"
msgstr "Sat(i)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__id
#: model:ir.model.fields,field_description:product.field_product_attribute_value__id
#: model:ir.model.fields,field_description:product.field_product_category__id
#: model:ir.model.fields,field_description:product.field_product_packaging__id
#: model:ir.model.fields,field_description:product.field_product_price_history__id
#: model:ir.model.fields,field_description:product.field_product_price_list__id
#: model:ir.model.fields,field_description:product.field_product_pricelist__id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__id
#: model:ir.model.fields,field_description:product.field_product_product__id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__id
#: model:ir.model.fields,field_description:product.field_product_template__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__id
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist__id
msgid "ID"
msgstr "ID"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_unread
#: model:ir.model.fields,help:product.field_product_template__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_needaction
#: model:ir.model.fields,help:product.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_has_error
#: model:ir.model.fields,help:product.field_product_template__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_id
msgid ""
"If not set, the vendor price will apply to all variants of this product."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr "Ako je odznačeno, možete sakriti cjenik bez da ga brišete."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__active
#: model:ir.model.fields,help:product.field_product_template__active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr "Ako je označeno omogućava sakrivanje proizvoda bez brisanja."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template__image
msgid "Image"
msgstr "Slika"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__image_medium
msgid ""
"Image of the product variant (Medium-sized image of product template if "
"false)."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__image_small
msgid ""
"Image of the product variant (Small-sized image of product template if "
"false)."
msgstr ""

#. module: product
#: code:addons/product/models/product_pricelist.py:348
#, python-format
msgid "Import Template for Pricelists"
msgstr ""

#. module: product
#: code:addons/product/models/product_template.py:573
#, python-format
msgid "Import Template for Products"
msgstr ""

#. module: product
#: code:addons/product/models/product.py:665
#, python-format
msgid "Import Template for Vendor Pricelists"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_24
#: model:product.template,name:product.product_product_24_product_template
msgid "Individual Workplace"
msgstr ""

#. module: product
#: model:product.category,name:product.product_category_2
msgid "Internal"
msgstr "Interni"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Internal Notes"
msgstr "Interne zabilješke"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__default_code
#: model:ir.model.fields,field_description:product.field_product_template__default_code
msgid "Internal Reference"
msgstr "Interna referenca"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__barcode
#: model:ir.model.fields,help:product.field_product_template__barcode
msgid "International Article Number used for product identification."
msgstr ""
"Internacionalni broj artikla koji se koristi za identifikaciju proizvoda."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "Skladište"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__is_product_variant
msgid "Is Product Variant"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template__is_product_variant
msgid "Is a product variant"
msgstr ""

#. module: product
#: selection:res.config.settings,product_weight_in_lbs:0
msgid "Kilogram"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_6
#: model:product.template,name:product.product_product_6_product_template
msgid "Large Cabinet"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_8
#: model:product.template,name:product.product_product_8_product_template
msgid "Large Desk"
msgstr ""

#. module: product
#: model:product.product,name:product.consu_delivery_02
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Large Meeting Table"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute____last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_value____last_update
#: model:ir.model.fields,field_description:product.field_product_category____last_update
#: model:ir.model.fields,field_description:product.field_product_packaging____last_update
#: model:ir.model.fields,field_description:product.field_product_price_history____last_update
#: model:ir.model.fields,field_description:product.field_product_price_list____last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist____last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist_item____last_update
#: model:ir.model.fields,field_description:product.field_product_product____last_update
#: model:ir.model.fields,field_description:product.field_product_supplierinfo____last_update
#: model:ir.model.fields,field_description:product.field_product_template____last_update
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion____last_update
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line____last_update
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value____last_update
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_category__write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__write_uid
#: model:ir.model.fields,field_description:product.field_product_price_history__write_uid
#: model:ir.model.fields,field_description:product.field_product_price_list__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_uid
#: model:ir.model.fields,field_description:product.field_product_product__write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_uid
#: model:ir.model.fields,field_description:product.field_product_template__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_date
#: model:ir.model.fields,field_description:product.field_product_category__write_date
#: model:ir.model.fields,field_description:product.field_product_packaging__write_date
#: model:ir.model.fields,field_description:product.field_product_price_history__write_date
#: model:ir.model.fields,field_description:product.field_product_price_list__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_date
#: model:ir.model.fields,field_description:product.field_product_product__write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_date
#: model:ir.model.fields,field_description:product.field_product_template__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Late Activities"
msgstr "Aktivnosti u kašnjenju"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Legs"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__attribute_line_ids
msgid "Lines"
msgstr "Retci"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Logistics"
msgstr "Logistika"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_main_attachment_id
#: model:ir.model.fields,field_description:product.field_product_template__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__exclude_for
msgid ""
"Make this attribute value not compatible with\n"
"        other values of the product or some attribute values of optional and accessory products."
msgstr ""

#. module: product
#: model:res.groups,name:product.group_pricelist_item
msgid "Manage Pricelist Items"
msgstr "Upravljaj stavkama cijenovnika"

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr "Upravljajte pakiranjem proizvoda"

#. module: product
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr "Upravljaj varijantama proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr "Max. marža"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_max_margin
msgid "Max. Price Margin"
msgstr "Maks. marža cijene"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_medium
#: model:ir.model.fields,field_description:product.field_product_template__image_medium
msgid "Medium-sized image"
msgstr "Slika srednje veličine"

#. module: product
#: model:ir.model.fields,help:product.field_product_template__image_medium
msgid ""
"Medium-sized image of the product. It is automatically resized as a "
"128x128px image, with aspect ratio preserved, only when the image exceeds "
"one of those sizes. Use this field in form views or some kanban views."
msgstr ""
"Srednje velika slika proizvoda. Automatski joj je promjenjena veličina na "
"128x128px, sa zadržanim omjerom, samo kada slika prelazi jednu od ovih "
"veličina. Koristite ovo polje u formama i kaban pogledima."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr "Min. marža"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_min_margin
msgid "Min. Price Margin"
msgstr "Min. marža cijene"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__min_quantity
msgid "Min. Quantity"
msgstr "Min. količina"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__min_qty
msgid "Minimal Quantity"
msgstr "Minimalna količina"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__name
#: model:ir.model.fields,field_description:product.field_product_product__name
#: model:ir.model.fields,field_description:product.field_product_template__name
msgid "Name"
msgstr "Naziv:"

#. module: product
#: selection:product.attribute,create_variant:0
msgid "Never"
msgstr "Nikada"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "New Price ="
msgstr "Nova cijena ="

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok za sljedeću aktivnost"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_summary
#: model:ir.model.fields,field_description:product.field_product_template__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sljedeće aktivnosti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_template__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_unread_counter
#: model:ir.model.fields,help:product.field_product_template__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: product
#: model:product.product,name:product.product_delivery_01
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Office Chair"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_12
#: model:product.template,name:product.product_product_12_product_template
msgid "Office Chair Black"
msgstr ""

#. module: product
#: model:product.product,name:product.product_order_01
#: model:product.template,name:product.product_order_01_product_template
msgid "Office Design Software"
msgstr ""

#. module: product
#: model:product.category,name:product.product_category_5
msgid "Office Furniture"
msgstr ""

#. module: product
#: model:product.product,name:product.product_delivery_02
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Office Lamp"
msgstr ""

#. module: product
#: selection:product.attribute,create_variant:0
msgid "Only when the product is added to a sales order"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Other Information"
msgstr "Ostali podaci"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base_pricelist_id
#: selection:product.pricelist.item,base:0
msgid "Other Pricelist"
msgstr "Drugi cjenovnici"

#. module: product
#: selection:product.product,activity_state:0
#: selection:product.template,activity_state:0
msgid "Overdue"
msgstr "Dospjele"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__name
msgid "Package Type"
msgstr "Tip paketa"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Packaging"
msgstr "Pakovanje"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_id
msgid "Parent Category"
msgstr "Izvorna kategorija"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_path
msgid "Parent Path"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_9
#: model:product.template,name:product.product_product_9_product_template
msgid "Pedal Bin"
msgstr ""

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Percentage (discount)"
msgstr "Procenat (popust)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__percent_price
msgid "Percentage Price"
msgstr "Procenat cijene"

#. module: product
#: selection:product.product,activity_state:0
#: selection:product.template,activity_state:0
msgid "Planned"
msgstr "Planiran"

#. module: product
#: selection:res.config.settings,product_weight_in_lbs:0
msgid "Pound"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price
#: model:ir.model.fields,field_description:product.field_product_product__price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price
#: model:ir.model.fields,field_description:product.field_product_template__price
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Price"
msgstr "Cijena"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Computation"
msgstr "Izračunavanje cijene"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_discount
msgid "Price Discount"
msgstr "Popust"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__price_extra
msgid ""
"Price Extra: Extra price for the variant with\n"
"        this attribute value on sale price. eg. 200 price extra, 1000 + 200 = 1200."
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.action_product_price_list
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Price List"
msgstr "Cjenovnik"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_round
msgid "Price Rounding"
msgstr "Zaokruživanje cijena"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_surcharge
msgid "Price Surcharge"
msgstr "Cijena doplate"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__list_price
#: model:ir.model.fields,help:product.field_product_template__list_price
#: model:ir.model.fields,help:product.field_product_template__lst_price
msgid "Price at which the product is sold to customers."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr "Cijena:"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__price_list
msgid "PriceList"
msgstr "Cjenovnik"

#. module: product
#: model:ir.actions.report,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__pricelist_id
#: model:ir.model.fields,field_description:product.field_product_product__pricelist_id
#: model:ir.model.fields,field_description:product.field_product_template__pricelist_id
#: model:ir.model.fields,field_description:product.field_res_partner__property_product_pricelist
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Pricelist"
msgstr "Cijenovnik"

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
#: model:ir.model.fields,field_description:product.field_product_product__pricelist_item_ids
msgid "Pricelist Item"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__applied_on
msgid "Pricelist Item applicable on selected option"
msgstr "Stavke cijenovnika primjenjive na odabrane opcije"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__item_ids
#: model:ir.model.fields,field_description:product.field_product_product__item_ids
#: model:ir.model.fields,field_description:product.field_product_template__item_ids
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Pricelist Items"
msgstr "Stavke cijenovnika"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__name
msgid "Pricelist Name"
msgstr "Naziv cjenovnika"

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.model.fields,field_description:product.field_res_country_group__pricelist_ids
msgid "Pricelists"
msgstr "Cjenovnik"

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Pricelists On Product"
msgstr "Cijenovnik proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr "Cjenovnicima se upravlja na"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "Cijene"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Print"
msgstr "Ispis"

#. module: product
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_packaging__product_id
#: model:ir.model.fields,field_description:product.field_product_price_history__product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_id
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_id
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_id
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: selection:product.pricelist.item,applied_on:0
msgid "Product"
msgstr "Proizvod"

#. module: product
#: model:ir.model,name:product.model_product_attribute
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_form
msgid "Product Attribute"
msgstr "Atribut proizvoda"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_value
msgid "Product Attribute Value"
msgstr "Vrijednost atributa proizvoda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_template_value_ids
msgid "Product Attribute Values"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Product Attribute and Values"
msgstr "Atributi i vrijednosti proizvoda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__attribute_line_ids
msgid "Product Attributes"
msgstr "Atributi proizvoda"

#. module: product
#: model:ir.actions.report,name:product.report_product_product_barcode
#: model:ir.actions.report,name:product.report_product_template_barcode
msgid "Product Barcode (PDF)"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "Kategorije proizvoda"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__categ_id
#: model:ir.model.fields,field_description:product.field_product_product__categ_id
#: model:ir.model.fields,field_description:product.field_product_template__categ_id
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: product
#: model:ir.actions.report,name:product.report_product_label
#: model:ir.actions.report,name:product.report_product_template_label
msgid "Product Label (PDF)"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr "Naziv proizvoda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template__packaging_ids
msgid "Product Packages"
msgstr "Paketi proizvoda"

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Product Packaging"
msgstr ""

#. module: product
#: model:ir.actions.report,name:product.report_product_packaging
msgid "Product Packaging (PDF)"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_stock_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
msgid "Product Packagings"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_price_history
msgid "Product Price List History"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_report_product_report_pricelist
msgid "Product Price List Report"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_price_list
msgid "Product Price per Unit Based on Pricelist Version"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_exclusion
msgid "Product Template Attribute Exclusion"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__type
#: model:ir.model.fields,field_description:product.field_product_template__type
msgid "Product Type"
msgstr "Tip proizvoda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_id
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: selection:product.pricelist.item,applied_on:0
msgid "Product Variant"
msgstr "Varijante proizvoda"

#. module: product
#: model:ir.actions.act_window,name:product.product_attribute_value_action
msgid "Product Variant Values"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
msgid "Product Variants"
msgstr "Varijante proizvoda"

#. module: product
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_all
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Products"
msgstr "Proizvodi"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr "Cijena proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr "Cjenovnik proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr "Pretraži cijene proizvoda"

#. module: product
#: code:addons/product/models/product.py:475
#, python-format
msgid "Products: "
msgstr "Proizvodi: "

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template__lst_price
#: selection:product.pricelist.item,base:0
msgid "Public Price"
msgstr "Javna cijena"

#. module: product
#: model:product.pricelist,name:product.list0
msgid "Public Pricelist"
msgstr "Javni cjenovnik"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Purchase"
msgstr "Nabava"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_purchase
#: model:ir.model.fields,field_description:product.field_product_template__description_purchase
msgid "Purchase Description"
msgstr "Opis nabavke"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template__uom_po_id
msgid "Purchase Unit of Measure"
msgstr "Nabavna jedinica mjere"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__qty1
msgid "Quantity-1"
msgstr "Količina-1"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__qty2
msgid "Quantity-2"
msgstr "Količina-2"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__qty3
msgid "Quantity-3"
msgstr "Količina-3"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__qty4
msgid "Quantity-4"
msgstr "Količina-4"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__qty5
msgid "Quantity-5"
msgstr "Količina-5"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__code
msgid "Reference"
msgstr "Referenca"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: product
#: model:product.product,name:product.expense_product
#: model:product.template,name:product.expense_product_product_template
msgid "Restaurant Expenses"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Rounding Method"
msgstr "Način zaokruživanja"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_sale
#: model:ir.model.fields,field_description:product.field_product_template__description_sale
msgid "Sale Description"
msgstr "Opis prodaje"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__lst_price
msgid "Sale Price"
msgstr "Prodajna cijena"

#. module: product
#: model:product.category,name:product.product_category_1
#: model:product.category,name:product.product_category_6
msgid "Saleable"
msgstr "Prodajni"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales"
msgstr "Prodaja"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__list_price
#: model:ir.model.fields,field_description:product.field_product_template__list_price
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
msgid "Sales Price"
msgstr "Prodajna cijena"

#. module: product
#: model:res.groups,name:product.group_sale_pricelist
msgid "Sales Pricelists"
msgstr "Prodajni cjenovnici"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__categ_id
#: model:ir.model.fields,help:product.field_product_template__categ_id
msgid "Select category for the current product"
msgstr "Odabir kategorije za trenutni proizvod"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value__sequence
#: model:ir.model.fields,field_description:product.field_product_packaging__sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist__sequence
#: model:ir.model.fields,field_description:product.field_product_product__sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__sequence
#: model:ir.model.fields,field_description:product.field_product_template__sequence
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: product
#: selection:product.template,type:0
msgid "Service"
msgstr "Usluga"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model:product.category,name:product.product_category_3
msgid "Services"
msgstr "Usluge"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, set rounding 10, surcharge -0.01"
msgstr ""
"Postavlja cijenu tako da je množioc ove vrijednosti.\n"
"Zaokruživanje se primjenjuje nakon popusta a prije poskupljenja.\n"
"Ako hoćete da Vam se cijena završava sa 9.99, postavite zaokruženje 10, dodatak -0.01"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__company_share_product
msgid "Share product to all companies"
msgstr "Dijeli proizvode sa svim kompanijama"

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings__company_share_product
msgid ""
"Share your product to all companies defined in your instance.\n"
" * Checked : Product are visible for every company, even if a company is defined on the partner.\n"
" * Unchecked : Each company can see only its product (product where company is defined). Product not related to a company are visible for all companies."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Show all records which has next action date is before today"
msgstr "Prikaži sve zapise koji imaju datum sljedeće akcije prije danas"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_pricelist
msgid "Show pricelists On Products"
msgstr "Prikaži cijenovnike na proizvodima"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_pricelist_item
msgid "Show pricelists to customers"
msgstr "Prikaži cijenovnike kupcima"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_small
#: model:ir.model.fields,field_description:product.field_product_template__image_small
msgid "Small-sized image"
msgstr "Male slike"

#. module: product
#: model:ir.model.fields,help:product.field_product_template__image_small
msgid ""
"Small-sized image of the product. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Mala slika proizvoda. Automatski joj je promjenjena veličina na 64x64px, sa "
"očuvanim omjerom. Koristite ovo polje gdje je god potrebna mala slika."

#. module: product
#: model:product.category,name:product.product_category_4
msgid "Software"
msgstr "Software"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""
"Upišite kategoriju proizvoda ako se ovo pravilo odnosi samo na proizvode "
"koji pripadaju toj kategoriji ili podređenim kategorijama. Ako ne, onda "
"ostavite prazno."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""
"Odredite proizvod ako se ovo pravilo odnosi na jedan proizvod. U suprotnom "
"ostavite prazno."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""
"Specifirajte predložak ako ovo pravilo se odnosi samo na jedan predložak "
"proizvoda. U suprotnom ostavite prazno."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_surcharge
msgid ""
"Specify the fixed amount to add or substract(if negative) to the amount "
"calculated with the discount."
msgstr ""
"Odredite fiksni iznos za dodati ili oduzeti(ukoliko je iznos negativan) "
"iznosu sa ukalkulisanim popustom."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr "Odredite najveći iznos marže za granicu iznad osnovne cijene."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr "Specificirajte minimalni iznos marže na osnovnu cijenu."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_start
msgid "Start Date"
msgstr "Datum početka"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_start
msgid "Start date for this vendor price"
msgstr "Početni datum za cijenu ovog dobavljača"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_start
msgid "Starting date for the pricelist item validation"
msgstr "Početni datum za validaciju stavke cijenovnika"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__activity_state
#: model:ir.model.fields,help:product.field_product_template__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
msgid "Steel"
msgstr ""

#. module: product
#: selection:product.template,type:0
msgid "Storable Product"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_7
#: model:product.template,name:product.product_product_7_product_template
msgid "Storage Box"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Cjenovnik dobavljača"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__pricelist_id
#: model:ir.model.fields,help:product.field_product_template__pricelist_id
msgid ""
"Technical field. Used for searching on pricelists, not stored in database."
msgstr ""
"Tehničko polje. Koristi se u pretragama cijenovnika, nije pohranjen u bazi "
"podataka."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_attribute_value_ids
msgid "Template Attribute Values"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"The computed price is expressed in the default Unit of Measure of the "
"product."
msgstr ""

#. module: product
#: code:addons/product/models/product_template.py:303
#, python-format
msgid ""
"The default Unit of Measure and the purchase Unit of Measure must be in the "
"same category."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__sequence
msgid "The first in the sequence is the default one."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__min_qty
msgid ""
"The minimal quantity to purchase from this vendor, expressed in the vendor "
"Product Unit of Measure if not any, in the default unit of measure of the "
"product otherwise."
msgstr ""

#. module: product
#: code:addons/product/models/product_pricelist.py:443
#, python-format
msgid "The minimum margin should be lower than the maximum margin."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_category__product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr ""

#. module: product
#: code:addons/product/models/product_template.py:475
#, python-format
msgid ""
"The number of variants to generate is too high. You should either not "
"generate variants for each combination or generate them on demand from the "
"sales order. To do so, open the form view of attributes and change the mode "
"of *Create Variants*."
msgstr ""

#. module: product
#: code:addons/product/models/product_attribute.py:56
#, python-format
msgid ""
"The operation cannot be completed:\n"
"You are trying to delete an attribute value with a reference on a product variant."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__price
msgid "The price to purchase a product"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__qty
msgid "The total number of products you can have per pallet or box."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__volume
#: model:ir.model.fields,help:product.field_product_template__volume
msgid "The volume in m3."
msgstr "Zapremina u m3"

#. module: product
#: model:ir.model.fields,help:product.field_product_template__weight
msgid "The weight of the contents in Kg, not including any packaging, etc."
msgstr ""

#. module: product
#: sql_constraint:product.attribute.value:0
msgid "This attribute value already exists !"
msgstr "Vrijednost atributa već postoji !"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_uom
msgid "This comes from the product form."
msgstr "Ovo dolazi iz forme proizvoda."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__image_variant
msgid ""
"This field holds the image used as image for the product variant, limited to"
" 1024x1024px."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_template__image
msgid ""
"This field holds the image used as image for the product, limited to "
"1024x1024px."
msgstr ""
"Ovo polje sadrži sliku koja se koristi kao slika proizvoda, ograničena na "
"1024x1024px."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is only for internal purposes."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note will show up on sales orders & invoices."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_res_partner__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"Ovaj cjenovnik će se koristiti, umjesto predefinisanog, za prodaju trenutnom"
" partneru"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""

#. module: product
#: model:product.product,name:product.consu_delivery_01
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Three-Seat Sofa"
msgstr ""

#. module: product
#: selection:product.product,activity_state:0
#: selection:product.template,activity_state:0
msgid "Today"
msgstr "Danas"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,field_description:product.field_product_product__uom_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,field_description:product.field_product_template__uom_id
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_name
#: model:ir.model.fields,field_description:product.field_product_template__uom_name
msgid "Unit of Measure Name"
msgstr ""

#. module: product
#: model:product.product,uom_name:product.consu_delivery_01
#: model:product.product,uom_name:product.consu_delivery_02
#: model:product.product,uom_name:product.consu_delivery_03
#: model:product.product,uom_name:product.expense_product
#: model:product.product,uom_name:product.product_delivery_01
#: model:product.product,uom_name:product.product_delivery_02
#: model:product.product,uom_name:product.product_order_01
#: model:product.product,uom_name:product.product_product_10
#: model:product.product,uom_name:product.product_product_11
#: model:product.product,uom_name:product.product_product_11b
#: model:product.product,uom_name:product.product_product_12
#: model:product.product,uom_name:product.product_product_13
#: model:product.product,uom_name:product.product_product_16
#: model:product.product,uom_name:product.product_product_20
#: model:product.product,uom_name:product.product_product_22
#: model:product.product,uom_name:product.product_product_24
#: model:product.product,uom_name:product.product_product_25
#: model:product.product,uom_name:product.product_product_27
#: model:product.product,uom_name:product.product_product_3
#: model:product.product,uom_name:product.product_product_4
#: model:product.product,uom_name:product.product_product_4b
#: model:product.product,uom_name:product.product_product_4c
#: model:product.product,uom_name:product.product_product_4d
#: model:product.product,uom_name:product.product_product_5
#: model:product.product,uom_name:product.product_product_6
#: model:product.product,uom_name:product.product_product_7
#: model:product.product,uom_name:product.product_product_8
#: model:product.product,uom_name:product.product_product_9
#: model:product.template,uom_name:product.consu_delivery_01_product_template
#: model:product.template,uom_name:product.consu_delivery_02_product_template
#: model:product.template,uom_name:product.consu_delivery_03_product_template
#: model:product.template,uom_name:product.expense_product_product_template
#: model:product.template,uom_name:product.product_delivery_01_product_template
#: model:product.template,uom_name:product.product_delivery_02_product_template
#: model:product.template,uom_name:product.product_order_01_product_template
#: model:product.template,uom_name:product.product_product_10_product_template
#: model:product.template,uom_name:product.product_product_11b_product_template
#: model:product.template,uom_name:product.product_product_12_product_template
#: model:product.template,uom_name:product.product_product_13_product_template
#: model:product.template,uom_name:product.product_product_16_product_template
#: model:product.template,uom_name:product.product_product_20_product_template
#: model:product.template,uom_name:product.product_product_22_product_template
#: model:product.template,uom_name:product.product_product_24_product_template
#: model:product.template,uom_name:product.product_product_25_product_template
#: model:product.template,uom_name:product.product_product_27_product_template
#: model:product.template,uom_name:product.product_product_3_product_template
#: model:product.template,uom_name:product.product_product_4b_product_template
#: model:product.template,uom_name:product.product_product_5_product_template
#: model:product.template,uom_name:product.product_product_6_product_template
#: model:product.template,uom_name:product.product_product_7_product_template
#: model:product.template,uom_name:product.product_product_8_product_template
#: model:product.template,uom_name:product.product_product_9_product_template
msgid "Unit(s)"
msgstr "kom (komad)"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_uom
msgid "Units of Measure"
msgstr "Jedinice mere"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_unread
#: model:ir.model.fields,field_description:product.field_product_template__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_unread_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_sale_pricelist
msgid "Use pricelists to adapt your price per customers"
msgstr "Koristite cjenik za prilagođavanje cijena po kupcima"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr "Validnost"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__name
msgid "Value"
msgstr "Vrijednost"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Values"
msgstr "Vrijednosti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_variant_count
msgid "Variant Count"
msgstr "Broj varijanti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant
msgid "Variant Image"
msgstr "Slika varijante"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr "Informacije varijante"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__price_extra
msgid "Variant Price Extra"
msgstr "Dodatna cijena varijante"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__variant_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__variant_seller_ids
msgid "Variant Seller"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:product.variants_tree_view
msgid "Variant Values"
msgstr "Vrijednosti varijante"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr "Varijante"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_variant
msgid "Variants and Options"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__name
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Vendor"
msgstr "Dobavljač"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Vendor Bills"
msgstr "Računi dobavljača"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr "Informacije dobavljača"

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Vendor Pricelists"
msgstr "Cijenovnik dobavljača"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_code
msgid "Vendor Product Code"
msgstr "Šifra dobavljača"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_name
msgid "Vendor Product Name"
msgstr "Naziv proizvoda dobavljača"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__name
msgid "Vendor of this product"
msgstr "Dobavljač ovog proizvoda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__seller_ids
msgid "Vendors"
msgstr "Dobavljači"

#. module: product
#: model:product.product,name:product.product_product_2
#: model:product.template,name:product.product_product_2_product_template
msgid "Virtual Home Staging"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_1
#: model:product.template,name:product.product_product_1_product_template
msgid "Virtual Interior Design"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume
#: model:ir.model.fields,field_description:product.field_product_template__volume
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Volume"
msgstr "Volumen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__website_message_ids
#: model:ir.model.fields,field_description:product.field_product_template__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__website_message_ids
#: model:ir.model.fields,help:product.field_product_template__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight
#: model:ir.model.fields,field_description:product.field_product_template__weight
msgid "Weight"
msgstr "Težina"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Weight Measurement"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight_uom_id
#: model:ir.model.fields,field_description:product.field_product_template__weight_uom_id
msgid "Weight Unit of Measure"
msgstr "Težinska jedinica mjere"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__weight
msgid ""
"Weight of the product, packaging not included. The unit of measure can be "
"changed in the general settings"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_weight_in_lbs
msgid "Weight unit of measure"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__weight_uom_name
msgid "Weight unit of measure label"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Weights"
msgstr "Težine"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
msgid "White"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"You can assign pricelists to your customers or select one when creating a "
"new sales quotation."
msgstr ""

#. module: product
#: code:addons/product/models/product_pricelist.py:437
#, python-format
msgid ""
"You cannot assign the Main Pricelist as Other Pricelist in PriceList Item"
msgstr ""

#. module: product
#: code:addons/product/models/product.py:54
#, python-format
msgid "You cannot create recursive categories."
msgstr ""

#. module: product
#: code:addons/product/models/decimal_precision.py:16
#, python-format
msgid ""
"You cannot define the decimal precision of 'Account' as greater than the "
"rounding factor of the company's main currency"
msgstr ""

#. module: product
#: code:addons/product/models/product_attribute.py:79
#, python-format
msgid "You cannot use this attribute with the following value."
msgstr ""

#. module: product
#: code:addons/product/wizard/product_price_list.py:26
#, python-format
msgid "You have to set a logo or a layout for your company."
msgstr ""

#. module: product
#: code:addons/product/wizard/product_price_list.py:28
#, python-format
msgid "You have to set your reports's header and footer layout."
msgstr "Morate da postavite zaglavlje i podnožje za izvještaj"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"You must define a product for everything you purchase,\n"
"                whether it's a physical product, a consumable or services."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell, whether it's a physical product,\n"
"                a consumable or a service you offer to customers.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""

#. module: product
#: code:addons/product/models/product_template.py:31
#, python-format
msgid ""
"You must define at least one product category in order to be able to create "
"products."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_view_kanban
msgid "days"
msgstr "Dani"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr "npr.: Lampe"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Subscription"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr "npr.: Dobavljači iz BiH"

#. module: product
#: model:product.product,weight_uom_name:product.consu_delivery_01
#: model:product.product,weight_uom_name:product.consu_delivery_02
#: model:product.product,weight_uom_name:product.consu_delivery_03
#: model:product.product,weight_uom_name:product.expense_hotel
#: model:product.product,weight_uom_name:product.expense_product
#: model:product.product,weight_uom_name:product.product_delivery_01
#: model:product.product,weight_uom_name:product.product_delivery_02
#: model:product.product,weight_uom_name:product.product_order_01
#: model:product.product,weight_uom_name:product.product_product_1
#: model:product.product,weight_uom_name:product.product_product_10
#: model:product.product,weight_uom_name:product.product_product_11
#: model:product.product,weight_uom_name:product.product_product_11b
#: model:product.product,weight_uom_name:product.product_product_12
#: model:product.product,weight_uom_name:product.product_product_13
#: model:product.product,weight_uom_name:product.product_product_16
#: model:product.product,weight_uom_name:product.product_product_2
#: model:product.product,weight_uom_name:product.product_product_20
#: model:product.product,weight_uom_name:product.product_product_22
#: model:product.product,weight_uom_name:product.product_product_24
#: model:product.product,weight_uom_name:product.product_product_25
#: model:product.product,weight_uom_name:product.product_product_27
#: model:product.product,weight_uom_name:product.product_product_3
#: model:product.product,weight_uom_name:product.product_product_4
#: model:product.product,weight_uom_name:product.product_product_4b
#: model:product.product,weight_uom_name:product.product_product_4c
#: model:product.product,weight_uom_name:product.product_product_4d
#: model:product.product,weight_uom_name:product.product_product_5
#: model:product.product,weight_uom_name:product.product_product_6
#: model:product.product,weight_uom_name:product.product_product_7
#: model:product.product,weight_uom_name:product.product_product_8
#: model:product.product,weight_uom_name:product.product_product_9
#: model:product.template,weight_uom_name:product.consu_delivery_01_product_template
#: model:product.template,weight_uom_name:product.consu_delivery_02_product_template
#: model:product.template,weight_uom_name:product.consu_delivery_03_product_template
#: model:product.template,weight_uom_name:product.expense_hotel_product_template
#: model:product.template,weight_uom_name:product.expense_product_product_template
#: model:product.template,weight_uom_name:product.product_delivery_01_product_template
#: model:product.template,weight_uom_name:product.product_delivery_02_product_template
#: model:product.template,weight_uom_name:product.product_order_01_product_template
#: model:product.template,weight_uom_name:product.product_product_10_product_template
#: model:product.template,weight_uom_name:product.product_product_11b_product_template
#: model:product.template,weight_uom_name:product.product_product_12_product_template
#: model:product.template,weight_uom_name:product.product_product_13_product_template
#: model:product.template,weight_uom_name:product.product_product_16_product_template
#: model:product.template,weight_uom_name:product.product_product_1_product_template
#: model:product.template,weight_uom_name:product.product_product_20_product_template
#: model:product.template,weight_uom_name:product.product_product_22_product_template
#: model:product.template,weight_uom_name:product.product_product_24_product_template
#: model:product.template,weight_uom_name:product.product_product_25_product_template
#: model:product.template,weight_uom_name:product.product_product_27_product_template
#: model:product.template,weight_uom_name:product.product_product_2_product_template
#: model:product.template,weight_uom_name:product.product_product_3_product_template
#: model:product.template,weight_uom_name:product.product_product_4b_product_template
#: model:product.template,weight_uom_name:product.product_product_5_product_template
#: model:product.template,weight_uom_name:product.product_product_6_product_template
#: model:product.template,weight_uom_name:product.product_product_7_product_template
#: model:product.template,weight_uom_name:product.product_product_8_product_template
#: model:product.template,weight_uom_name:product.product_product_9_product_template
msgid "kg"
msgstr "kg"

#. module: product
#: code:addons/product/models/product.py:586
#: code:addons/product/models/product_template.py:566
#, python-format
msgid "product"
msgstr ""

#. module: product
#: model:product.product,description_sale:product.consu_delivery_02
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid "raid 1, 512ECC ram"
msgstr ""

#. module: product
#: model:product.product,description_sale:product.consu_delivery_01
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid "raid 10, 2048ECC ram"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr "nadređena kompanija"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template"
msgstr "šablon proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "za"
