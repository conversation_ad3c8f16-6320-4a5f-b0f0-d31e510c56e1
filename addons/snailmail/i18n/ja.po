# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 05:52+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__snailmail_cover
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Add a Cover Page"
msgstr "表紙をつける"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Address"
msgstr "アドレス"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending a letter with Snailmail."
msgstr "郵便で手紙を郵送中にエラーが発生しました。"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "An error occurred when sending the document by post.<br>Error: %s"
msgstr "郵送でドキュメントを送信中にエラーが発生しました。<br>エラー: %s"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "An unknown error happened. Please contact the support."
msgstr "不明なエラーが発生しました。サポートまでご連絡下さい。"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "An unknown error occurred. Please contact our"
msgstr "不明なエラーが発生しました。"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_id
msgid "Attachment"
msgstr "添付"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_fname
msgid "Attachment Filename"
msgstr "添付ファイル名"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/models/snailmail_notification_popover_content_view.js:0
#, python-format
msgid "Awaiting Dispatch"
msgstr "発送待ち"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__duplex
msgid "Both side"
msgstr "両面"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_duplex
msgid "Both sides"
msgstr "両面"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "Buy credits"
msgstr "クレジットを購入"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__credit_error
msgid "CREDIT_ERROR"
msgstr "CREDIT_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Cancel"
msgstr "取消"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel Letter"
msgstr "手紙を取消"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
#, python-format
msgid "Cancel letter"
msgstr "手紙を取消"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel notification in failure"
msgstr "失敗した通知をキャンセルする"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/models/snailmail_notification_popover_content_view.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__canceled
#, python-format
msgid "Canceled"
msgstr "取消済"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__city
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__city
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "City"
msgstr "市区町村"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
#, python-format
msgid "Close"
msgstr "閉じる"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_color
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__color
msgid "Color"
msgstr "色"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_company
msgid "Companies"
msgstr "会社"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__company_id
msgid "Company"
msgstr "会社"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "Confirm"
msgstr "確認"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_partner
msgid "Contact"
msgstr "連絡先"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__country_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__country_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Country"
msgstr "国"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__cover
msgid "Cover Page"
msgstr "カバーページ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_uid
msgid "Created by"
msgstr "作成者"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_date
msgid "Created on"
msgstr "作成日"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__display_name
msgid "Display Name"
msgstr "表示名"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_datas
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Document"
msgstr "ドキュメント"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__res_id
msgid "Document ID"
msgstr "ドキュメントID"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_thread
msgid "Email Thread"
msgstr "Eメールスレッド"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/models/snailmail_notification_popover_content_view.js:0
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__error_code
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__error
#, python-format
msgid "Error"
msgstr "エラー"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__format_error
msgid "FORMAT_ERROR"
msgstr "FORMAT_ERROR"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_missing_required_fields_action
#, python-format
msgid "Failed letter"
msgstr "失敗した手紙"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "故障タイプ"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_format_error_action
msgid "Format Error"
msgstr "フォーマットエラー"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_format_error
msgid "Format Error Sending a Snailmail Letter"
msgstr "郵送で手紙を送信するフォーマットエラー"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__id
msgid "ID"
msgstr "ID"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__pending
msgid "In Queue"
msgstr "送信予約済"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__info_msg
msgid "Information"
msgstr "情報"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Invalid recipient name."
msgstr "無効な宛先名"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter____last_update
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error____last_update
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_mail_message__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__letter_id
msgid "Letter"
msgstr "手紙"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Letter sent by post with Snailmail"
msgstr "Snailmailで郵送で送られたレター"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Letters"
msgstr "レター"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__missing_required_fields
msgid "MISSING_REQUIRED_FIELDS"
msgstr "MISSING_REQUIRED_FIELDS"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_message
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__message_id
msgid "Message"
msgstr "メッセージ"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_notification
msgid "Message Notifications"
msgstr "メッセージ通知"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,help:snailmail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr "メッセージの種類：電子メールメッセージのメール、システムメッセージの通知、ユーザ返信など他のメッセージのコメント"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__model
msgid "Model"
msgstr "モデル"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_confirm__model_name
msgid "Model Name"
msgstr "モデル名"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__no_price_available
msgid "NO_PRICE_AVAILABLE"
msgstr "NO_PRICE_AVAILABLE"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Not enough credits for Snail Mail"
msgstr "Snalmail用のクレジットが不足しています。・"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "通知タイプ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__notification_ids
msgid "Notifications"
msgstr "通知"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "One or more required fields are empty."
msgstr "1以上の必要なフィールドが空です。"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__report_template
msgid "Optional report to print and attach"
msgstr "印刷・添付するレポート"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid ""
"Our service cannot read your letter due to its format.<br/>\n"
"                Please modify the format of the template or update your settings\n"
"                to automatically add a blank cover page to all letters."
msgstr ""
"当社サービスでは、フォーマットの問題でレターを読むことができません。<br/>\n"
"              テンプレートのフォーマットを変更するか、設定を更新して\n"
"全てのレターに空白のカバーページを自動的に追加して下さい。"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__partner_id
msgid "Partner"
msgstr "取引先"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Please use an A4 Paper format."
msgstr "A4フォーマットを使用して下さい。"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr "両面印刷"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_color
msgid "Print In Color"
msgstr "カラー印刷"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "Re-send letter"
msgstr "レターを再送"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__partner_id
msgid "Recipient"
msgstr "宛先"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__reference
msgid "Related Record"
msgstr "関連レコード"

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_actions_report
msgid "Report Action"
msgstr "レポートアクション"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Send Now"
msgstr "今すぐ送信"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/models/snailmail_notification_popover_content_view.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__sent
#, python-format
msgid "Sent"
msgstr "送信日"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__user_id
msgid "Sent by"
msgstr "送信者"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Snail Mails are successfully sent"
msgstr "Snailmailが正常に送られました"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/wizard/snailmail_confirm.py:0
#: model:ir.model.fields.selection,name:snailmail.selection__mail_message__message_type__snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__notification_type__snail
#, python-format
msgid "Snailmail"
msgstr "Snailmail"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_confirm
msgid "Snailmail Confirm"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "Snailmail Confirmation"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_credit
msgid "Snailmail Credit Error"
msgstr "Snailmailクレジットエラー"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/models/notification_group.js:0
#, python-format
msgid "Snailmail Failures"
msgstr "Snailmail機能"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_format
msgid "Snailmail Format Error"
msgstr "Snailmailフォーマットエラー"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__letter_id
msgid "Snailmail Letter"
msgstr "Snailmailレター"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.action_mail_letters
#: model:ir.ui.menu,name:snailmail.menu_snailmail_letters
msgid "Snailmail Letters"
msgstr "Snailmailレター"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_fields
msgid "Snailmail Missing Required Fields"
msgstr "Snailmail欠落した要求フィールド"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_price
msgid "Snailmail No Price Available"
msgstr "Snailmail可能な利用価格なし"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__message_id
msgid "Snailmail Status Message"
msgstr "Snailmailステータスメッセージ"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_trial
msgid "Snailmail Trial Error"
msgstr "Snailmail試行エラー"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_error
msgid "Snailmail Unknown Error"
msgstr "Snailmail不明なエラー"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__snailmail_error
#: model:ir.model.fields,field_description:snailmail.field_mail_message__snailmail_error
msgid "Snailmail message in error"
msgstr "エラー中のSnailmailメッセージ"

#. module: snailmail
#: model:ir.actions.server,name:snailmail.snailmail_print_ir_actions_server
#: model:ir.cron,cron_name:snailmail.snailmail_print
msgid "Snailmail: process letters queue"
msgstr "Snailmail: 送信予約済みレターの処理"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__state_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "State"
msgstr "都道府県/州"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state
msgid "Status"
msgstr "ステータス"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street
msgid "Street"
msgstr "町名番地"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street 2..."
msgstr "町名番地2…"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street..."
msgstr "町名番地…"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street2
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street2
msgid "Street2"
msgstr "町名番地2"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__trial_error
msgid "TRIAL_ERROR"
msgstr "TRIAL_ERROR"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The address of the recipient is not complete"
msgstr "宛先の住所が不完全です。"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"The attachment of the letter could not be sent. Please check its content and"
" contact the support if the problem persists."
msgstr "レターの添付ファイルを送信できませんでした。内容をご確認の上、問題が解決しない場合はサポートまでご連絡下さい。"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The country of the partner is not covered by Snailmail."
msgstr "取引先の国はSnailmailの対象外です。"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid ""
"The country to which you want to send the letter is not supported by our "
"service."
msgstr "レターを送りたい国は、当社サービスではサポートされていません。"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid ""
"The customer address is not complete. Update the address here and re-send "
"the letter."
msgstr "顧客の住所に不備があります。こちらから住所を更新して、レターを再送信してください。"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The document was correctly sent by post.<br>The tracking id is %s"
msgstr "ドキュメントは正常に郵送されました。<br>トラッキングID:%s"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid ""
"The letter could not be sent due to insufficient credits on your IAP "
"account."
msgstr "IAPアカウントのクレジットが不十分なため、レターを送信することができませんでした。"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:snailmail.field_mail_message__message_type
msgid "Type"
msgstr "タイプ"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__unknown_error
msgid "UNKNOWN_ERROR"
msgstr "UNKNOWN_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Update Config and Re-send"
msgstr "設定を更新して再送"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Update address and re-send"
msgstr "住所を更新し、再送"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_missing_required_fields
msgid "Update address of partner"
msgstr "取引先の住所を更新"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_snailmail_letter__state
msgid ""
"When a letter is created, the status is 'Pending'.\n"
"If the letter is correctly sent, the status goes in 'Sent',\n"
"If not, it will got in state 'Error' and the error message will be displayed in the field 'Error Message'."
msgstr ""
"レターが作成されると、ステータスは'保留'になります。\n"
"レターが正しく送信されると、ステータスは'送信済'になり、\n"
"そうでない場合は、'エラー'ステータスになり、エラーメッセージが'エラーメッセージ'フィールドに表示されます。"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "You are about to send this"
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"You don't have an IAP account registered for this service.<br>Please go to "
"<a href=%s target=\"new\">iap.odoo.com</a> to claim your free credits."
msgstr ""
"このサービス用に登録されたIAPアカウントがありません。<br>無料クレジットを申請するには<a href=%s "
"target=\"new\">iap.odoo.com</a>に行って下さい。"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"You don't have enough credits to perform this operation.<br>Please go to "
"your <a href=%s target=\"new\">iap account</a>."
msgstr ""
"この操作を行うにはクレジットが不足しています。<br>ご自分の<a href=%s "
"target=\"new\">IAPアカウント</a>に行って下さい。"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "You need credits on your IAP account to send a letter."
msgstr "手紙を送るには、IAPアカウントにクレジットが必要です。"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "ZIP"
msgstr "郵便番号"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__zip
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__zip
msgid "Zip"
msgstr "郵便番号"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "by post. Are you sure you want to continue?"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "for further assistance."
msgstr "ご連絡下さい。"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "support"
msgstr "サポートまで"
