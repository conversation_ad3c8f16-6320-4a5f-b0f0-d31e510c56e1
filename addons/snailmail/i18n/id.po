# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 05:52+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__snailmail_cover
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Add a Cover Page"
msgstr "Tambahkan Halaman Cover"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Address"
msgstr "Alamat"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending a letter with Snailmail."
msgstr "Terjadi error saat mengirimkan surat melalui pos."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "An error occurred when sending the document by post.<br>Error: %s"
msgstr "Terjadi error saat mengirimkan dokumen melalui pos.<br>Error: %s"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "An unknown error happened. Please contact the support."
msgstr "Error yang tidak diketahui terjadi. Mohon hubungi bantuan."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "An unknown error occurred. Please contact our"
msgstr "Error yang tidak diketahui terjadi. Mohon hubungi kami"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_id
msgid "Attachment"
msgstr "Lampiran"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_fname
msgid "Attachment Filename"
msgstr "Nama file Lampiran"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/models/snailmail_notification_popover_content_view.js:0
#, python-format
msgid "Awaiting Dispatch"
msgstr "Menunggu Dispatch"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__duplex
msgid "Both side"
msgstr "Kedua sisi"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_duplex
msgid "Both sides"
msgstr "Kedua sisi-sisi"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "Buy credits"
msgstr "Beli kredit"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__credit_error
msgid "CREDIT_ERROR"
msgstr "CREDIT_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Cancel"
msgstr "Batal"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel Letter"
msgstr "Batalkan Surat"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
#, python-format
msgid "Cancel letter"
msgstr "Batalkan surat"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel notification in failure"
msgstr "Batalkan notifikasi dalam gagal"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/models/snailmail_notification_popover_content_view.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__canceled
#, python-format
msgid "Canceled"
msgstr "Dibatalkan"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__city
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__city
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "City"
msgstr "Kota"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
#, python-format
msgid "Close"
msgstr "Tutup"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_color
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__color
msgid "Color"
msgstr "Warna"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__company_id
msgid "Company"
msgstr "Perusahaan"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "Confirm"
msgstr "Konfirmasi"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__country_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__country_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Country"
msgstr "Negara"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__cover
msgid "Cover Page"
msgstr "Halaman Cover"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_datas
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Document"
msgstr "Dokumen"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__res_id
msgid "Document ID"
msgstr "ID Dokumen"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_thread
msgid "Email Thread"
msgstr "Thread email"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/models/snailmail_notification_popover_content_view.js:0
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__error_code
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__error
#, python-format
msgid "Error"
msgstr "Eror!"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__format_error
msgid "FORMAT_ERROR"
msgstr "FORMAT_ERROR"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_missing_required_fields_action
#, python-format
msgid "Failed letter"
msgstr "Surat gagal"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Tipe kegagalan"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_format_error_action
msgid "Format Error"
msgstr "Format Error"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_format_error
msgid "Format Error Sending a Snailmail Letter"
msgstr "Format Error Mengirimkan Surat Pos"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__id
msgid "ID"
msgstr "ID"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__pending
msgid "In Queue"
msgstr "Dalam Antrian"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__info_msg
msgid "Information"
msgstr "Informasi"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Invalid recipient name."
msgstr "Nama penerima tidak valid."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter____last_update
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error____last_update
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_mail_message__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__letter_id
msgid "Letter"
msgstr "Sura"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Letter sent by post with Snailmail"
msgstr "Surat dikirim melalui pos dengan Snailmail"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Letters"
msgstr "Surat-Surat"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__missing_required_fields
msgid "MISSING_REQUIRED_FIELDS"
msgstr "MISSING_REQUIRED_FIELDS"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_message
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__message_id
msgid "Message"
msgstr "Pesan"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_notification
msgid "Message Notifications"
msgstr "Notifikasi Pesan"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,help:snailmail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Jenis pesan: email untuk pesan email, pemberitahuan untuk pesan sistem, "
"komentar untuk pesan lain seperti balasan pengguna"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__model
msgid "Model"
msgstr "Model"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_confirm__model_name
msgid "Model Name"
msgstr "Nama Model"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__no_price_available
msgid "NO_PRICE_AVAILABLE"
msgstr "NO_PRICE_AVAILABLE"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Not enough credits for Snail Mail"
msgstr "Kredit tidak mencukupi untuk surat pos"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Tipe Notifikasi"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__notification_ids
msgid "Notifications"
msgstr "Notifikasi"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "One or more required fields are empty."
msgstr "Satu atau lebih field yang dibutuhkan masih kosong."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__report_template
msgid "Optional report to print and attach"
msgstr "Laporan opsional untuk dicetak dan dilampirkan"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid ""
"Our service cannot read your letter due to its format.<br/>\n"
"                Please modify the format of the template or update your settings\n"
"                to automatically add a blank cover page to all letters."
msgstr ""
"Layanan kami tidak dapat membaca surat Anda karena formatnya.<br/>\n"
"                Mohon modifikasi format templat atau update pengaturan Anda\n"
"                untuk secara otomatis menambahkan halaman sampul kosong ke semua surat."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__partner_id
msgid "Partner"
msgstr "Rekanan"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Please use an A4 Paper format."
msgstr "Mohon gunakan format Kertas A4."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr "Cetak Kedua sisi"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_color
msgid "Print In Color"
msgstr "Cetak dengan Warna"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "Re-send letter"
msgstr "Kirim-ulang surat"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__partner_id
msgid "Recipient"
msgstr "Penerima"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__reference
msgid "Related Record"
msgstr "Record Terkait"

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_actions_report
msgid "Report Action"
msgstr "Report Action"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Send Now"
msgstr "Kirim Sekarang"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/models/snailmail_notification_popover_content_view.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__sent
#, python-format
msgid "Sent"
msgstr "Terkirim"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__user_id
msgid "Sent by"
msgstr "Dikirim oleh"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Snail Mails are successfully sent"
msgstr "Surat Pos berhasil dikirim"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/wizard/snailmail_confirm.py:0
#: model:ir.model.fields.selection,name:snailmail.selection__mail_message__message_type__snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__notification_type__snail
#, python-format
msgid "Snailmail"
msgstr "Snailmail"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_confirm
msgid "Snailmail Confirm"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "Snailmail Confirmation"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_credit
msgid "Snailmail Credit Error"
msgstr "Error Kredit Snailmail"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/models/notification_group.js:0
#, python-format
msgid "Snailmail Failures"
msgstr "Surat Pos Gagal"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_format
msgid "Snailmail Format Error"
msgstr "Error Format Surat Pos"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__letter_id
msgid "Snailmail Letter"
msgstr "Surat Pos"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.action_mail_letters
#: model:ir.ui.menu,name:snailmail.menu_snailmail_letters
msgid "Snailmail Letters"
msgstr "Surat-Surat Po"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_fields
msgid "Snailmail Missing Required Fields"
msgstr "Surat Pos Kurang Field yang Dibutuhkan"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_price
msgid "Snailmail No Price Available"
msgstr "Surat Pos Tidak Ada Harga Tersedia"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__message_id
msgid "Snailmail Status Message"
msgstr "Status Pesan Surat Po"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_trial
msgid "Snailmail Trial Error"
msgstr "Trial Surat Pos Error"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_error
msgid "Snailmail Unknown Error"
msgstr "Surat Pos Error Tidak Diketahui"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__snailmail_error
#: model:ir.model.fields,field_description:snailmail.field_mail_message__snailmail_error
msgid "Snailmail message in error"
msgstr "Pesan surat pos di error"

#. module: snailmail
#: model:ir.actions.server,name:snailmail.snailmail_print_ir_actions_server
#: model:ir.cron,cron_name:snailmail.snailmail_print
msgid "Snailmail: process letters queue"
msgstr "Surat Pos: proses antrian surat"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__state_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "State"
msgstr "Status"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state
msgid "Status"
msgstr "Status"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street
msgid "Street"
msgstr "Jalan"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street 2..."
msgstr "Tambahan nama jalan..."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street..."
msgstr "Jalan..."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street2
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street2
msgid "Street2"
msgstr "Jalan ke-2"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__trial_error
msgid "TRIAL_ERROR"
msgstr "TRIAL_ERROR"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The address of the recipient is not complete"
msgstr "Alamat penerima tidak lengkap"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"The attachment of the letter could not be sent. Please check its content and"
" contact the support if the problem persists."
msgstr ""
"Lampiran surat tidak dapat dikirim. Mohon periksa kontennya dan hubungi "
"bantuan bila masalah berlanjut."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The country of the partner is not covered by Snailmail."
msgstr "Negara partner tidak didukung oleh Surat Pos."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid ""
"The country to which you want to send the letter is not supported by our "
"service."
msgstr ""
"Negara ke mana Anda ingin mengirimkan surat tidak didukung oleh layanan "
"kami."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid ""
"The customer address is not complete. Update the address here and re-send "
"the letter."
msgstr ""
"Alamat pelanggan tidak lengkap. Update alamat di sini dan kirim ulang surat."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The document was correctly sent by post.<br>The tracking id is %s"
msgstr "Dokumen dengan tepat dikirim melalui pos.<br>Id pelacakan adalah %s"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid ""
"The letter could not be sent due to insufficient credits on your IAP "
"account."
msgstr ""
"Surat tidak dapat dikirim karena kerdit tidak mencukupi pada akun IAP Anda."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:snailmail.field_mail_message__message_type
msgid "Type"
msgstr "Jenis"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__unknown_error
msgid "UNKNOWN_ERROR"
msgstr "UNKNOWN_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Update Config and Re-send"
msgstr "Update Config dan Kirim-ulang"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Update address and re-send"
msgstr "Update alamat dan kirim-ulang"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_missing_required_fields
msgid "Update address of partner"
msgstr "Update alamat partner"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_snailmail_letter__state
msgid ""
"When a letter is created, the status is 'Pending'.\n"
"If the letter is correctly sent, the status goes in 'Sent',\n"
"If not, it will got in state 'Error' and the error message will be displayed in the field 'Error Message'."
msgstr ""
"Saat surat dibuat, status adalah 'Pending'.\n"
"Bila surat dengan tepat dikirim, status menjadi 'Terkirim',\n"
"Bila tidak, akan mendapatkan status 'Error' dan pesan error akan ditampilkan di field 'Pesan Error'."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "You are about to send this"
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"You don't have an IAP account registered for this service.<br>Please go to "
"<a href=%s target=\"new\">iap.odoo.com</a> to claim your free credits."
msgstr ""
"Anda tidak memiliki akun IAP yang didaftarkan untuk layanan ini.<br>Mohon "
"pergi ke <a href=%s target=\"new\">iap.odoo.com</a> untuk mengeklaim kredit "
"gratis Anda."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"You don't have enough credits to perform this operation.<br>Please go to "
"your <a href=%s target=\"new\">iap account</a>."
msgstr ""
"Anda tidak memiliki kredit yang mencukupi untuk melakukan operasi "
"ini.<br>Mohon pergi ke <a href=%s target=\"new\">akun iap</a> Anda."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "You need credits on your IAP account to send a letter."
msgstr "Anda membutuhkan kredit pada akun IAP Anda untuk mengirimkan surat."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "ZIP"
msgstr "Kode Pos"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__zip
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__zip
msgid "Zip"
msgstr "Kode Pos"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "by post. Are you sure you want to continue?"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "for further assistance."
msgstr "untuk bantuan lebih lanjut."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/components/snailmail_error/snailmail_error.xml:0
#, python-format
msgid "support"
msgstr "bantua"
