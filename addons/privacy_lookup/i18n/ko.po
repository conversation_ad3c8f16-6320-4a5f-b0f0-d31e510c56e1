# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* privacy_lookup
# 
# Translators:
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-09 14:06+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__additional_note
msgid "Additional Note"
msgstr "추가 메모"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__anonymized_email
msgid "Anonymized Email"
msgstr "익명 이메일"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__anonymized_name
msgid "Anonymized Name"
msgstr "익명"

#. module: privacy_lookup
#: model:ir.actions.server,name:privacy_lookup.ir_actions_server_archive_all
msgid "Archive Selection"
msgstr "보관 선택"

#. module: privacy_lookup
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
#, python-format
msgid "Archived"
msgstr "보관됨"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Can be archived"
msgstr "보관 가능"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__create_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__create_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__create_uid
msgid "Created by"
msgstr "작성자"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__create_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__create_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__create_date
msgid "Created on"
msgstr "작성일자"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__date
msgid "Date"
msgstr "일자"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "Delete"
msgstr "삭제"

#. module: privacy_lookup
#: model:ir.actions.server,name:privacy_lookup.ir_actions_server_unlink_all
msgid "Delete Selection"
msgstr "삭제 선택"

#. module: privacy_lookup
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "Deleted"
msgstr "삭제됨"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__display_name
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__display_name
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__display_name
msgid "Display Name"
msgstr "표시명"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_model
msgid "Document Model"
msgstr "문서 모델"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__email
msgid "Email"
msgstr "이메일"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__execution_details
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__execution_details
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__execution_details
msgid "Execution Details"
msgstr "실행 세부 정보"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__records_description
msgid "Found Records"
msgstr "검색된 정보"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Group By"
msgstr "그룹별"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__user_id
msgid "Handled By"
msgstr "담당자"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__has_active
msgid "Has Active"
msgstr "활성화됨"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__id
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__id
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__id
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "ID"
msgstr "ID"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__is_active
msgid "Is Active"
msgstr "활성화"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__is_unlinked
msgid "Is Unlinked"
msgstr "연결 해제됨"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log____last_update
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard____last_update
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__write_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__write_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__write_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__write_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__line_ids
msgid "Line"
msgstr "선"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__line_count
msgid "Line Count"
msgstr "명세 수"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__log_id
msgid "Log"
msgstr "로그"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "Lookup"
msgstr "검색"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Model"
msgstr "모델"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__name
msgid "Name"
msgstr "이름"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "Open Record"
msgstr "레코드 열기"

#. module: privacy_lookup
#: model:ir.ui.menu,name:privacy_lookup.privacy_menu
msgid "Privacy"
msgstr "비공개"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_log
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_log_view_form
msgid "Privacy Log"
msgstr "개인 정보 로그"

#. module: privacy_lookup
#: model:ir.actions.act_window,name:privacy_lookup.privacy_log_action
#: model:ir.actions.act_window,name:privacy_lookup.privacy_log_form_action
#: model:ir.ui.menu,name:privacy_lookup.pricacy_log_menu
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_log_view_list
msgid "Privacy Logs"
msgstr "개인 정보 로그"

#. module: privacy_lookup
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#: model:ir.actions.act_window,name:privacy_lookup.action_privacy_lookup_wizard
#: model:ir.actions.server,name:privacy_lookup.ir_action_server_action_privacy_lookup_partner
#: model:ir.actions.server,name:privacy_lookup.ir_action_server_action_privacy_lookup_user
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
#, python-format
msgid "Privacy Lookup"
msgstr "개인 정보 조회"

#. module: privacy_lookup
#: model:ir.actions.act_window,name:privacy_lookup.action_privacy_lookup_wizard_line
msgid "Privacy Lookup Line"
msgstr "개인 정보 조회 내역"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_lookup_wizard
msgid "Privacy Lookup Wizard"
msgstr "개인 정보 조회 마법사"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_lookup_wizard_line
msgid "Privacy Lookup Wizard Line"
msgstr "개인 정보 조회 마법사 내역"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__resource_ref
msgid "Record"
msgstr "기록"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__records_description
msgid "Records Description"
msgstr "레코드 설명"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "References"
msgstr "참조"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_model_id
msgid "Related Document Model"
msgstr "관련 문서 모델"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_id
msgid "Resource ID"
msgstr "리소스 ID"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_name
msgid "Resource name"
msgstr "자원명"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Search References"
msgstr "참조 검색"

#. module: privacy_lookup
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "The record is already unlinked."
msgstr "레코드가 이미 연결 해제되었습니다."

#. module: privacy_lookup
#: code:addons/privacy_lookup/models/privacy_log.py:0
#, python-format
msgid "This email address is not valid (%s)"
msgstr "이 이메일 주소는 올바르지 않습니다 (%s)"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid ""
"This operation is irreversible. Do you wish to proceed to the record "
"deletion ?"
msgstr ""

#. module: privacy_lookup
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "Unarchived"
msgstr "보관 취소됨"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__wizard_id
msgid "Wizard"
msgstr "마법사"
