# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:12+0000\n"
"PO-Revision-Date: 2017-11-30 13:12+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Costa Rica) (https://www.transifex.com/odoo/teams/41243/es_CR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CR\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery
#: model:mail.template,report_name:delivery.mail_template_data_delivery_confirmation
msgid "${(object.name or '').replace('/','_')}"
msgstr ""

#. module: delivery
#: model:mail.template,subject:delivery.mail_template_data_delivery_confirmation
msgid ""
"${object.company_id.name} Delivery Order (Ref ${object.name or 'n/a' })"
msgstr ""

#. module: delivery
#: model:mail.template,subject:delivery.mail_template_data_delivery_notification
msgid "${object.subject}"
msgstr ""

#. module: delivery
#: model:mail.template,body_html:delivery.mail_template_data_delivery_confirmation
msgid ""
"<?xml version=\"1.0\"?>\n"
"<data><p>Dear ${object.partner_id.name},</p>\n"
"<p>We are glad to inform you that your order has been shipped.</p>\n"
"<p>\n"
"%if object.carrier_tracking_ref:\n"
"  Your tracking reference:\n"
"  %if object.carrier_tracking_url:\n"
"    <a href=\"${object.carrier_tracking_url}\" target=\"_blank\">${object.carrier_tracking_ref}</a>.\n"
"  %else:\n"
"    ${object.carrier_tracking_ref}.\n"
"  %endif\n"
"%endif\n"
"</p>\n"
"<p>Find your delivery order attached for more details.</p>\n"
"<p>Thank you,</p>\n"
"      </data>"
msgstr ""

#. module: delivery
#: model:mail.template,body_html:delivery.mail_template_data_delivery_notification
msgid ""
"<?xml version=\"1.0\"?>\n"
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"font-family:Arial,Helvetica,sans-serif; padding: 20px; background-color: #ededed\" summary=\"o_mail_notification\">\n"
"          <tbody>\n"
"            <!-- HEADER -->\n"
"            <tr>\n"
"              <td align=\"center\" style=\"min-width: 590px;\">\n"
"                <table width=\"650\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px;\">\n"
"                  <tr>\n"
"                    <td valign=\"middle\">\n"
"                      <span style=\"font-size:20px; color:white; font-weight: bold;\">${object.record_name}</span>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\">\n"
"                      <img src=\"/logo.png\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${user.company_id.name}\"/>\n"
"                    </td>\n"
"                  </tr>\n"
"                </table>\n"
"              </td>\n"
"            </tr>\n"
"            <!-- CONTENT -->\n"
"            <tr>\n"
"              <td align=\"center\" style=\"min-width: 590px;\">\n"
"                <table width=\"650\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px;\">\n"
"                  <tbody>\n"
"                    <td valign=\"top\" style=\"color: #555; font-size: 14px;\">${object.body | safe}</td>\n"
"                  </tbody>\n"
"                </table>\n"
"              </td>\n"
"            </tr>\n"
"            <!-- FOOTER -->\n"
"            <tr>\n"
"              <td align=\"center\" style=\"min-width: 590px;\">\n"
"                <table width=\"650\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px;\">\n"
"                  <tr>\n"
"                    <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">${user.company_id.name}<br/>${user.company_id.phone or ''}</td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                      % if user.company_id.email:\n"
"                      <a href=\"mailto:${user.company_id.email}\" style=\"text-decoration:none; color: white;\">${user.company_id.email}</a><br/>\n"
"                      % endif\n"
"                      % if user.company_id.website:\n"
"                        <a href=\"${user.company_id.website}\" style=\"text-decoration:none; color: white;\">${user.company_id.website}</a>\n"
"                      % endif\n"
"                    </td>\n"
"                  </tr>\n"
"                </table>\n"
"              </td>\n"
"            </tr>\n"
"            <tr>\n"
"              <td align=\"center\">Powered by <a href=\"https://www.odoo.com\">Odoo</a>.</td>\n"
"            </tr>\n"
"          </tbody>\n"
"        </table>\n"
"      "
msgstr ""

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:76
#, python-format
msgid ""
"<p class=\"oe_view_nocontent\">\n"
"                    Buy Odoo Enterprise now to get more providers.\n"
"                </p>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "<span>kg</span>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid "<strong>Carrier</strong>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>Tracking Number</strong>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid "<strong>Weight</strong>"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier_integration_level
msgid "Action while validating Delivery Orders"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_active
msgid "Active"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_amount
msgid "Amount"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier_amount
msgid ""
"Amount of the order to benefit from a free shipping, expressed in the "
"company currency"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Archived"
msgstr ""

#. module: delivery
#: selection:delivery.carrier,delivery_type:0
msgid "Based on Rules"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking_weight_bulk
msgid "Bulk Weight"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package_shipping_weight
msgid ""
"Can be changed during the 'put in pack' to adjust the weight of the "
"shipping."
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "Cancelar"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_carrier
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_carrier_id
#: model:ir.model.fields,field_description:delivery.field_product_packaging_package_carrier_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking_carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_tree
msgid "Carrier"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Check price"
msgstr ""

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid "Click to define a new delivery method."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_company_id
msgid "Company"
msgstr "Compañía"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Condition"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_res_partner
msgid "Contact"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_country_ids
msgid "Countries"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package_create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_create_uid
msgid "Created by"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package_create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_create_date
msgid "Created on"
msgstr "Creado en"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_debug_logging
msgid "Debug logging"
msgstr ""

#. module: delivery
#: model:ir.ui.menu,name:delivery.menu_delivery
msgid "Delivery"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Delivery Carrier"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Delivery Cost"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Delivery Information"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_delivery_message
msgid "Delivery Message"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_res_partner_property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_res_users_property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_sale_order_carrier_id
msgid "Delivery Method"
msgstr ""

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_carrier_form
#: model:ir.ui.menu,name:delivery.menu_action_delivery_carrier_form
#: model:ir.ui.menu,name:delivery.sale_menu_action_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.res_config_settings_view_form
msgid "Delivery Methods"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr ""

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_packaging_view
#: model:ir.ui.menu,name:delivery.menu_delivery_packagings
#: model_terms:ir.ui.view,arch_db:delivery.product_packaging_delivery_tree
msgid "Delivery Packages"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package_delivery_packaging_id
#: model_terms:ir.ui.view,arch_db:delivery.product_packaging_delivery_form
msgid "Delivery Packaging"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_price_rule
msgid "Delivery Price Rules"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_product_id
msgid "Delivery Product"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_delivery_rating_success
msgid "Delivery Rating Success"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Destination Availability"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier_sequence
msgid "Determine the display order"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package_display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_display_name
msgid "Display Name"
msgstr ""

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"Each carrier (e.g. UPS) can have several delivery methods (e.g.\n"
"                UPS Express, UPS Standard) with a set of pricing rules attached\n"
"                to each method."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_prod_environment
msgid "Environment"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/delivery_grid.py:50
#: code:addons/delivery/models/delivery_grid.py:109
#, python-format
msgid "Error: no matching grid."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_delivery_price
msgid "Estimated Delivery Price"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_sale_order_carrier_id
msgid "Fill this field if you plan to invoice the shipping based on picking."
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Filling this form allows you to filter delivery carriers according to the "
"delivery address of your customer."
msgstr ""

#. module: delivery
#: selection:delivery.carrier,delivery_type:0
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_fixed_price
msgid "Fixed Price"
msgstr ""

#. module: delivery
#: model:product.product,name:delivery.product_product_delivery
#: model:product.template,name:delivery.product_product_delivery_product_template
msgid "Free delivery charges"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_free_over
msgid "Free if order amount is above"
msgstr ""

#. module: delivery
#: selection:delivery.carrier,integration_level:0
msgid "Get Rate"
msgstr ""

#. module: delivery
#: selection:delivery.carrier,integration_level:0
msgid "Get Rate and Create Shipment"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Group By"
msgstr "Agrupar por"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_product_hs_code
#: model:ir.model.fields,field_description:delivery.field_product_template_hs_code
msgid "HS Code"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_packaging_height
msgid "Height"
msgstr ""

#. module: delivery
#: sql_constraint:product.packaging:0
msgid "Height must be positive"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_id
msgid "ID"
msgstr "ID"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier_free_over
msgid ""
"If the order total amount (shipping excluded) is above or equal to this "
"value, the customer benefits from a free shipping"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:125
#, python-format
msgid ""
"Info:\n"
"The shipping is free because the order amount exceeds %.2f.\n"
"(The actual shipping cost is: %.2f)"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Install more Providers"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_integration_level
msgid "Integration Level"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_invoice_shipping_on_delivery
msgid "Invoice Shipping on Delivery"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line_is_delivery
msgid "Is a Delivery"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package___last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier___last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule___last_update
msgid "Last Modified on"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package_write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_write_uid
msgid "Last Updated by"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package_write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_write_date
msgid "Last Updated on"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_packaging_length
msgid "Length"
msgstr ""

#. module: delivery
#: sql_constraint:product.packaging:0
msgid "Length must be positive"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier_debug_logging
msgid "Log requests in order to ease debugging"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Manage Package Type"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_margin
msgid "Margin"
msgstr ""

#. module: delivery
#: sql_constraint:delivery.carrier:0
msgid "Margin cannot be lower than -100%"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Margin on Rate"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_packaging_max_weight
msgid "Max Weight"
msgstr ""

#. module: delivery
#: sql_constraint:product.packaging:0
msgid "Max Weight must be positive"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_max_value
msgid "Maximum Value"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_product_packaging_max_weight
msgid "Maximum weight shippable in this packaging"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_name
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Name"
msgstr ""

#. module: delivery
#: selection:product.packaging,package_carrier_type:0
msgid "No carrier integration"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/sale_order.py:73
#, python-format
msgid "No carrier set for this order."
msgstr ""

#. module: delivery
#: code:addons/delivery/models/delivery_grid.py:100
#, python-format
msgid "No price rule matching this order; delivery cost cannot be computed."
msgstr ""

#. module: delivery
#: model:product.product,name:delivery.product_product_delivery_normal
#: model:product.template,name:delivery.product_product_delivery_normal_product_template
msgid "Normal Delivery Charges"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking_number_of_packages
msgid "Number of Packages"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_operator
msgid "Operator"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Package"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_packaging_shipper_package_code
msgid "Package Code"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:34
#: code:addons/delivery/models/stock_picking.py:124
#, python-format
msgid "Package Details"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking_package_ids
msgid "Packages"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_product_packaging
msgid "Packaging"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move_line
msgid "Packing Operation"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package_stock_quant_package_id
msgid "Physical Package"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_stock_quant_package
msgid "Physical Packages"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/sale_order.py:75
#, python-format
msgid ""
"Please use \"Check price\" in order to compute a shipping price for this "
"quotation."
msgstr ""

#. module: delivery
#: selection:delivery.price.rule,variable:0
#: selection:delivery.price.rule,variable_factor:0
msgid "Price"
msgstr "Precio"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_tree
msgid "Price Rules"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Pricing"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_price_rule_ids
msgid "Pricing Rules"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_delivery_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking_delivery_type
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Provider"
msgstr ""

#. module: delivery
#: selection:delivery.price.rule,variable:0
#: selection:delivery.price.rule,variable_factor:0
#: model:ir.model.fields,field_description:delivery.field_sale_order_line_product_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order
msgid "Quotation"
msgstr "Presupuesto"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_return_picking
msgid "Return Picking"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_list_base_price
msgid "Sale Base Price"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_list_price
msgid "Sale Price"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea pedido de venta"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Save"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Send Confirmation Email"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_sequence
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Set price"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier_prod_environment
msgid "Set to True if your credentials are certified for production."
msgstr ""

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:168
#, python-format
msgid ""
"Shipment sent to carrier %s for shipping with tracking number %s<br/>Cost: "
"%.2f %s"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking_carrier_price
msgid "Shipping Cost"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package_shipping_weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package_shipping_weight
msgid "Shipping Weight"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_product_product_hs_code
#: model:ir.model.fields,help:delivery.field_product_template_hs_code
msgid "Standardized code for international shipping and goods declaration"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_state_ids
msgid "States"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: delivery
#: model:product.product,name:delivery.product_product_delivery_poste
#: model:product.template,name:delivery.product_product_delivery_poste_product_template
msgid "The Poste"
msgstr ""

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"These methods allow to automatically compute the delivery price\n"
"                according to your settings; on the sales order (based on the\n"
"                quotation) or the invoice (based on the delivery orders)."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_res_partner_property_delivery_carrier_id
#: model:ir.model.fields,help:delivery.field_res_users_property_delivery_carrier_id
msgid "This delivery method will be used when invoicing from picking."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier_margin
msgid "This percentage will be added to the shipping price."
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking_carrier_tracking_ref
msgid "Tracking Reference"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking_carrier_tracking_url
msgid "Tracking URL"
msgstr ""

#. module: delivery
#: model:ir.model,name:delivery.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking_weight_uom_id
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_move_weight_uom_id
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for Weight"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking_weight_uom_id
msgid "Unit of measurement for Weight"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_variable
msgid "Variable"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule_variable_factor
msgid "Variable Factor"
msgstr ""

#. module: delivery
#: selection:delivery.price.rule,variable:0
#: selection:delivery.price.rule,variable_factor:0
#: model:ir.model.fields,field_description:delivery.field_stock_picking_volume
msgid "Volume"
msgstr ""

#. module: delivery
#: selection:delivery.price.rule,variable:0
#: selection:delivery.price.rule,variable_factor:0
#: model:ir.model.fields,field_description:delivery.field_stock_move_weight
#: model:ir.model.fields,field_description:delivery.field_stock_picking_weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package_weight
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr ""

#. module: delivery
#: selection:delivery.price.rule,variable:0
#: selection:delivery.price.rule,variable_factor:0
msgid "Weight * Volume"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_move_weight_uom_id
msgid "Weight Unit of Measure"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking_shipping_weight
msgid "Weight for Shipping"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_packaging_width
msgid "Width"
msgstr ""

#. module: delivery
#: sql_constraint:product.packaging:0
msgid "Width must be positive"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:206
#, python-format
msgid ""
"You are shipping different packaging types in the same shipment.\n"
"Packaging Types: %s"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/sale_order.py:71
#, python-format
msgid "You can add delivery price only on unconfirmed quotations."
msgstr ""

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:182
#, python-format
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_zip_from
msgid "Zip From"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier_zip_to
msgid "Zip To"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "e.g. UPS Express"
msgstr ""
