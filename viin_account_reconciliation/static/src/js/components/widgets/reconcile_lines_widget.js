/** @odoo-module **/


import fieldUtils from 'web.field_utils';
import { registry } from '@web/core/registry';
import session from 'web.session';
const { Component, useState, onWillUpdateProps, onWillStart, onWillDestroy } = owl;

//const { Component } = owl;


export class ReconcileLinesWidget extends Component {
    setup() {
        super.setup();
        // Initialize state here
        this.state = useState({
            move_lines: this.getInitialMoveLinesData()
        });
    }
    getInitialMoveLinesData() {
        // Assuming `this.props.record.data[this.props.name].move_lines` is your initial data source
        return this.props.record.data[this.props.name].move_lines || [];
    }
    getReconcileLinesData() {
        var move_lines = this.props.record.data[this.props.name].move_lines || [];
            console.log("this.props.name",this)
        for (var line in move_lines) {
            move_lines[line].amount_format = fieldUtils.format.monetary(
                move_lines[line].amount,
                undefined,
                {
                    currency: session.get_currency(move_lines[line].currency_id),
                }
            );
            move_lines[line].debit_format = fieldUtils.format.monetary(
                move_lines[line].debit,
                undefined,
                {
                    currency: session.get_currency(move_lines[line].company_currency_id),
                }
            );
            move_lines[line].credit_format = fieldUtils.format.monetary(
                move_lines[line].credit,
                undefined,
                {
                    currency: session.get_currency(move_lines[line].company_currency_id),
                }
            );
            if (move_lines[line].original_amount) {
                move_lines[line].original_amount_format = fieldUtils.format.monetary(
                    move_lines[line].original_amount,
                    undefined,
                    {
                        currency: session.get_currency(move_lines[line].company_currency_id),
                    }
                );
            }
            move_lines[line].date_format = fieldUtils.format.date(
                fieldUtils.parse.date(move_lines[line].date, undefined, {isUTC: true})
            );
        }
        return move_lines;
    }
    onTrashLine(ev, line) {
        this.props.record.update({
            manual_reference: line.reference,
            manual_delete: true,
        });
    }
    selectReconcileLine(ev, line) {
		var can_edit = line.reference.startsWith('reconcile_auxiliary');
        this.props.record.update({
            manual_reference: line.reference,
            can_edit: can_edit
        });
        const triggerEv = new CustomEvent('reconcile-page-navigate', {
            detail: {
                name: 'manual',
                originalEv: ev,
            },
        });
        this.env.bus.trigger('RECONCILE_PAGE_NAVIGATE', triggerEv);
    }

     /**
     * Check the journal items with the same currency.
	 * If the currency of the journal items is different from the company's currency, display the foreign currency amount.
	 * else do not display it.
     */
    isSameCurrency() {
        const recordData = this.props.record.data.reconcile_lines_widget;
        return recordData.move_lines.every(item => {
            return item.currency_id == item.company_currency_id
        })
    }
     onClickNewButton(ev) {
        // Example new line. Modify as needed or get data from a form/modal.
        const newLine = {
            amount: 200,
            debit: 100,
            credit: 2000,
            currency_id: session.company_currency_id,
            company_currency_id: session.company_currency_id,
            date: new Date().toISOString().slice(0, 10), // Current date
            account_id:  25 , // Example structure, adjust as necessary
            // Other fields...
        };

        this.props.record.data[this.props.name].move_lines.push(newLine);
console.log("this.getReconcileLinesData()",this.getReconcileLinesData())
        this.render();
    }
}
ReconcileLinesWidget.template = 'viin_account_reconciliation.ReconcileLinesWidget';

registry.category('fields').add('reconcile_lines', ReconcileLinesWidget);
