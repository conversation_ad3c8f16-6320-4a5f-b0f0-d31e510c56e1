<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="viin_account_reconciliation.ReconcileKanbanController" t-inherit="web.KanbanView" t-inherit-mode="primary" owl="1">
        <xpath expr="//Layout" position="attributes">
            <attribute name="className">model.useSampleModel ? 'o_view_sample_data o_account_reconcile_kanban_main' : 'o_account_reconcile_kanban_main'</attribute>
        </xpath>
        <xpath expr="//t[@t-component='props.Renderer']" position="attributes">
            <attribute name="selectedBankStatementLineId">state.selectedBankStatementLineId</attribute>
        </xpath>
        <xpath expr="//Layout" position="inside">
            <div class="o_account_reconcile_form_info">
                <t t-if="state.selectedBankStatementLineId">
                    <View t-props="accountReconcileFormInfo" t-key="state.selectedBankStatementLineId" />
                </t>
            </div>
        </xpath>
    </t>
    <t t-name="viin_account_reconciliation.ReconcileKanbanRenderer" t-inherit="web.KanbanRenderer" t-inherit-mode="primary" owl="1">
        <xpath expr="//div[@t-ref='root']" position="attributes">
            <attribute name="class" add="o_account_reconcile_kanban_div" separator=" "/>
        </xpath>
        <xpath expr="//KanbanRecord[not(@group)]" position="attributes">
            <attribute name="selectedBankStatementLineId">props.selectedBankStatementLineId</attribute>
        </xpath>
    </t>
    <t t-name="viin_account_reconciliation.ReconcileKanbanView.Buttons" owl="1">
        <button t-on-click="onClickNewButton" class="btn btn-primary" t-if="activeActions.create">Create</button>
    </t>
</templates>
