# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ohrms_salary_advance
# 	* hr_multi_company
# 	* ohrms_service_request
# 	* oh_employee_creation_from_user
# 	* hr_reward_warning
# 	* ohrms_loan
# 	* hr_leave_request_aliasing
# 	* oh_appraisal
# 	* oh_employee_documents_expiry
# 	* ohrms_loan_accounting
# 	* hr_gratuity_settlement
# 	* uae_wps_report
# 	* ohrms_core
# 	* oh_hr_lawsuit_management
# 	* oh_employee_check_list
# 	* hr_insurance
# 	* hr_employee_transfer
# 	* hr_employee_shift
# 	* saudi_gosi
# 	* oh_hr_zk_attendance
# 	* ohrmspro_holidays_approval
# 	* hr_vacation_mngmt
# 	* hr_reminder
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-02-12 09:57+0000\n"
"PO-Revision-Date: 2020-02-12 09:57+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,help:oh_employee_documents_expiry.field_hr_employee_document__notification_type
msgid ""
"\n"
"        Notification on expiry date: You will get notification only on expiry date.\n"
"        Notification before few days: You will get notification in 2 days.On expiry date and number of days before date.\n"
"        Everyday till expiry date: You will get notification on everyday till the expiry date of the document.\n"
"        Notification on and after expiry: You will get notification on the expiry date and continues upto Days.\n"
"        If you did't select any then you will get notification before 7 days of document expiry."
msgstr ""
"\n"
"        الإخطار في تاريخ انتهاء الصلاحية: سوف تتلقى إشعارًا فقط في تاريخ انتهاء الصلاحية.\n"
"        إشعار قبل أيام قليلة: ستتلقى إشعارًا خلال يومين. في تاريخ انتهاء الصلاحية وعدد الأيام قبل التاريخ.\n"
"        كل يوم حتى تاريخ انتهاء الصلاحية: سوف تتلقى إشعارًا يوميًا حتى تاريخ انتهاء صلاحية المستند.\n"
"        إشعار عند انتهاء الصلاحية وبعده: ستتلقى إشعارًا بتاريخ انتهاء الصلاحية وتستمر حتى أيام.\n"
"        إذا لم تختر أيًا ، فستتلقى إشعارًا قبل 7 أيام من انتهاء صلاحية المستند."

#. module: hr_vacation_mngmt
#: model:mail.template,body_html:hr_vacation_mngmt.email_template_hr_leave_reminder_mail
msgid ""
"\n"
"<p>Hello ,</p>\n"
"<p>The employee <strong>${object.employee_id.name}</strong> has taken <strong>${object.no_of_days_temp}</strong> days leave starting from <strong>${object.date_from}</strong> to <strong>${object.date_to}</strong>.</p>\n"
"\n"
"<p>Kindly do the needful.</p>\n"
"\n"
"</p><p>Thank you!</p>\n"
msgstr ""
"\n"
"<p>، مرحبا</p>\n"
"<p>الموظف <strong>${object.employee_id.name}</strong> تم أخذه <strong>${object.no_of_days_temp}</strong> أيام إجازة تبدأ من <strong>${object.date_from}</strong> إلى <strong>${object.date_to}</strong>.</p>\n"
"\n"
"<p>.قم بالمطلوب رجاءا</p>\n"
"\n"
"</p><p>!شكرا لكم</p>\n"

#. module: hr_employee_transfer
#: model:ir.model.fields,help:hr_employee_transfer.field_employee_transfer__state
msgid ""
" * The 'Draft' status is used when a transfer is created and unconfirmed Transfer.\n"
" * The 'Transferred' status is used when the user confirm the transfer. It stays in the open status till the other branch/company receive the employee.\n"
" * The 'Done' status is set automatically when the employee is Joined/Received.\n"
" * The 'Cancelled' status is used when user cancel Transfer."
msgstr ""
" * يتم استخدام حالة 'المسودة' عند إنشاء عملية نقل ونقل غير مؤكد.\n"
" * يتم استخدام الحالة 'المنقولة' عندما يؤكد المستخدم عملية النقل. يبقى في وضع مفتوح حتى يستقبل الفرع / الشركة الأخرى الموظف.\n"
" * يتم تعيين حالة 'تم' تلقائيًا عند انضمام / تلقي الموظف.\n"
" * يتم استخدام حالة 'تم الإلغاء' عندما يقوم المستخدم بإلغاء النقل."
#. module: hr_reward_warning
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_employee__announcement_count
msgid "# Announcements"
msgstr "الإعلانات #"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee__document_count
msgid "# Documents"
msgstr "مستندات#"

#. module: oh_hr_lawsuit_management
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_employee__legal_count
msgid "# Legal Actions"
msgstr "اجراءات قانونية #"

#. module: oh_employee_documents_expiry
#: code:addons/oh_employee_documents_expiry/models/employee_documents.py:0
#, python-format
msgid ""
"<p class=\"oe_view_nocontent_create\">\n"
"                           Click to Create for New Documents\n"
"                        </p>"
msgstr ""
"<p class=\"oe_view_nocontent_create\">\n"
"                           انقر لإنشاء للمستندات الجديدة\n"
"                        </p>"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
msgid ""
"<span attrs=\"{'invisible': [('wage_type', '=', 'hourly')]}\">/ month</span>\n"
"                                    <span attrs=\"{'invisible': [('wage_type', '!=', 'hourly')]}\">/ hour</span>"
msgstr ""
"<span attrs=\"{'invisible': [('wage_type', '=', 'hourly')]}\">/ شهر</span>\n"
"                                    <span attrs=\"{'invisible': [('wage_type', '!=', 'hourly')]}\">/ ساعة</span>"
#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.gratuity_configuration_form_view
msgid "<span class=\"oe_inline\">-days gratuity pay.</span>"
msgstr "<span class=\"oe_inline\">.أيام دفع مكافأ-</span>"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.gratuity_configuration_form_view
msgid "<span class=\"oe_inline\">Employee is entitled to</span>"
msgstr "<span class=\"oe_inline\">يحق للموظف</span>"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.gratuity_configuration_form_view
msgid "<span class=\"oe_inline\">of the</span>"
msgstr "<span class=\"oe_inline\">من</span>"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
msgid "<span> years</span>"
msgstr "<span> سنوات</span>"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.hr_contract_form_inherit_wage
msgid "<span>/ hour</span>"
msgstr "<span>ساعة /</span>"

#. module: hr_employee_transfer
#: model:ir.model.fields,help:hr_employee_transfer.field_employee_transfer__sequence_number
msgid "A unique sequence number for the Transfer"
msgstr "رقم تسلسل فريد للنقل"

#. module: saudi_gosi
#: model:ir.model.fields,field_description:saudi_gosi.field_hr_employee__age
msgid "AGE"
msgstr "عمر"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
msgid "Account Details"
msgstr "تفاصيل الحساب"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
msgid "Accounting"
msgstr "محاسبة"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
msgid "Accounting Configuration"
msgstr "التكوين المحاسبي"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
msgid "Accounting Configuration Menu"
msgstr "قائمة التكوين المحاسبة"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_needaction
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_needaction
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_needaction
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_needaction
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_needaction
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_needaction
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_needaction
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_needaction
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_needaction
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_needaction
msgid "Action Needed"
msgstr "الإجراءات اللازمة"

#. modules: hr_gratuity_settlement, hr_reminder, hr_insurance
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__active
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__active
#: model:ir.model.fields.selection,name:hr_insurance.selection__hr_insurance__state__active
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__active
msgid "Active"
msgstr "نشيط"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__activity_ids
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__activity_ids
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__activity_ids
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__activity_ids
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__activity_ids
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__activity_ids
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__activity_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__activity_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__activity_ids
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__activity_ids
msgid "Activities"
msgstr "أنشطة"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__activity_exception_decoration
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__activity_exception_decoration
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__activity_exception_decoration
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__activity_exception_decoration
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__activity_exception_decoration
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__activity_exception_decoration
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__activity_exception_decoration
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "نشاط استثناء الديكور"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__activity_state
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__activity_state
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__activity_state
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__activity_state
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__activity_state
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__activity_state
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__activity_state
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__activity_state
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__activity_state
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: ohrms_service_request
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__service_type__adjust
msgid "Adjustment"
msgstr "تعديل"

#. module: ohrms_salary_advance
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__advance
#: model:ir.ui.menu,name:ohrms_salary_advance.parent_menu_salary_advance
msgid "Advance"
msgstr "تقدم"

#. module: ohrms_salary_advance
#: model:hr.salary.rule,name:ohrms_salary_advance.hr_payslip_rule_advance
msgid "Advance Salary"
msgstr "راتب مقدما"

#. module: ohrms_core
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Advanced Features"
msgstr "الخيارات المتقدمة"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.hr_contract_form_inherit_wage
msgid "Advantages..."
msgstr "...مزايا"

#. module: oh_appraisal
#: model:ir.model.fields,help:oh_appraisal.field_hr_appraisal__final_interview
msgid "After sending survey link,you can schedule final interview date"
msgstr "بعد إرسال رابط الاستطلاع ، يمكنك تحديد موعد المقابلة النهائية"

#. module: uae_wps_report
#: model:ir.model.fields,field_description:uae_wps_report.field_hr_employee__agent_id
msgid "Agent/Bank"
msgstr "وكيل / البنك"

#. module: hr_leave_request_aliasing
#: model:ir.model.fields,field_description:hr_leave_request_aliasing.field_res_config_settings__alias_domain
msgid "Alias Domain"
msgstr "لقب النطاق"

#. modules: hr_gratuity_settlement, ohrms_loan
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__amount
msgid "Amount"
msgstr "كمية"

#. module: hr_reward_warning
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_search
msgid "Announcement Reason"
msgstr "سبب الإعلان"

#. module: hr_reward_warning
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__announcement_type
msgid "Announcement Type"
msgstr "نوع الإعلان"

#. module: hr_reward_warning
#: code:addons/hr_reward_warning/models/hr_employee.py:0
#: code:addons/hr_reward_warning/models/hr_employee.py:0
#: model:ir.actions.act_window,name:hr_reward_warning.action_hr_announcement
#: model:ir.ui.menu,name:hr_reward_warning.hr_announcement
#: model:ir.ui.menu,name:hr_reward_warning.hr_announcement_sub_menu
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.announcement_inherit_form_view
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_form
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_search
#, python-format
msgid "Announcements"
msgstr "الإعلانات"

#. module: oh_appraisal
#: model:ir.ui.menu,name:oh_appraisal.menu_hr_appraisal_answers
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_form_view
msgid "Answers"
msgstr "الأجوبة"

#. module: oh_appraisal
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_kanban
msgid "Answers:"
msgstr ":الأجوبة"

#. module: oh_appraisal
#: model:ir.actions.act_window,name:oh_appraisal.hr_appraisal_action_form
#: model:ir.model,name:oh_appraisal.model_hr_appraisal
#: model:ir.module.category,name:oh_appraisal.module_category_hr_appraisal
#: model:ir.ui.menu,name:oh_appraisal.menu_hr_appraisal
#: model:ir.ui.menu,name:oh_appraisal.menu_hr_appraisal_root
msgid "Appraisal"
msgstr "توصيه"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__appraisal_deadline
msgid "Appraisal Deadline"
msgstr "تقييم الموعد النهائي"

#. module: oh_appraisal
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_form_view
msgid "Appraisal Form"
msgstr "استمارة التقييم"

#. module: oh_appraisal
#: model:ir.model,name:oh_appraisal.model_hr_appraisal_stages
msgid "Appraisal Stages"
msgstr "مراحل التقييم"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_survey_user_input__appraisal_id
msgid "Appriasal id"
msgstr "معرف التقييم"

#. module: ohrmspro_holidays_approval
#: model:ir.ui.menu,name:ohrmspro_holidays_approval.menu_open_leave_approvals
msgid "Approval Requests"
msgstr "طلبات الموافقة"

#. module: ohrms_loan_accounting
#: model:ir.model.fields,field_description:ohrms_loan_accounting.field_res_config_settings__loan_approve
msgid "Approval from Accounting Department"
msgstr "موافقة من قسم المحاسبة"

#. modules: hr_gratuity_settlement, ohrmspro_holidays_approval
#: model:ir.ui.menu,name:hr_gratuity_settlement.menu_hr_employee
#: code:addons/ohrmspro_holidays_approval/models/leave_request.py:0
#: model:ir.actions.server,name:ohrmspro_holidays_approval.open_holidays_to_approve
#: model:ir.ui.menu,name:ohrmspro_holidays_approval.menu_hr_holidays_leave_approvals
#, python-format
msgid "Approvals"
msgstr "الموافقات"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_loan_accounting, ohrms_service_request, ohrmspro_holidays_approval,
#. hr_reward_warning, ohrms_loan
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_priority
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_form
#: model_terms:ir.ui.view,arch_db:ohrms_loan.hr_loan_form_view
#: model_terms:ir.ui.view,arch_db:ohrms_loan_accounting.hr_loan_inherited
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_form
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_request11
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_validators
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_validators_leave_create
msgid "Approve"
msgstr "يوافق"

#. module: ohrmspro_holidays_approval
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status__validation_status
msgid "Approve Status"
msgstr "الموافقة على الحالة"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_loan_accounting, ohrms_service_request, hr_reward_warning, ohrms_loan
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_gratuity__state__approve
#: model:ir.model.fields.selection,name:hr_reward_warning.selection__hr_announcement__state__approved
#: model:ir.model.fields.selection,name:ohrms_loan.selection__hr_loan__state__approve
#: model:ir.model.fields.selection,name:ohrms_loan_accounting.selection__hr_loan__state__approve
#: model:ir.model.fields.selection,name:ohrms_salary_advance.selection__salary_advance__state__approve
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_execute__state_execute__approved
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__state__approved
msgid "Approved"
msgstr "وافق"

#. module: hr_reward_warning
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_search
msgid "Approved Letters"
msgstr "رسائل معتمدة"

#. module: ohrms_salary_advance
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
msgid "Approved Requests"
msgstr "الطلبات المعتمدة"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__04
msgid "April"
msgstr "أبريل"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.hr_gratuity_accounting_configuration_filter_view
msgid "Archived"
msgstr "من الأرشيف"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.hr_gratuity_accounting_configuration_filter_view
msgid "Archived Gratuity Configuration"
msgstr "أرشفة التكوين التكوين"

#. module: oh_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_machine_form
msgid "Are you sure you want to do this?"
msgstr "هل انت متأكد من أنك تريد أن تفعل هذا؟"

#. module: ohrms_service_request
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_request11
msgid "Assign"
msgstr "تعيين"

#. module: ohrms_service_request
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_execute__state_execute__assign
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__state__assign
msgid "Assigned"
msgstr "تعيين"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__assigned_to
msgid "Assigned to"
msgstr "مخصص ل"

#. modules: hr_reward_warning, oh_employee_documents_expiry
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__attachment_id
#: model:ir.model,name:oh_employee_documents_expiry.model_ir_attachment
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__doc_attachment_id
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_ir_attachment__doc_attach_rel
msgid "Attachment"
msgstr "مرفق"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_attachment_count
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_attachment_count
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_attachment_count
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_attachment_count
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_attachment_count
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_attachment_count
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_attachment_count
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_attachment_count
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_attachment_count
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_attachment_count
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. modules: hr_multi_company, oh_hr_zk_attendance
#: model:ir.model,name:hr_multi_company.model_hr_attendance
#: model:ir.model,name:oh_hr_zk_attendance.model_hr_attendance
msgid "Attendance"
msgstr "الحضور"

#. module: oh_hr_zk_attendance
#: model:ir.actions.act_window,name:oh_hr_zk_attendance.action_zk_report_daily_attendance
msgid "Attendance Analysis"
msgstr "تحليل الحضور"

#. module: oh_hr_zk_attendance
#: model:ir.ui.menu,name:oh_hr_zk_attendance.menu_zk_attendance_view
msgid "Attendance log"
msgstr "سجل الحضور"

#. module: oh_hr_zk_attendance
#: model:ir.actions.act_window,name:oh_hr_zk_attendance.zk_machine_action
msgid "Attendances"
msgstr "الحضور"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__08
msgid "August"
msgstr "أغسطس"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__balance_amount
msgid "Balance Amount"
msgstr "مقدار وسطي"

#. module: uae_wps_report
#: model:ir.model,name:uae_wps_report.model_res_bank
msgid "Bank"
msgstr "البنك"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_leave__leave_salary__0
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__res_config_settings__default_leave_salary__0
msgid "Basic"
msgstr "قاعدي"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__employee_basic_salary
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
msgid "Basic Salary"
msgstr "راتب اساسي"

#. module: oh_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_machine_form
msgid "Biometric Device"
msgstr "جهاز القياس الحيوي"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_hr_attendance__device_id
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_hr_employee__device_id
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__device_id
msgid "Biometric Device ID"
msgstr "معرف الجهاز البيومترية"

#. module: ohrms_core
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Biometric Device Integration"
msgstr "تكامل الأجهزة البيومترية"

#. module: oh_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_machine_tree
msgid "Biometric Machine"
msgstr "آلة القياس الحيوي"

#. module: oh_hr_zk_attendance
#: model:ir.ui.menu,name:oh_hr_zk_attendance.zk_machine_menu
msgid "Biometric Manager"
msgstr "مدير البيومترية"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_vacation.py:0
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.hr_vacation_form_view
#, python-format
msgid "Book Flight Ticket"
msgstr "حجز تذكرة طيران"

#. module: hr_vacation_mngmt
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_book_flight_ticket_form
msgid "Book Ticket"
msgstr "حجز تذكرة"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__state__booked
msgid "Booked"
msgstr "حجز"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__punch_type__3
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__punch_type__3
msgid "Break In"
msgstr "يقتحم"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__punch_type__2
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__punch_type__2
msgid "Break Out"
msgstr "انطلق"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__ticket_class__business
msgid "Business"
msgstr "اعمال"

#. module: hr_reward_warning
#: model:ir.model.fields.selection,name:hr_reward_warning.selection__hr_announcement__announcement_type__department
msgid "By Department"
msgstr "من قبل وزارة"

#. module: hr_reward_warning
#: model:ir.model.fields.selection,name:hr_reward_warning.selection__hr_announcement__announcement_type__employee
msgid "By Employee"
msgstr "بواسطة الموظف"

#. module: hr_reward_warning
#: model:ir.model.fields.selection,name:hr_reward_warning.selection__hr_announcement__announcement_type__job_position
msgid "By Job Position"
msgstr "حسب الوظيفة"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_loan_accounting, uae_wps_report, hr_vacation_mngmt,
#. oh_hr_lawsuit_management, hr_employee_transfer, ohrms_loan,
#. hr_employee_shift
#: model_terms:ir.ui.view,arch_db:hr_employee_shift.generate_schedule_form
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.employee_transfer
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.reassign_task_form
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_book_flight_ticket_form
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_flight_ticket_form
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_form_view
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.hr_lawsuit_form_view
#: model_terms:ir.ui.view,arch_db:ohrms_loan.hr_loan_form_view
#: model_terms:ir.ui.view,arch_db:ohrms_loan_accounting.hr_loan_inherited
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_form
#: model_terms:ir.ui.view,arch_db:uae_wps_report.wps_wizard_form
msgid "Cancel"
msgstr "إلغاء"

#. modules: hr_vacation_mngmt, ohrms_loan_accounting, ohrms_loan
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__state__canceled
#: model:ir.model.fields.selection,name:ohrms_loan.selection__hr_loan__state__cancel
#: model:ir.model.fields.selection,name:ohrms_loan_accounting.selection__hr_loan__state__cancel
msgid "Canceled"
msgstr "ملغي"

#. modules: hr_employee_transfer, oh_hr_lawsuit_management,
#. ohrms_salary_advance, hr_gratuity_settlement
#: model:ir.model.fields.selection,name:hr_employee_transfer.selection__employee_transfer__state__cancel
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_contract__state__cancel
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_gratuity__state__cancel
#: model:ir.model.fields.selection,name:oh_hr_lawsuit_management.selection__hr_lawsuit__state__cancel
#: model:ir.model.fields.selection,name:ohrms_salary_advance.selection__salary_advance__state__cancel
msgid "Cancelled"
msgstr "ملغي"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__4
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__attendance_type__4
msgid "Card"
msgstr "بطاقة"

#. module: oh_hr_lawsuit_management
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__case_details
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.hr_lawsuit_form_view
msgid "Case Details"
msgstr "تفاصيل الحالة"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__attendance_type
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_report_daily_attendance__attendance_type
msgid "Category"
msgstr "الفئة"

#. module: ohrmspro_holidays_approval
#: code:addons/ohrmspro_holidays_approval/models/leave_request.py:0
#, python-format
msgid ""
"Changing leave validators is not permitted. You can only change it from "
"Leave Types Configuration"
msgstr ""
"تغيير إجازة المدققين غير مسموح به. يمكنك فقط تغييره من "
"ترك تكوين الأنواع"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__check_cancel
msgid "Check Cancel"
msgstr "تحقق إلغاء"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__check_done
msgid "Check Done"
msgstr "تحقق من"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__check_draft
msgid "Check Draft"
msgstr "تحقق مشروع"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__check_in
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__punch_type__0
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__punch_type__0
msgid "Check In"
msgstr "تحقق في"

#. module: oh_employee_check_list
#: model:ir.model.fields,field_description:oh_employee_check_list.field_hr_employee__check_list_enable
msgid "Check List Enable"
msgstr "تحقق من قائمة تمكين"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__check_out
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__punch_type__1
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__punch_type__1
msgid "Check Out"
msgstr "الدفع"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__check_sent
msgid "Check Sent Mail"
msgstr "تحقق البريد المرسلة"

#. module: ohrms_service_request
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_execute__state_execute__check
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__state__check
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_execute2
msgid "Checked"
msgstr "التحقق"

#. module: ohrms_service_request
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__service_type__checking
msgid "Checking"
msgstr "تدقيق"

#. module: oh_employee_check_list
#: model_terms:ir.ui.view,arch_db:oh_employee_check_list.employee_check_list_form_view
#: model_terms:ir.ui.view,arch_db:oh_employee_check_list.hr_employee_inherit_form_view
msgid "Checklist"
msgstr "قائمة تدقيق"

#. module: oh_employee_check_list
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__document_type
msgid "Checklist Type"
msgstr "نوع قائمة التحقق"

#. module: hr_vacation_mngmt
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_leave_configuration
msgid ""
"Choose the expence account to post the flight tickets accounting entries"
msgstr ""
"اختر حساب المصاريف لنشر إدخالات محاسبة تذاكر الطيران"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__ticket_class
msgid "Class"
msgstr "فئة"

#. module: oh_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_machine_form
msgid "Clear Data"
msgstr "امسح البيانات"

#. module: hr_reminder
#: model_terms:ir.actions.act_window,help:hr_reminder.action_hr_reminder
msgid "Click here to configure new periodic reminder."
msgstr ".انقر هنا لتكوين تذكير دوري جديد"

#. modules: hr_reward_warning, oh_hr_lawsuit_management
#: model_terms:ir.actions.act_window,help:hr_reward_warning.action_hr_announcement
#: model_terms:ir.actions.act_window,help:oh_hr_lawsuit_management.action_hr_lawsuit
msgid "Click to Create a New Record."
msgstr ".انقر لإنشاء سجل جديد"

#. module: oh_employee_check_list
#: model_terms:ir.actions.act_window,help:oh_employee_check_list.action_entry_checklist
msgid "Click to create a New Entry Checklist"
msgstr ".انقر لإنشاء قائمة تدقيق إدخال جديدة"

#. module: oh_employee_check_list
#: model_terms:ir.actions.act_window,help:oh_employee_check_list.action_exit_checklist
msgid "Click to create a New Exit Checklist"
msgstr "انقر لإنشاء قائمة تدقيق خروج جديدة"

#. module: hr_employee_transfer
#: model_terms:ir.actions.act_window,help:hr_employee_transfer.action_employee_transfer
msgid "Click to create a new Employee Transfer."
msgstr ".انقر لإنشاء نقل موظف جديد"

#. module: ohrms_loan
#: model_terms:ir.actions.act_window,help:ohrms_loan.action_hr_loan_request
msgid "Click to create a new Loan request."
msgstr ".انقر لإنشاء طلب قرض جديد"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__client
msgid "Client"
msgstr "زبون"

#. module: oh_hr_lawsuit_management
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__name
msgid "Code"
msgstr "الشفرة"

#. module: hr_reward_warning
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__name
msgid "Code No:"
msgstr ":رقم الكود"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__hr_colloborator
msgid "Collaborators"
msgstr "المتعاونين"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__hr_colleague
msgid "Colleague"
msgstr "زميل"

#. modules: oh_appraisal, hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_resource_calendar__color
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__color
msgid "Color Index"
msgstr "مؤشر اللون"

#. module: hr_insurance
#: model:ir.model.fields,field_description:hr_insurance.field_insurance_policy__note_field
msgid "Comment"
msgstr "تعليق"

#. module: ohrmspro_holidays_approval
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status__leave_comments
msgid "Comments"
msgstr "تعليقات"

#. modules: hr_employee_transfer, uae_wps_report
#: model:ir.model,name:hr_employee_transfer.model_res_company
#: model:ir.model,name:uae_wps_report.model_res_company
msgid "Companies"
msgstr "الشركات"

#. modules: hr_gratuity_settlement, ohrms_loan, oh_appraisal,
#. ohrms_salary_advance, hr_multi_company, oh_hr_zk_attendance,
#. hr_reward_warning, hr_vacation_mngmt, oh_hr_lawsuit_management,
#. hr_insurance, hr_employee_transfer, hr_reminder, hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate__company_id
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__company_id
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__company_id
#: model:ir.model.fields,field_description:hr_employee_transfer.field_hr_contract__company_id
#: model:ir.model.fields,field_description:hr_employee_transfer.field_transfer_company__company_id
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__company_id
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__company_id
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__company_id
#: model:ir.model.fields,field_description:hr_insurance.field_insurance_policy__company_id
#: model:ir.model.fields,field_description:hr_multi_company.field_hr_attendance__company_id
#: model:ir.model.fields,field_description:hr_multi_company.field_hr_leave__company_id
#: model:ir.model.fields,field_description:hr_multi_company.field_hr_payslip_run__company_id
#: model:ir.model.fields,field_description:hr_multi_company.field_hr_salary_rule_category__company_id
#: model:ir.model.fields,field_description:hr_multi_company.field_zk_machine_attendance__company_id
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__company_id
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__company_id
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__company_id
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__company_id
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__company_id
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine__company_id
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__company_id
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__company_id
msgid "Company"
msgstr "الشركة"

#. module: hr_insurance
#: model:ir.model.fields,field_description:hr_insurance.field_hr_employee__insurance_percentage
msgid "Company Percentage "
msgstr "نسبة الشركة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_contract__company_country_id
msgid "Company country"
msgstr "بلد الشركة"

#. module: hr_employee_transfer
#: model:ir.model.fields,help:hr_employee_transfer.field_transfer_company__company_id
msgid "Company name same as res.company"
msgstr "res.company اسم الشركة مثل"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__state__completed
msgid "Completed"
msgstr "منجز"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.gratuity_configuration_form_view
msgid "Computations Details"
msgstr "تفاصيل الحسابات"

#. module: ohrms_loan
#: model_terms:ir.ui.view,arch_db:ohrms_loan.hr_loan_form_view
msgid "Compute Installment"
msgstr "حساب القسط"

#. modules: hr_vacation_mngmt, ohrms_loan_accounting, ohrms_core,
#. hr_leave_request_aliasing
#: model:ir.model,name:hr_leave_request_aliasing.model_res_config_settings
#: model:ir.model,name:hr_vacation_mngmt.model_res_config_settings
#: model:ir.model,name:ohrms_core.model_res_config_settings
#: model:ir.model,name:ohrms_loan_accounting.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الإعدادات"

#. module: hr_employee_shift
#: model:ir.ui.menu,name:hr_employee_shift.shift_configuration
msgid "Configuration"
msgstr "ترتيب"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__employee_gratuity_duration
msgid "Configuration Line"
msgstr "خط التكوين"

#. module: uae_wps_report
#: code:addons/uae_wps_report/wizard/wizard.py:0
#, python-format
msgid "Configure Your Bank In Accounting Dashboard"
msgstr "تكوين البنك الخاص بك في لوحة القيادة المحاسبة"

#. module: uae_wps_report
#: code:addons/uae_wps_report/wizard/wizard.py:0
#, python-format
msgid "Configure Your Company Employer ID"
msgstr "تكوين معرف صاحب العمل لشركتك"

#. module: hr_vacation_mngmt
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.reassign_task_form
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_flight_ticket_form
msgid "Confirm"
msgstr "تؤكد"

#. module: hr_vacation_mngmt
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.reassign_task_form
msgid "Confirm leave request and reassign pending works of the employee."
msgstr ".تأكيد طلب الإجازة وإعادة تعيينه في انتظار أعمال الموظف"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__state__confirmed
msgid "Confirmed"
msgstr "تم تأكيد"

#. modules: hr_employee_transfer, ohrms_salary_advance
#: code:addons/hr_employee_transfer/models/employee_transfer.py:0
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__employee_contract_id
#, python-format
msgid "Contract"
msgstr "عقد"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__employee_contract_type
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__config_contract_type
msgid "Contract Type"
msgstr "نوع العقد"

#. module: hr_gratuity_settlement
#: model:ir.actions.act_window,name:hr_gratuity_settlement.action_contract_approvals
msgid "Contracts For Approvals"
msgstr "عقود الموافقة"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__tot_comp_survey
msgid "Count Answers"
msgstr "عد الإجابات"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__tot_sent_survey
msgid "Count Sent Questions"
msgstr "عد الأسئلة المرسلة"

#. module: oh_hr_lawsuit_management
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__court_name
msgid "Court Name"
msgstr "اسم المحكمة"

#. module: hr_gratuity_settlement
#: model_terms:ir.actions.act_window,help:hr_gratuity_settlement.training_menu_action
msgid "Create Probation Details"
msgstr "إنشاء تفاصيل الاختبار"

#. module: ohrms_salary_advance
#: model_terms:ir.actions.act_window,help:ohrms_salary_advance.action_my_salary_advance
#: model_terms:ir.actions.act_window,help:ohrms_salary_advance.action_my_salary_advance_request_approved
#: model_terms:ir.actions.act_window,help:ohrms_salary_advance.action_salary_advance_to_approve
msgid "Create Requests."
msgstr ".إنشاء طلبات"

#. module: uae_wps_report
#: model:ir.ui.menu,name:uae_wps_report.wps_wizard_submenu
msgid "Create SIF"
msgstr "SIF إنشاء"

#. module: ohrms_service_request
#: model_terms:ir.actions.act_window,help:ohrms_service_request.action_view_service_requests
msgid "Create new Request"
msgstr "إنشاء طلب جديد"

#. modules: ohrms_service_request, saudi_gosi
#: model_terms:ir.actions.act_window,help:ohrms_service_request.action_view_service_approve
#: model_terms:ir.actions.act_window,help:ohrms_service_request.action_view_service_check
#: model_terms:ir.actions.act_window,help:saudi_gosi.action_view_employee_gosi
msgid "Create new record"
msgstr "إنشاء سجل جديد"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__created_by
msgid "Created By"
msgstr "انشأ من قبل"

#. modules: hr_gratuity_settlement, ohrms_loan, oh_appraisal,
#. oh_employee_documents_expiry, ohrms_salary_advance, oh_hr_zk_attendance,
#. uae_wps_report, ohrms_service_request, ohrmspro_holidays_approval,
#. hr_reward_warning, hr_vacation_mngmt, oh_employee_check_list,
#. oh_hr_lawsuit_management, hr_insurance, hr_employee_transfer, hr_reminder,
#. hr_employee_shift, saudi_gosi
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate__create_uid
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__create_uid
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__create_uid
#: model:ir.model.fields,field_description:hr_employee_transfer.field_transfer_company__create_uid
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__create_uid
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__create_uid
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__create_uid
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__create_uid
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__create_uid
#: model:ir.model.fields,field_description:hr_insurance.field_insurance_policy__create_uid
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__create_uid
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__create_uid
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__create_uid
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__create_uid
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_task_reassign__create_uid
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__create_uid
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal_stages__create_uid
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__create_uid
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_document_type__create_uid
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__create_uid
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__create_uid
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine__create_uid
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__create_uid
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__create_uid
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__create_uid
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__create_uid
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__create_uid
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__create_uid
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_holidays_validators__create_uid
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status__create_uid
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__create_uid
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__create_uid
msgid "Created by"
msgstr "انشأ من قبل"

#. modules: hr_gratuity_settlement, ohrms_loan, oh_appraisal,
#. oh_employee_documents_expiry, ohrms_salary_advance, oh_hr_zk_attendance,
#. uae_wps_report, ohrms_service_request, ohrmspro_holidays_approval,
#. hr_reward_warning, hr_vacation_mngmt, oh_employee_check_list,
#. oh_hr_lawsuit_management, hr_insurance, hr_employee_transfer, hr_reminder,
#. hr_employee_shift, saudi_gosi
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate__create_date
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__create_date
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__create_date
#: model:ir.model.fields,field_description:hr_employee_transfer.field_transfer_company__create_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__create_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__create_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__create_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__create_date
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__create_date
#: model:ir.model.fields,field_description:hr_insurance.field_insurance_policy__create_date
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__create_date
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__create_date
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__create_date
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__create_date
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_task_reassign__create_date
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__create_date
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal_stages__create_date
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__create_date
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_document_type__create_date
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__create_date
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__create_date
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine__create_date
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__create_date
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__create_date
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__create_date
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__create_date
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__create_date
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__create_date
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_holidays_validators__create_date
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status__create_date
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__create_date
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__create_date
msgid "Created on"
msgstr "تم إنشاؤها على"

#. modules: hr_gratuity_settlement, ohrms_salary_advance
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__credit
msgid "Credit Account"
msgstr "حساب الائتمان"

#. modules: hr_gratuity_settlement, ohrms_salary_advance, ohrms_loan
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__currency_id
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__currency_id
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__currency_id
msgid "Currency"
msgstr "عملة"

#. module: oh_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_report_daily_attendance_search
msgid "Current Month"
msgstr "الشهر الحالي"

#. module: ohrms_core
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Custody Management"
msgstr "إدارة الحراسة"

#. modules: ohrms_salary_advance, oh_hr_zk_attendance, uae_wps_report,
#. oh_hr_lawsuit_management, hr_employee_transfer, ohrms_loan
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__date
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__requested_date
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_report_daily_attendance__punching_day
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.inherited_hr_attendance_view_filter
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__date
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__date
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__date
msgid "Date"
msgstr "تاريخ"

#. modules: hr_employee_shift, hr_insurance
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__start_date
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__date_from
msgid "Date From"
msgstr "التاريخ من"

#. module: saudi_gosi
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__dob
msgid "Date Of Birth"
msgstr "تاريخ الولادة"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__execute_date
msgid "Date Of Reporting"
msgstr "تاريخ الإبلاغ"

#. modules: hr_employee_shift, hr_insurance
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__end_date
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__date_to
msgid "Date To"
msgstr "تاريخ ل"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__before_days
msgid "Days"
msgstr "أيام"

#. module: hr_vacation_mngmt
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_leave_configuration
msgid "Days Before"
msgstr "قبل أيام"

#. module: uae_wps_report
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__days
msgid "Days of Payment"
msgstr "أيام الدفع"

#. module: oh_appraisal
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_kanban
msgid "Deadline:"
msgstr ":حد اقصى"

#. modules: hr_gratuity_settlement, ohrms_salary_advance
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__debit
msgid "Debit Account"
msgstr "حساب المدين"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__12
msgid "December"
msgstr "ديسمبر"

#. module: hr_leave_request_aliasing
#: model:ir.model.fields,help:hr_leave_request_aliasing.field_res_config_settings__alias_domain
msgid "Default Alias Domain for Leave"
msgstr "الافتراضي اسم المجال للإجازة"

#. module: hr_leave_request_aliasing
#: model:ir.model.fields,field_description:hr_leave_request_aliasing.field_res_config_settings__alias_prefix
#: model:ir.model.fields,help:hr_leave_request_aliasing.field_res_config_settings__alias_prefix
msgid "Default Alias Name for Leave"
msgstr "الاسم المستعار الافتراضي للإجازة"

#. modules: oh_appraisal, hr_employee_shift
#: model_terms:ir.ui.view,arch_db:hr_employee_shift.shift_template_kanban_view
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_kanban
msgid "Delete"
msgstr "حذف"

#. modules: ohrms_salary_advance, oh_hr_zk_attendance, hr_vacation_mngmt,
#. ohrms_loan, hr_employee_shift, saudi_gosi
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_contract__department_id
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate__hr_department
#: model:ir.model.fields,field_description:hr_employee_shift.field_resource_calendar__hr_department
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__dept_id
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__department_id
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__department_id
#: model_terms:ir.ui.view,arch_db:ohrms_loan.view_loan_request_search_form
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__department
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__department
msgid "Department"
msgstr "القسم"

#. module: hr_reward_warning
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__department_ids
msgid "Departments"
msgstr "الإدارات"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__depart_from
msgid "Departure"
msgstr "مغادرة"

#. modules: hr_vacation_mngmt, oh_employee_documents_expiry
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__description
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__description
#: model_terms:ir.ui.view,arch_db:oh_employee_documents_expiry.employee_document_form_view
msgid "Description"
msgstr "وصف"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__destination
msgid "Destination"
msgstr "المكان المقصود"

#. module: oh_hr_zk_attendance
#: model:ir.ui.menu,name:oh_hr_zk_attendance.zk_machine_sub_menu
msgid "Device Configuration"
msgstr "تكوين الجهاز"

#. modules: hr_gratuity_settlement, ohrms_loan, oh_appraisal,
#. oh_employee_documents_expiry, ohrms_salary_advance, oh_hr_zk_attendance,
#. uae_wps_report, ohrms_service_request, ohrmspro_holidays_approval,
#. hr_reward_warning, hr_vacation_mngmt, oh_employee_check_list,
#. oh_hr_lawsuit_management, hr_insurance, hr_employee_transfer, hr_reminder,
#. hr_employee_shift, saudi_gosi
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate__display_name
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__display_name
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__display_name
#: model:ir.model.fields,field_description:hr_employee_transfer.field_transfer_company__display_name
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__display_name
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__display_name
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__display_name
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__display_name
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__display_name
#: model:ir.model.fields,field_description:hr_insurance.field_insurance_policy__display_name
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__display_name
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__display_name
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__display_name
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__display_name
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_task_reassign__display_name
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__display_name
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal_stages__display_name
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__display_name
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_document_type__display_name
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__display_name
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__display_name
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine__display_name
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__display_name
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_report_daily_attendance__display_name
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__display_name
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__display_name
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__display_name
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__display_name
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__display_name
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_holidays_validators__display_name
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status__display_name
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__display_name
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: oh_employee_check_list
#: model:ir.model.fields,field_description:oh_employee_check_list.field_hr_employee_document__document_name
msgid "Document"
msgstr "وثيقة"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__name
msgid "Document Number"
msgstr ""

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__document_type
msgid "Document Type"
msgstr "نوع الوثيقة"

#. module: oh_employee_documents_expiry
#: code:addons/oh_employee_documents_expiry/models/employee_documents.py:0
#: code:addons/oh_employee_documents_expiry/models/employee_documents.py:0
#: code:addons/oh_employee_documents_expiry/models/employee_documents.py:0
#: code:addons/oh_employee_documents_expiry/models/employee_documents.py:0
#: code:addons/oh_employee_documents_expiry/models/employee_documents.py:0
#, python-format
msgid "Document-%s Expired On %s"
msgstr "%s-انتهت صلاحية المستند %sفي"

#. module: ohrms_core
#. openerp-web
#: code:addons/ohrms_core/static/src/xml/link_view.xml:0
#, python-format
msgid "Documentation"
msgstr "التوثيق"

#. module: oh_employee_documents_expiry
#: code:addons/oh_employee_documents_expiry/models/employee_documents.py:0
#: model_terms:ir.ui.view,arch_db:oh_employee_documents_expiry.hr_employee_document_inherit_form_view
#, python-format
msgid "Documents"
msgstr "مستندات"

#. module: hr_leave_request_aliasing
#: model_terms:ir.ui.view,arch_db:hr_leave_request_aliasing.view_hr_leave_configuration
msgid "Domain"
msgstr "نطاق"

#. modules: hr_employee_transfer, oh_appraisal
#: model:ir.model.fields.selection,name:hr_employee_transfer.selection__employee_transfer__state__done
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.view_employee_transfer_filter
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_form_view
msgid "Done"
msgstr "منجز"

#. module: oh_hr_zk_attendance
#: model:ir.actions.server,name:oh_hr_zk_attendance.cron_download_data_ir_actions_server
#: model:ir.cron,cron_name:oh_hr_zk_attendance.cron_download_data
#: model:ir.cron,name:oh_hr_zk_attendance.cron_download_data
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_machine_form
msgid "Download Data"
msgstr "تحميل البيانات"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_loan_accounting, ohrms_service_request, hr_reward_warning,
#. oh_hr_lawsuit_management, hr_employee_transfer, ohrms_loan
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.view_employee_transfer_filter
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_gratuity__state__draft
#: model:ir.model.fields.selection,name:hr_reward_warning.selection__hr_announcement__state__draft
#: model:ir.model.fields.selection,name:oh_hr_lawsuit_management.selection__hr_lawsuit__state__draft
#: model:ir.model.fields.selection,name:ohrms_loan.selection__hr_loan__state__draft
#: model:ir.model.fields.selection,name:ohrms_loan_accounting.selection__hr_loan__state__draft
#: model:ir.model.fields.selection,name:ohrms_salary_advance.selection__salary_advance__state__draft
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_execute__state_execute__draft
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__state__draft
msgid "Draft"
msgstr "مشروع"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.gratuity_configuration_form_view
msgid "Duration Configuration"
msgstr "مدة التكوين"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__ticket_class__economy
msgid "Economy"
msgstr "اقتصاد"

#. modules: oh_appraisal, hr_employee_shift
#: model_terms:ir.ui.view,arch_db:hr_employee_shift.shift_template_kanban_view
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_kanban
msgid "Edit"
msgstr "تعديل"

#. module: saudi_gosi
#: model:ir.model.fields,field_description:saudi_gosi.field_hr_employee__limit
msgid "Eligible For GOSI"
msgstr "GOSI مؤهلة للحصول على"

#. modules: hr_gratuity_settlement, oh_appraisal,
#. oh_employee_documents_expiry, ohrms_salary_advance, oh_hr_zk_attendance,
#. uae_wps_report, ohrms_service_request, hr_reward_warning,
#. hr_vacation_mngmt, oh_employee_check_list, oh_hr_lawsuit_management,
#. hr_insurance, hr_employee_transfer, ohrms_loan, hr_employee_shift,
#. saudi_gosi
#: model:ir.model,name:hr_employee_shift.model_hr_employee
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__employee_id
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.view_employee_transfer_filter
#: model:ir.model,name:hr_gratuity_settlement.model_hr_employee
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__employee_id
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__employee_id
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
#: model:ir.model,name:hr_insurance.model_hr_employee
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__employee_id
#: model:ir.model,name:hr_reward_warning.model_hr_employee
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__employee_id
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__emp_id
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__hr_emp
#: model:res.groups,name:oh_appraisal.group_appraisal_employee
#: model:ir.model,name:oh_employee_check_list.model_hr_employee
#: model:ir.model,name:oh_employee_documents_expiry.model_hr_employee
#: model_terms:ir.ui.view,arch_db:oh_employee_documents_expiry.employee_document_form_view
#: model_terms:ir.ui.view,arch_db:oh_employee_documents_expiry.employee_documents_tree_view
#: model_terms:ir.ui.view,arch_db:oh_employee_documents_expiry.search_employee_documents
#: model:ir.model,name:oh_hr_lawsuit_management.model_hr_employee
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__employee_id
#: model:ir.model.fields.selection,name:oh_hr_lawsuit_management.selection__hr_lawsuit__party2__employee
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.hr_lawsuit_search_view
#: model:ir.model,name:oh_hr_zk_attendance.model_hr_employee
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__employee_id
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_report_daily_attendance__name
#: model:ir.model,name:ohrms_loan.model_hr_employee
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__employee_id
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__employee_id
#: model_terms:ir.ui.view,arch_db:ohrms_loan.view_loan_request_search_form
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__employee_id
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__employee_id
#: model:ir.model,name:saudi_gosi.model_hr_employee
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__employee_id
#: model:ir.model,name:uae_wps_report.model_hr_employee
msgid "Employee"
msgstr "الموظف"

#. module: uae_wps_report
#: model:ir.model.fields,field_description:uae_wps_report.field_hr_employee__labour_card_number
msgid "Employee Card Number"
msgstr "رقم بطاقة الموظف"

#. module: ohrms_core
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Employee Checklist"
msgstr "قائمة مراجعة الموظفين"

#. modules: hr_employee_transfer, hr_employee_shift, hr_gratuity_settlement
#: model:ir.model,name:hr_employee_shift.model_hr_contract
#: model:ir.model,name:hr_employee_transfer.model_hr_contract
#: model:ir.model,name:hr_gratuity_settlement.model_hr_contract
msgid "Employee Contract"
msgstr "عقد الموظف"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__employee_daily_wage_days
msgid "Employee Daily Wage Days"
msgstr "الموظف أيام الأجور اليومية"

#. modules: hr_gratuity_settlement, saudi_gosi
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
#: model_terms:ir.ui.view,arch_db:saudi_gosi.gosi_payslip3
msgid "Employee Details"
msgstr "تفاصيل الموظف"

#. module: oh_employee_documents_expiry
#: model:ir.actions.server,name:oh_employee_documents_expiry.employee_data_reminder_ir_actions_server
#: model:ir.cron,cron_name:oh_employee_documents_expiry.employee_data_reminder
#: model:ir.cron,name:oh_employee_documents_expiry.employee_data_reminder
msgid "Employee Document Expiration"
msgstr "وثيقة الموظف انتهاء الصلاحية"

#. module: oh_employee_documents_expiry
#: model:ir.actions.act_window,name:oh_employee_documents_expiry.hr_employee_document_type_action
#: model:ir.ui.menu,name:oh_employee_documents_expiry.document_types_menuitem
msgid "Employee Document Types"
msgstr "أنواع وثائق الموظف"

#. modules: oh_employee_documents_expiry, oh_employee_check_list
#: model:ir.model,name:oh_employee_check_list.model_employee_checklist
#: model:ir.actions.act_window,name:oh_employee_documents_expiry.hr_employee_document_action
#: model:ir.ui.menu,name:oh_employee_documents_expiry.employe_document_menuitem
#: model_terms:ir.ui.view,arch_db:oh_employee_documents_expiry.search_employee_documents
msgid "Employee Documents"
msgstr "وثائق الموظف"

#. module: saudi_gosi
#: model:ir.actions.act_window,name:saudi_gosi.action_view_employee_gosi
msgid "Employee GOSI"
msgstr "GOSI موظف"

#. module: hr_gratuity_settlement
#: model:ir.model,name:hr_gratuity_settlement.model_hr_gratuity
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_tree
msgid "Employee Gratuity"
msgstr "مكافأة الموظف"

#. modules: ohrms_core, hr_insurance
#: model:ir.actions.act_window,name:hr_insurance.action_employee_insurance_details
#: model_terms:ir.ui.view,arch_db:hr_insurance.employee_insurance__details_form
#: model_terms:ir.ui.view,arch_db:hr_insurance.employee_insurance_details_tree
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Employee Insurance"
msgstr "تأمين الموظفين"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
msgid "Employee Name"
msgstr "اسم الموظف"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__employee_ref
msgid "Employee Ref"
msgstr "مرجع الموظف"

#. modules: ohrms_core, hr_employee_shift
#: model:ir.actions.act_window,name:hr_employee_shift.generate_schedule_action_window
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Employee Shift"
msgstr "موظف التحول"

#. module: oh_employee_documents_expiry
#: model_terms:ir.ui.view,arch_db:oh_employee_documents_expiry.view_employee_category_form2
msgid "Employee Tags"
msgstr "علامات الموظف"

#. module: hr_employee_transfer
#: model:ir.model,name:hr_employee_transfer.model_employee_transfer
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.employee_transfer
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.employee_transfer_tree
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.view_employee_transfer_filter
msgid "Employee Transfer"
msgstr "نقل الموظف"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
msgid "Employee Working Details"
msgstr "الموظف تفاصيل العمل"

#. module: oh_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.inherited_view_attendance_tree
msgid "Employee attendances"
msgstr "حضور الموظفين"

#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/hr_gratuity.py:0
#, python-format
msgid "Employee wage days is not configured in the gratuity configuration..!"
msgstr "!..لم يتم تكوين أيام أجر الموظف في تكوين مكافأة"

#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/hr_gratuity.py:0
#, python-format
msgid ""
"Employee working days is not configured in the gratuity configuration..!"
msgstr ""
"لم يتم تكوين أيام عمل الموظف في تكوين مكافأة..!"

#. module: oh_appraisal
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_form_view
msgid "Employee's Name"
msgstr "اسم الموظف"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__employee_basic_salary
msgid "Employee's basic salary."
msgstr ".الراتب الأساسي للموظف"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_contract__hourly_wage
msgid "Employee's hourly gross wage."
msgstr ".الموظف الأجر بالساعة الإجمالي"

#. module: oh_employee_creation_from_user
#: model:ir.model.fields,help:oh_employee_creation_from_user.field_res_users__employee_id
msgid "Employee-related data of the user"
msgstr "بيانات الموظف المتعلقة بالمستخدم"

#. module: hr_reward_warning
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__employee_ids
msgid "Employees"
msgstr "الموظفين"

#. module: uae_wps_report
#: model:ir.model.fields,field_description:uae_wps_report.field_res_company__employer_id
msgid "Employer ID"
msgstr "معرف صاحب العمل"

#. modules: hr_gratuity_settlement, uae_wps_report, hr_reward_warning,
#. hr_reminder, hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate__end_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__gratuity_end_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__end_date
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__date_to
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__date_end
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__end_date
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: hr_gratuity_settlement
#: model:ir.ui.menu,name:hr_gratuity_settlement.main_menu_hr_resignation
msgid "End of Service"
msgstr "نهاية الخدمة"

#. module: ohrms_service_request
#: model_terms:ir.actions.act_window,help:ohrms_service_request.action_view_pivot
msgid "Enter the target"
msgstr "أدخل الهدف"

#. module: oh_employee_check_list
#: model:ir.actions.act_window,name:oh_employee_check_list.action_entry_checklist
#: model:ir.ui.menu,name:oh_employee_check_list.employee_entry_checklist_menu
#: model_terms:ir.ui.view,arch_db:oh_employee_check_list.hr_employee_inherit_form_view
msgid "Entry Checklist"
msgstr "قائمة التحقق من الدخول"

#. module: oh_employee_check_list
#: model:ir.model.fields,field_description:oh_employee_check_list.field_hr_employee__entry_checklist
#: model:ir.model.fields.selection,name:oh_employee_check_list.selection__employee_checklist__document_type__entry
msgid "Entry Process"
msgstr "عملية الدخول"

#. module: oh_employee_check_list
#: model:ir.model.fields,field_description:oh_employee_check_list.field_hr_employee__entry_progress
#: model_terms:ir.ui.view,arch_db:oh_employee_check_list.hr_employee_inherit_kanban_view
msgid "Entry Progress"
msgstr "تقدم الدخول"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields.selection,name:oh_employee_documents_expiry.selection__hr_employee_document__notification_type__everyday
msgid "Everyday till expiry date"
msgstr "كل يوم حتى تاريخ انتهاء الصلاحية"

#. module: ohrms_salary_advance
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__exceed_condition
msgid "Exceed than maximum"
msgstr "تتجاوز من الحد الأقصى"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__executer
msgid "Executer"
msgstr "المنفذ"

#. module: oh_employee_check_list
#: model:ir.actions.act_window,name:oh_employee_check_list.action_exit_checklist
#: model:ir.ui.menu,name:oh_employee_check_list.employee_exit_checklist_menu
#: model_terms:ir.ui.view,arch_db:oh_employee_check_list.hr_employee_inherit_form_view
msgid "Exit Checklist"
msgstr "خروج المرجعية"

#. module: oh_employee_check_list
#: model:ir.model.fields,field_description:oh_employee_check_list.field_hr_employee__exit_checklist
#: model:ir.model.fields.selection,name:oh_employee_check_list.selection__employee_checklist__document_type__exit
msgid "Exit Process"
msgstr "الخروج من العملية"

#. module: oh_employee_check_list
#: model:ir.model.fields,field_description:oh_employee_check_list.field_hr_employee__exit_progress
msgid "Exit Progress"
msgstr "خروج التقدم"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_leave__expense_account
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_leave_configuration
msgid "Expense Account"
msgstr "حساب المصاريف"

#. modules: hr_gratuity_settlement, hr_insurance
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_contract__state__close
#: model:ir.model.fields.selection,name:hr_insurance.selection__hr_insurance__state__expired
msgid "Expired"
msgstr "منتهي الصلاحية"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__expiry_date
msgid "Expiry Date"
msgstr "تاريخ الانتهاء"

#. module: hr_gratuity_settlement
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_training__state__extended
msgid "Extended"
msgstr "وسعوا"

#. module: hr_insurance
#: model_terms:ir.ui.view,arch_db:hr_insurance.insurance_policy_details_form
msgid "Extra Notes"
msgstr "ملاحظات إضافية"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__15
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__attendance_type__15
msgid "Face"
msgstr "وجه"

#. module: oh_hr_lawsuit_management
#: model:ir.model.fields.selection,name:oh_hr_lawsuit_management.selection__hr_lawsuit__state__fail
msgid "Failed"
msgstr "فشل"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__02
msgid "February"
msgstr "فبراير"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__model_field
msgid "Field"
msgstr "حقل"

#. module: uae_wps_report
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__name
msgid "File Name"
msgstr "اسم الملف"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__final_evaluation
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_form_view
msgid "Final Evaluation"
msgstr "التقييم النهائي"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__final_interview
msgid "Final Interview"
msgstr "المقابلة النهائية"

#. module: oh_appraisal
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_kanban
msgid "Final Interview:"
msgstr ":المقابلة النهائية"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__1
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__attendance_type__1
msgid "Finger"
msgstr "اصبع اليد"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__ticket_class__first_class
msgid "First Class"
msgstr "الصف الأول"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__flight_details
msgid "Flight Details"
msgstr "تفاصيل الرحلة"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_vacation.py:0
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_leave__flight_ticket
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.hr_vacation_form_view
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_book_flight_ticket_form
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_flight_ticket_form
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_flight_ticket_tree
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_leave_configuration
#, python-format
msgid "Flight Ticket"
msgstr "تذكرة طيران"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_employee_ticket.py:0
#, python-format
msgid "Flight Ticket Invoice"
msgstr "فاتورة تذكرة طيران"

#. module: hr_vacation_mngmt
#: model:ir.actions.act_window,name:hr_vacation_mngmt.action_hr_flight_tickets
#: model:ir.ui.menu,name:hr_vacation_mngmt.menu_hr_flight_tickets
msgid "Flight Tickets"
msgstr "تذاكر طيران"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_employee_ticket.py:0
#, python-format
msgid "Flight ticket for %s on %s to %s"
msgstr "%s إلى %s على %s تذكرة طيران ل"

#. module: hr_vacation_mngmt
#: model:ir.actions.server,name:hr_vacation_mngmt.ir_cron_ticket_status_update_ir_actions_server
#: model:ir.cron,cron_name:hr_vacation_mngmt.ir_cron_ticket_status_update
#: model:ir.cron,name:hr_vacation_mngmt.ir_cron_ticket_status_update
msgid "Flight ticket status update"
msgstr "تحديث حالة تذكرة الطيران"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_employee_ticket.py:0
#, python-format
msgid "Flight travelling start date must be less than flight return date."
msgstr ".يجب أن يكون تاريخ بدء رحلة السفر أقل من تاريخ عودة الرحلة"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal_stages__fold
msgid "Folded in Appraisal Pipeline"
msgstr "مطوية في خط أنابيب التقييم"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_follower_ids
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_follower_ids
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_follower_ids
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_follower_ids
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_follower_ids
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_follower_ids
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_follower_ids
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_follower_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_follower_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_follower_ids
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_follower_ids
msgid "Followers"
msgstr "متابعون"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_channel_ids
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_channel_ids
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_channel_ids
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_channel_ids
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_channel_ids
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_channel_ids
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_channel_ids
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_channel_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_channel_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_channel_ids
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_channel_ids
msgid "Followers (Channels)"
msgstr "(المتابعون (القنوات"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_partner_ids
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_partner_ids
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_partner_ids
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_partner_ids
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_partner_ids
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_partner_ids
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_partner_ids
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_partner_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_partner_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_partner_ids
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_partner_ids
msgid "Followers (Partners)"
msgstr "(المتابعون (الشركاء"

#. module: hr_employee_shift
#: code:addons/hr_employee_shift/models/hr_employee_shift.py:0
#, python-format
msgid "Friday Morning"
msgstr "صباح الجمعة"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__app_period_from
msgid "From"
msgstr "من عند"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__from_year
msgid "From Year"
msgstr "من سنة"

#. module: saudi_gosi
#: model:hr.salary.rule.category,name:saudi_gosi.GOSI
msgid "GOSI"
msgstr "GOSI"

#. module: saudi_gosi
#: model:hr.salary.rule,name:saudi_gosi.hr_rule_gosi_comp
msgid "GOSI Company Contribution For Saudi Employee"
msgstr ""

#. module: saudi_gosi
#: model:hr.salary.rule,name:saudi_gosi.hr_rule_gosi
msgid "GOSI Contribution For Saudi Employee"
msgstr "مساهمة الموظف السعودي GOSI"

#. module: saudi_gosi
#: model_terms:ir.ui.view,arch_db:saudi_gosi.gosi_payslip3
msgid "GOSI Details"
msgstr "تفاصيل GOSI"

#. module: saudi_gosi
#: model_terms:ir.ui.view,arch_db:saudi_gosi.hr_employee_inherited_form_view
msgid "GOSI Information"
msgstr "المعلومات GOSI"

#. module: saudi_gosi
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__gos_numb
#: model:ir.model.fields,field_description:saudi_gosi.field_hr_employee__gosi_number
msgid "GOSI Number"
msgstr "رقم GOSI"

#. module: saudi_gosi
#: model:ir.model,name:saudi_gosi.model_gosi_payslip
msgid "GOSI Record"
msgstr "سجل GOSI"

#. module: saudi_gosi
#: model:ir.model.fields,field_description:saudi_gosi.field_hr_payslip__gosi_no
msgid "GOSI Reference"
msgstr ""

#. module: saudi_gosi
#: model:ir.ui.menu,name:saudi_gosi.gosi_employee
msgid "GOSI Register"
msgstr "تسجيل GOSI"

#. module: hr_reward_warning
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_form
msgid "General Announcement"
msgstr "الإعلان العام"

#. module: hr_reward_warning
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_search
msgid "General Announcements"
msgstr "الإعلانات العامة"

#. module: ohrms_core
#: model:ir.actions.act_window,name:ohrms_core.action_hr_general_config
msgid "General Settings"
msgstr "الاعدادات العامة"

#. module: hr_employee_shift
#: model_terms:ir.ui.view,arch_db:hr_employee_shift.generate_schedule_form
msgid "Generate"
msgstr "انشاء"

#. module: uae_wps_report
#: model:ir.actions.act_window,name:uae_wps_report.wps_action_view
#: model_terms:ir.ui.view,arch_db:uae_wps_report.wps_wizard_form
msgid "Generate SIF"
msgstr "SIF انشاء"

#. module: hr_employee_shift
#: model:ir.ui.menu,name:hr_employee_shift.menu_shift_schedule_generate_id
msgid "Generate Schedule"
msgstr "توليد الجدول"

#. module: ohrms_core
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Get This App"
msgstr "الحصول على هذا التطبيق"

#. module: hr_employee_transfer
#: model:ir.model.fields,help:hr_employee_transfer.field_employee_transfer__name
msgid "Give a name to the Transfer"
msgstr "إعطاء اسم للنقل"

#. module: hr_gratuity_settlement
#: model:ir.model,name:hr_gratuity_settlement.model_hr_gratuity_accounting_configuration
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__gratuity_accounting_configuration_id
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_tree
msgid "Gratuity Accounting Configuration"
msgstr "تكوين المحاسبة المكافأة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__employee_gratuity_years
msgid "Gratuity Calculation Years"
msgstr "سنوات حساب المكافأة"

#. module: hr_gratuity_settlement
#: model:ir.actions.act_window,name:hr_gratuity_settlement.action_view_hr_gratuity_accounting_configuration
#: model:ir.model,name:hr_gratuity_settlement.model_gratuity_configuration
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__employee_gratuity_configuration
#: model:ir.ui.menu,name:hr_gratuity_settlement.menu_action_hr_gratuity_account_config
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
msgid "Gratuity Configuration"
msgstr "تكوين البراعة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__gratuity_configuration_table
msgid "Gratuity Configuration Table"
msgstr "جدول تكوين المكافأة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__gratuity_credit_account
msgid "Gratuity Credit Account"
msgstr "حساب الرضا الائتماني"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__gratuity_debit_account
msgid "Gratuity Debit Account"
msgstr "حساب الخصم المباشر"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
msgid "Gratuity Details"
msgstr "تفاصيل المكافأة"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.gratuity_configuration_form_view
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.gratuity_configuration_tree_view
msgid "Gratuity Duration"
msgstr "مدة المكافأة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__gratuity_journal
msgid "Gratuity Journal"
msgstr "مجلة البراعة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__employee_gratuity_amount
msgid "Gratuity Payment"
msgstr "دفع المكافأة"

#. module: hr_gratuity_settlement
#: model:ir.actions.act_window,name:hr_gratuity_settlement.action_employee_gratuity
#: model:ir.ui.menu,name:hr_gratuity_settlement.menu_hr_gratuity
msgid "Gratuity Settlement"
msgstr "تسوية المكافأة"

#. module: hr_gratuity_settlement
#: model:ir.model.constraint,message:hr_gratuity_settlement.constraint_hr_gratuity_accounting_configuration_name_uniq
msgid "Gratuity configuration name should be unique!"
msgstr "!يجب أن يكون اسم تكوين البراعة فريدًا"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_leave__leave_salary__1
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__res_config_settings__default_leave_salary__1
msgid "Gross"
msgstr "إجمالي"

#. modules: oh_hr_zk_attendance, oh_hr_lawsuit_management,
#. oh_employee_documents_expiry
#: model_terms:ir.ui.view,arch_db:oh_employee_documents_expiry.search_employee_documents
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.hr_lawsuit_search_view
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.inherited_hr_attendance_view_filter
msgid "Group By"
msgstr "مجموعة من"

#. module: hr_reward_warning
#: model:ir.model,name:hr_reward_warning.model_hr_announcement
msgid "HR Announcement"
msgstr "إعلان الموارد البشرية"

#. modules: oh_employee_documents_expiry, oh_employee_check_list
#: model:ir.model,name:oh_employee_check_list.model_hr_employee_document
#: model:ir.model,name:oh_employee_documents_expiry.model_hr_employee_document
msgid "HR Employee Documents"
msgstr "وثائق موظف الموارد البشرية"

#. module: hr_insurance
#: model:ir.model,name:hr_insurance.model_hr_insurance
msgid "HR Insurance"
msgstr "تأمين الموارد البشرية"

#. module: hr_vacation_mngmt
#: model:ir.actions.server,name:hr_vacation_mngmt.hr_email_leave_reminder_ir_actions_server
#: model:ir.cron,cron_name:hr_vacation_mngmt.hr_email_leave_reminder
#: model:ir.cron,name:hr_vacation_mngmt.hr_email_leave_reminder
msgid "HR Leave Reminder"
msgstr "الموارد البشرية ترك تذكير"

#. module: hr_reminder
#: model_terms:ir.ui.view,arch_db:hr_reminder.hr_reminder_form_view
msgid "HR Reminder"
msgstr "تذكير الموارد البشرية"

#. module: hr_gratuity_settlement
#: model:ir.model,name:hr_gratuity_settlement.model_hr_training
msgid "HR Training"
msgstr "تدريب الموارد البشرية"

#. module: ohrms_core
#: model:ir.ui.menu,name:ohrms_core.menu_hr_general_config
msgid "HRMS Settings"
msgstr "إعدادات نظام إدارة الموارد البشرية"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_contract__half_leave_ids
msgid "Half Leave"
msgstr "إجازة نصف"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__module_hr_resignation
msgid "Handle the resignation process of the employee"
msgstr "التعامل مع عملية استقالة الموظف"

#. module: oh_appraisal
#: model:ir.module.category,description:oh_appraisal.module_category_hr_appraisal
msgid "Helps to evaluate employees..."
msgstr "...يساعد على تقييم الموظفين"

#. module: ohrms_core
#: model:ir.model.fields,help:ohrms_core.field_res_config_settings__module_oh_hr_zk_attendance
msgid ""
"Helps you to manage Biometric Device Integration.\n"
"- This installs the module Biometric Device Integration."
msgstr ""
"يساعدك على إدارة تكامل الأجهزة البيومترية.\n"
"- هذا بتثبيت وحدة تكامل الأجهزة البيومترية."
#. module: ohrms_core
#: model:ir.model.fields,help:ohrms_core.field_res_config_settings__module_hr_custody
msgid ""
"Helps you to manage Custody Requests.\n"
"- This installs the module Custody Management."
msgstr ""

#. module: ohrms_core
#: model:ir.model.fields,help:ohrms_core.field_res_config_settings__module_oh_employee_check_list
msgid ""
"Helps you to manage Employee Checklist.\n"
"- This installs the module Employee Checklist."
msgstr ""
"يساعدك على إدارة الموظف المرجعية.\n"
"- هذا بتثبيت الوحدة النمطية الموظف المرجعية."
#. module: ohrms_core
#: model:ir.model.fields,help:ohrms_core.field_res_config_settings__module_hr_insurance
msgid ""
"Helps you to manage Employee Insurance.\n"
"- This installs the module Employee Insurance."
msgstr ""
"يساعدك على إدارة تأمين الموظفين.\n"
"- هذا يثبت الوحدة الموظف التأمين."

#. module: ohrms_core
#: model:ir.model.fields,help:ohrms_core.field_res_config_settings__module_hr_employee_shift
msgid ""
"Helps you to manage Employee Shift.\n"
"- This installs the module Employee Shift."
msgstr ""
"يساعدك على إدارة الموظف التحول.\n"
"- هذا بتثبيت الوحدة النمطية للموظف التحول."
#. module: ohrms_core
#: model:ir.model.fields,help:ohrms_core.field_res_config_settings__module_oh_hr_lawsuit_management
msgid ""
"Helps you to manage Lawsuit Management.\n"
"- This installs the module Lawsuit Management."
msgstr ""
"يساعدك على إدارة إدارة الدعاوى.\n"
"- هذا بتثبيت وحدة إدارة الدعاوى القضائية."
#. module: ohrms_core
#: model:ir.model.fields,help:ohrms_core.field_res_config_settings__module_hr_resignation
msgid ""
"Helps you to manage Resignation Process.\n"
"- This installs the module Resignation Process."
msgstr ""
"يساعدك على إدارة عملية الاستقالة.\n"
"- هذا بتثبيت الوحدة النمطية عملية الاستقالة."
#. module: ohrms_core
#: model:ir.model.fields,help:ohrms_core.field_res_config_settings__module_hr_vacation_mngmt
msgid ""
"Helps you to manage Vacation Management.\n"
"- This installs the module Vacation Management."
msgstr ""
"يساعدك على إدارة إدارة العطلات.\n"
"- هذا بتثبيت وحدة إدارة عطلة."
#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_leave__holiday_managers
msgid "Holiday Managers"
msgstr "مديري العطلات"

#. module: ohrmspro_holidays_approval
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status__holiday_status
msgid "Holiday Status"
msgstr "حالة عطلة"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.hr_contract_form_inherit_wage
msgid "Hourly Advantages in Cash"
msgstr "مزايا كل ساعة نقدا"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_contract__hourly_wage
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_contract__wage_type__hourly
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_gratuity__wage_type__hourly
msgid "Hourly Wage"
msgstr "الأجر بالساعة"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,help:oh_employee_documents_expiry.field_hr_employee_document__before_days
msgid "How many number of days before to get the notification email"
msgstr "كم عدد الأيام قبل الحصول على إشعار البريد الإلكتروني"

#. module: oh_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.inherited_hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_report_daily_attendance_search
msgid "Hr Attendance Search"
msgstr "البحث عن الموارد البشرية الحضور"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__hr_gratuity_credit_account
msgid "Hr Gratuity Credit Account"
msgstr "حساب رصيد الموارد البشرية"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__hr_gratuity_debit_account
msgid "Hr Gratuity Debit Account"
msgstr "حساب الخصم الخاص بالموارد البشرية"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__hr_gratuity_journal
msgid "Hr Gratuity Journal"
msgstr "مجلة الموارد البشرية"

#. module: ohrmspro_holidays_approval
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_holidays_validators__hr_holiday_status
msgid "Hr Holiday Status"
msgstr "الموارد البشرية حالة عطلة"

#. module: oh_hr_lawsuit_management
#: model:ir.model,name:oh_hr_lawsuit_management.model_hr_lawsuit
msgid "Hr Lawsuit Management"
msgstr "إدارة دعاوى الموارد البشرية"

#. modules: hr_gratuity_settlement, ohrms_loan, oh_appraisal,
#. oh_employee_documents_expiry, ohrms_salary_advance, oh_hr_zk_attendance,
#. uae_wps_report, ohrms_service_request, ohrmspro_holidays_approval,
#. hr_reward_warning, hr_vacation_mngmt, oh_employee_check_list,
#. oh_hr_lawsuit_management, hr_insurance, hr_employee_transfer, hr_reminder,
#. hr_employee_shift, saudi_gosi
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate__id
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__id
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__id
#: model:ir.model.fields,field_description:hr_employee_transfer.field_transfer_company__id
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__id
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__id
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__id
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__id
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__id
#: model:ir.model.fields,field_description:hr_insurance.field_insurance_policy__id
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__id
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__id
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__id
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__id
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_task_reassign__id
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__id
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal_stages__id
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__id
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_document_type__id
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__id
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__id
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine__id
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__id
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_report_daily_attendance__id
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__id
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__id
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__id
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__id
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__id
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_holidays_validators__id
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status__id
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__id
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__id
msgid "ID"
msgstr "هوية شخصية"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__activity_exception_icon
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__activity_exception_icon
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__activity_exception_icon
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__activity_exception_icon
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__activity_exception_icon
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__activity_exception_icon
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__activity_exception_icon
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__activity_exception_icon
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__activity_exception_icon
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__activity_exception_icon
msgid "Icon"
msgstr "أيقونة"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__activity_exception_icon
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__activity_exception_icon
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__activity_exception_icon
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__activity_exception_icon
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__activity_exception_icon
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__activity_exception_icon
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__activity_exception_icon
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__activity_exception_icon
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__activity_exception_icon
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ".أيقونة للإشارة إلى نشاط استثناء"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__message_needaction
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__message_unread
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__message_needaction
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__message_unread
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__message_needaction
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__message_unread
#: model:ir.model.fields,help:oh_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,help:oh_appraisal.field_hr_appraisal__message_unread
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__message_needaction
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__message_unread
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__message_needaction
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__message_unread
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__message_needaction
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__message_unread
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__message_needaction
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__message_unread
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__message_needaction
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__message_unread
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__message_needaction
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__message_unread
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__message_needaction
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__message_unread
msgid "If checked, new messages require your attention."
msgstr ".إذا تم تحديد ذلك ، فإن الرسائل الجديدة تتطلب اهتمامك"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__message_has_error
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__message_has_sms_error
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__message_has_error
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__message_has_sms_error
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__message_has_error
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__message_has_sms_error
#: model:ir.model.fields,help:oh_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,help:oh_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__message_has_error
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__message_has_sms_error
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__message_has_error
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__message_has_sms_error
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__message_has_error
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__message_has_sms_error
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__message_has_error
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__message_has_sms_error
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__message_has_error
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__message_has_sms_error
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__message_has_error
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__message_has_sms_error
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__message_has_error
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ".إذا تم التحقق ، فبعض الرسائل بها خطأ في التسليم"

#. modules: ohrms_loan_accounting, ohrms_loan
#: model:ir.model,name:ohrms_loan.model_hr_loan_line
#: model:ir.model,name:ohrms_loan_accounting.model_hr_loan_line
msgid "Installment Line"
msgstr "خط التقسيط"

#. module: ohrms_loan
#: model_terms:ir.ui.view,arch_db:ohrms_loan.hr_loan_form_view
msgid "Installments"
msgstr "الأقساط"

#. module: hr_insurance
#: model:ir.model.fields,field_description:hr_insurance.field_hr_employee__insurance
#: model:ir.ui.menu,name:hr_insurance.hr_employee_insurance_menu
#: model_terms:ir.ui.view,arch_db:hr_insurance.hr_employee_insurance_form
msgid "Insurance"
msgstr "تأمين"

#. module: hr_insurance
#: model:hr.salary.rule,name:hr_insurance.hr_payslip_rule_insurance
msgid "Insurance Amount"
msgstr "مبلغ التأمين"

#. module: hr_insurance
#: model:ir.ui.menu,name:hr_insurance.policy_management_menu
msgid "Insurance Policy"
msgstr "بوليصة التأمين"

#. module: hr_insurance
#: model:ir.actions.act_window,name:hr_insurance.action_insurance_policy_management
msgid "Insurance Policy "
msgstr " بوليصة التأمين"

#. modules: hr_employee_transfer, ohrms_service_request
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__note
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.employee_transfer
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_execute2
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_request11
msgid "Internal Notes"
msgstr "ملاحظات داخلية"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__notes
msgid "Internal notes"
msgstr "ملاحظات داخلية"

#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/gratuity_accounting_configuration.py:0
#, python-format
msgid "Invalid date configuration!"
msgstr "!تكوين تاريخ غير صالح"

#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/gratuity_configuration.py:0
#, python-format
msgid "Invalid year configuration!"
msgstr "!التكوين العام غير صالح"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__invoice_id
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_flight_ticket_form
msgid "Invoice"
msgstr "فاتورة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_contract__is_approve
msgid "Is Approve"
msgstr "هو الموافقة"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_is_follower
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_is_follower
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_is_follower
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_is_follower
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_is_follower
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_is_follower
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_is_follower
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_is_follower
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_is_follower
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_is_follower
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_is_follower
msgid "Is Follower"
msgstr "هو تابع"

#. module: hr_reward_warning
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_search
msgid "Is a General Announcement"
msgstr "هو إعلان عام"

#. module: hr_reward_warning
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__is_announcement
msgid "Is general Announcement?"
msgstr "هل الإعلان العام؟"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__issue
msgid "Issue"
msgstr "القضية"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__issue_date
msgid "Issue Date"
msgstr "تاريخ الاصدار"

#. module: saudi_gosi
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__issued_dat
#: model:ir.model.fields,field_description:saudi_gosi.field_hr_employee__issue_date
msgid "Issued Date"
msgstr "تاريخ الإصدار"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__service_product
msgid "Item For Service"
msgstr "البند للخدمة"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__01
msgid "January"
msgstr "يناير"

#. modules: ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__job_position
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__position
msgid "Job Position"
msgstr "وظيفه"

#. module: hr_reward_warning
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__position_ids
msgid "Job Positions"
msgstr "المناصب الوظيفية"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__employee_joining_date
msgid "Joining Date"
msgstr "تاريخ الانضمام"

#. modules: hr_gratuity_settlement, ohrms_loan_accounting,
#. ohrms_salary_advance
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
#: model:ir.model.fields,field_description:ohrms_loan_accounting.field_hr_loan__journal_id
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__journal
msgid "Journal"
msgstr "مجلة"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
msgid "Journal Configuration"
msgstr "مجلة التكوين"

#. module: ohrms_loan_accounting
#: model:ir.model,name:ohrms_loan_accounting.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: oh_hr_lawsuit_management
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__judge
msgid "Judge"
msgstr "القاضي"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__07
msgid "July"
msgstr "يوليو"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__06
msgid "June"
msgstr "يونيو"

#. modules: hr_gratuity_settlement, ohrms_loan, oh_appraisal,
#. oh_employee_documents_expiry, ohrms_salary_advance, oh_hr_zk_attendance,
#. uae_wps_report, ohrms_service_request, ohrmspro_holidays_approval,
#. hr_reward_warning, hr_vacation_mngmt, oh_employee_check_list,
#. oh_hr_lawsuit_management, hr_insurance, hr_employee_transfer, hr_reminder,
#. hr_employee_shift, saudi_gosi
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate____last_update
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule____last_update
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer____last_update
#: model:ir.model.fields,field_description:hr_employee_transfer.field_transfer_company____last_update
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration____last_update
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity____last_update
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration____last_update
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training____last_update
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance____last_update
#: model:ir.model.fields,field_description:hr_insurance.field_insurance_policy____last_update
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder____last_update
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement____last_update
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket____last_update
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task____last_update
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_task_reassign____last_update
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal____last_update
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal_stages____last_update
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist____last_update
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_document_type____last_update
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document____last_update
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit____last_update
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine____last_update
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance____last_update
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_report_daily_attendance____last_update
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan____last_update
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line____last_update
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance____last_update
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute____last_update
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request____last_update
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_holidays_validators____last_update
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status____last_update
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip____last_update
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. modules: hr_gratuity_settlement, ohrms_loan, oh_appraisal,
#. oh_employee_documents_expiry, ohrms_salary_advance, oh_hr_zk_attendance,
#. uae_wps_report, ohrms_service_request, ohrmspro_holidays_approval,
#. hr_reward_warning, hr_vacation_mngmt, oh_employee_check_list,
#. oh_hr_lawsuit_management, hr_insurance, hr_employee_transfer, hr_reminder,
#. hr_employee_shift, saudi_gosi
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate__write_uid
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__write_uid
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__write_uid
#: model:ir.model.fields,field_description:hr_employee_transfer.field_transfer_company__write_uid
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__write_uid
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__write_uid
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__write_uid
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__write_uid
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__write_uid
#: model:ir.model.fields,field_description:hr_insurance.field_insurance_policy__write_uid
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__write_uid
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__write_uid
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__write_uid
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__write_uid
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_task_reassign__write_uid
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__write_uid
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal_stages__write_uid
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__write_uid
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_document_type__write_uid
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__write_uid
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__write_uid
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine__write_uid
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__write_uid
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__write_uid
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__write_uid
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__write_uid
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__write_uid
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__write_uid
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_holidays_validators__write_uid
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status__write_uid
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__write_uid
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. modules: hr_gratuity_settlement, ohrms_loan, oh_appraisal,
#. oh_employee_documents_expiry, ohrms_salary_advance, oh_hr_zk_attendance,
#. uae_wps_report, ohrms_service_request, ohrmspro_holidays_approval,
#. hr_reward_warning, hr_vacation_mngmt, oh_employee_check_list,
#. oh_hr_lawsuit_management, hr_insurance, hr_employee_transfer, hr_reminder,
#. hr_employee_shift, saudi_gosi
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate__write_date
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__write_date
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__write_date
#: model:ir.model.fields,field_description:hr_employee_transfer.field_transfer_company__write_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__write_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__write_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__write_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__write_date
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__write_date
#: model:ir.model.fields,field_description:hr_insurance.field_insurance_policy__write_date
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__write_date
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__write_date
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__write_date
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__write_date
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_task_reassign__write_date
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__write_date
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal_stages__write_date
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__write_date
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_document_type__write_date
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__write_date
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__write_date
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine__write_date
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__write_date
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__write_date
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__write_date
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__write_date
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__write_date
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__write_date
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_holidays_validators__write_date
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status__write_date
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__write_date
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: oh_hr_lawsuit_management
#: model:ir.module.category,name:oh_hr_lawsuit_management.module_lawsuit_category
msgid "Lawsuit"
msgstr "دعوى قضائية"

#. module: ohrms_core
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Lawsuit Management"
msgstr "إدارة الدعاوى"

#. module: oh_hr_lawsuit_management
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__lawyer
msgid "Lawyer"
msgstr "محامي"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__leave_id
msgid "Leave"
msgstr "غادر"

#. module: hr_leave_request_aliasing
#: model_terms:ir.ui.view,arch_db:hr_leave_request_aliasing.view_hr_leave_configuration
msgid "Leave Email Alias"
msgstr "ترك البريد الإلكتروني الاسم المستعار"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.training_from_view
msgid "Leave Information"
msgstr "ترك المعلومات"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_res_config_settings__leave_reminder
msgid "Leave Reminder Email"
msgstr "ترك تذكير البريد الإلكتروني"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__leave_id
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_task_reassign__leave_req_id
msgid "Leave Request"
msgstr "ترك طلب"

#. module: hr_vacation_mngmt
#: model:hr.salary.rule,name:hr_vacation_mngmt.hr_salary_rule_leave_salary_basic
#: model:hr.salary.rule,name:hr_vacation_mngmt.hr_salary_rule_leave_salary_gross
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_leave__leave_salary
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_payslip__leave_salary
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_res_config_settings__default_leave_salary
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.res_config_settings_view_form
msgid "Leave Salary"
msgstr "ترك الراتب"

#. module: ohrmspro_holidays_approval
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_holidays_validators__holiday_validators
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_leave__leave_approvals
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_leave_type__leave_validators
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_leave_validation_status__validating_users
msgid "Leave Validators"
msgstr "اترك المدققين"

#. modules: ohrmspro_holidays_approval, hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_vacation.py:0
#: code:addons/ohrmspro_holidays_approval/models/leave_request.py:0
#, python-format
msgid "Leave request must be confirmed (\"To Approve\") in order to approve it."
msgstr ".يجب تأكيد طلب الإجازة (\" للموافقة \") للموافقة عليه"

#. module: ohrmspro_holidays_approval
#: code:addons/ohrmspro_holidays_approval/models/leave_request.py:0
#: code:addons/ohrmspro_holidays_approval/models/leave_request.py:0
#, python-format
msgid "Leave request must be confirmed or validated in order to refuse it."
msgstr ".يجب تأكيد أو تأكيد طلب الإجازة لرفضه"

#. module: hr_vacation_mngmt
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.res_config_settings_view_form
msgid "Leave salary calculation"
msgstr "ترك حساب الراتب"

#. module: ohrmspro_holidays_approval
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_double_validation
msgid "Leave validation"
msgstr "ترك التحقق من الصحة"

#. modules: hr_gratuity_settlement, hr_vacation_mngmt,
#. hr_leave_request_aliasing
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__leave_ids
#: model_terms:ir.ui.view,arch_db:hr_leave_request_aliasing.view_hr_leave_configuration
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.res_config_settings_view_form
msgid "Leaves"
msgstr "اوراق اشجار"

#. module: hr_leave_request_aliasing
#: model:ir.actions.act_window,name:hr_leave_request_aliasing.action_hr_general_config
msgid "Leaves Config"
msgstr "يترك التكوين"

#. module: hr_vacation_mngmt
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_leave_configuration
msgid "Leaves Reminder"
msgstr "يترك تذكير"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__employee_probation_years
msgid "Leaves Taken(Years)"
msgstr "(الأوراق المتخذة (سنوات"

#. module: oh_hr_lawsuit_management
#: code:addons/oh_hr_lawsuit_management/models/legal_action.py:0
#: code:addons/oh_hr_lawsuit_management/models/legal_action.py:0
#: model:ir.ui.menu,name:oh_hr_lawsuit_management.hr_lawsuit_sub_menu
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.hr_lawsuit_form_view
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.hr_lawsuit_search_view
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.legal_hr_employee_inherit_form_view
#, python-format
msgid "Legal Actions"
msgstr "اجراءات قانونية"

#. module: oh_hr_lawsuit_management
#: model:ir.actions.act_window,name:oh_hr_lawsuit_management.action_hr_lawsuit
msgid "Legal Management"
msgstr "الإدارة القانونية"

#. module: hr_reward_warning
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__announcement
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_form
msgid "Letter"
msgstr "رسالة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_gratuity__employee_contract_type__limited
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_gratuity_accounting_configuration__config_contract_type__limited
msgid "Limited"
msgstr "محدود"

#. module: ohrms_loan
#: model:hr.salary.rule,name:ohrms_loan.hr_rule_loan
#: model:ir.ui.menu,name:ohrms_loan.menu_base_hr_loan_request
#: model_terms:ir.ui.view,arch_db:ohrms_loan.view_loan_request_search_form
msgid "Loan"
msgstr "قرض"

#. module: ohrms_loan_accounting
#: model:ir.model.fields,field_description:ohrms_loan_accounting.field_hr_loan__employee_account_id
msgid "Loan Account"
msgstr "حساب القرض"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__loan_amount
msgid "Loan Amount"
msgstr "مبلغ القرض"

#. module: ohrms_loan_accounting
#: model_terms:ir.ui.view,arch_db:ohrms_loan_accounting.view_account_config_inherit
msgid "Loan Approval"
msgstr "الموافقة على القرض"

#. module: ohrms_loan_accounting
#: model:ir.model.fields,help:ohrms_loan_accounting.field_res_config_settings__loan_approve
#: model_terms:ir.ui.view,arch_db:ohrms_loan_accounting.view_account_config_inherit
msgid "Loan Approval from account manager"
msgstr "موافقة القرض من مدير الحساب"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_employee__loan_count
msgid "Loan Count"
msgstr "عدد القروض"

#. module: ohrms_loan_accounting
#: model:ir.model.fields,field_description:ohrms_loan_accounting.field_account_move_line__loan_id
msgid "Loan Id"
msgstr "معرف القرض"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_payslip_input__loan_line_id
msgid "Loan Installment"
msgstr "قرض القسط"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__loan_lines
msgid "Loan Line"
msgstr "خط القرض"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__name
msgid "Loan Name"
msgstr "اسم القرض"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__loan_id
msgid "Loan Ref."
msgstr ".مرجع القرض"

#. modules: ohrms_loan_accounting, ohrms_loan
#: model:ir.model,name:ohrms_loan.model_hr_loan
#: model_terms:ir.ui.view,arch_db:ohrms_loan.hr_loan_form_view
#: model:ir.model,name:ohrms_loan_accounting.model_hr_loan
msgid "Loan Request"
msgstr "طلب قرض"

#. module: ohrms_loan
#: model_terms:ir.ui.view,arch_db:ohrms_loan.hr_loan_tree_view
msgid "Loan Requests"
msgstr "طلبات القروض"

#. module: ohrms_loan
#: model:ir.actions.act_window,name:ohrms_loan.act_hr_employee_loan_request
#: model_terms:ir.ui.view,arch_db:ohrms_loan.view_employee_form_loan_inherit
msgid "Loans"
msgstr "القروض"

#. module: ohrms_loan
#: model:ir.ui.menu,name:ohrms_loan.menu_hr_loans_and_advances
msgid "Loans & Advances"
msgstr "القروض والسلفيات"

#. module: ohrms_core
#. openerp-web
#: code:addons/ohrms_core/static/src/xml/link_view.xml:0
#, python-format
msgid "Log out"
msgstr "تسجيل الخروج"

#. module: oh_hr_lawsuit_management
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.hr_lawsuit_form_view
msgid "Loss"
msgstr "خسارة"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine__name
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_machine_form
msgid "Machine IP"
msgstr "IP آلة"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_main_attachment_id
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_main_attachment_id
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_main_attachment_id
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_main_attachment_id
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_main_attachment_id
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_main_attachment_id
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_main_attachment_id
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_main_attachment_id
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__module_hr_insurance
msgid "Manage Insurance for employees"
msgstr "إدارة التأمين للموظفين"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__module_oh_hr_zk_attendance
msgid ""
"Manage biometric device (Model: ZKteco uFace 202) integration with HR "
"attendance (Face + Thumb)"
msgstr ""
"إدارة جهاز البيومترية (Model: ZKteco uFace 202) التكامل مع الموارد البشرية "
"الحضور (وجه +إبهام اليد)"
#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__module_hr_employee_shift
msgid "Manage different type of shifts"
msgstr "إدارة أنواع مختلفة من التحولات"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__module_hr_vacation_mngmt
msgid "Manage employee vacation"
msgstr "إدارة عطلة الموظف"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__module_oh_hr_lawsuit_management
msgid "Manage legal actions"
msgstr "إدارة الإجراءات القانونية"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__module_hr_custody
msgid "Manage the company properties when it is in the custody of an employee"
msgstr "إدارة ممتلكات الشركة عندما تكون في عهدة موظف"

#. modules: oh_appraisal, oh_hr_lawsuit_management
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__hr_manager
#: model:res.groups,name:oh_appraisal.group_appraisal_manager
#: model:res.groups,name:oh_hr_lawsuit_management.lawsuit_group_manager
msgid "Manager"
msgstr "مدير"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__module_oh_employee_check_list
msgid "Manages employee's entry & exit Process"
msgstr "يدير عملية دخول وخروج الموظف"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__03
msgid "March"
msgstr "مارس"

#. module: ohrms_salary_advance
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_hr_payroll_structure__max_percent
msgid "Max.Salary Advance Percentage"
msgstr "الحد الأقصى لنسبة الراتب مقدما"

#. module: oh_employee_check_list
#: model:ir.model.fields,field_description:oh_employee_check_list.field_hr_employee__maximum_rate
msgid "Maximum Rate"
msgstr "معدل الحد الأقصى"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__05
msgid "May"
msgstr "مايو"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_has_error
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_has_error
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_has_error
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_has_error
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_has_error
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_has_error
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_has_error
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_has_error
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_has_error
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسالة"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_ids
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_ids
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_ids
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_ids
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_ids
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_ids
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_ids
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_ids
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_ids
msgid "Messages"
msgstr "رسائل"

#. module: ohrmspro_holidays_approval
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_validators_users
msgid "Mode"
msgstr "الوضع"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__model_name
msgid "Model"
msgstr "نموذج"

#. module: ohrms_core
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Module is not Present in Your Repository."
msgstr ".الوحدة النمطية غير موجودة في المستودع الخاص بك"

#. module: hr_employee_shift
#: code:addons/hr_employee_shift/models/hr_employee_shift.py:0
#, python-format
msgid "Monday Morning"
msgstr "صباح الاثنين"

#. module: uae_wps_report
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__salary_month
msgid "Month of Salary"
msgstr "شهر الراتب"

#. module: hr_insurance
#: model:ir.model.fields.selection,name:hr_insurance.selection__hr_insurance__policy_coverage__monthly
msgid "Monthly"
msgstr "شهريا"

#. module: hr_gratuity_settlement
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_contract__wage_type__monthly
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_gratuity__wage_type__monthly
msgid "Monthly Fixed Wage"
msgstr "الأجر الثابت الشهري"

#. module: ohrmspro_holidays_approval
#: model:ir.model.fields.selection,name:ohrmspro_holidays_approval.selection__hr_leave_type__validation_type__multi
msgid "Multi Level Approval"
msgstr "متعدد المستويات الموافقة"

#. module: ohrmspro_holidays_approval
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_leave_type__multi_level_validation
msgid "Multiple Level Approval"
msgstr "الموافقة على مستوى متعدد"

#. module: ohrms_salary_advance
#: model:ir.ui.menu,name:ohrms_salary_advance.menu_my_salary_advance_approved
msgid "My Approved Salary Advance"
msgstr "سلف الراتب المعتمد"

#. module: oh_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_report_daily_attendance_search
msgid "My Attendance"
msgstr "حضوري"

#. module: ohrms_core
#. openerp-web
#: code:addons/ohrms_core/static/src/xml/link_view.xml:0
#, python-format
msgid "My Odoo.com account"
msgstr "حسابي على أودو"

#. modules: ohrms_salary_advance, ohrms_loan
#: model_terms:ir.ui.view,arch_db:ohrms_loan.view_loan_request_search_form
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
msgid "My Requests"
msgstr "طلباتي"

#. modules: hr_gratuity_settlement, oh_appraisal,
#. oh_employee_documents_expiry, ohrms_salary_advance, oh_hr_zk_attendance,
#. hr_reward_warning, hr_vacation_mngmt, oh_employee_check_list,
#. oh_hr_lawsuit_management, hr_insurance, hr_employee_transfer
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__name
#: model:ir.model.fields,field_description:hr_employee_transfer.field_transfer_company__name
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.employee_transfer
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__name
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__name
#: model:ir.model.fields,field_description:hr_insurance.field_insurance_policy__name
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_search
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__name
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal_stages__name
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__name
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_document_type__name
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__party2_name
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_report_daily_attendance_search
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__name
msgid "Name"
msgstr "اسم"

#. module: saudi_gosi
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__nationality
msgid "Nationality"
msgstr "جنسية"

#. modules: hr_employee_transfer, ohrms_service_request, saudi_gosi,
#. hr_gratuity_settlement
#: model:ir.model.fields.selection,name:hr_employee_transfer.selection__employee_transfer__state__draft
#: code:addons/hr_gratuity_settlement/models/hr_gratuity.py:0
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_contract__state__draft
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_training__state__new
#: code:addons/ohrms_service_request/models/service.py:0
#: code:addons/saudi_gosi/models/gosi.py:0
#, python-format
msgid "New"
msgstr "جديد"

#. module: ohrms_salary_advance
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
msgid "New Requests"
msgstr "طلبات جديدة"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__activity_date_deadline
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__activity_date_deadline
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__activity_date_deadline
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__activity_date_deadline
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__activity_date_deadline
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__activity_date_deadline
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__activity_date_deadline
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__activity_date_deadline
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__activity_date_deadline
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "آخر نشاط الموعد النهائي"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__activity_summary
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__activity_summary
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__activity_summary
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__activity_summary
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__activity_summary
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__activity_summary
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__activity_summary
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__activity_summary
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__activity_summary
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__activity_type_id
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__activity_type_id
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__activity_type_id
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__activity_type_id
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__activity_type_id
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__activity_type_id
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__activity_type_id
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__activity_type_id
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__activity_type_id
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__installment
msgid "No Of Installments"
msgstr "عدد الاقساط"

#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/hr_gratuity.py:0
#, python-format
msgid ""
"No contracts found for the selected employee...!\n"
"Employee must have at least one contract to compute gratuity settelement."
msgstr ""
"لم يتم العثور على عقود للموظف المختار...!\n"
"يجب أن يكون لدى الموظف عقد واحد على الأقل لحساب تسوية المكافآت."
#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/hr_gratuity.py:0
#, python-format
msgid ""
"No gratuity accounting configuration found or please set proper start date "
"and end date for gratuity configuration!"
msgstr ""
"لم يتم العثور على تكوين محاسبة على المكافأة أو يرجى تحديد تاريخ البدء المناسب "
"وتاريخ الانتهاء لتكوين مكافأة!"
#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/hr_gratuity.py:0
#, python-format
msgid "No suitable gratuity durations found !"
msgstr "! لم يتم العثور على فترات مكافأة مناسبة"

#. module: hr_employee_shift
#: code:addons/hr_employee_shift/models/hr_shift_payroll.py:0
#, python-format
msgid "Normal Working Days paid at 100%"
msgstr "100% أيام العمل العادية المدفوعة بنسبة"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,field_description:oh_employee_documents_expiry.field_hr_employee_document__notification_type
msgid "Notification Type"
msgstr "نوع إعلام"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields.selection,name:oh_employee_documents_expiry.selection__hr_employee_document__notification_type__multi
msgid "Notification before few days"
msgstr "الإخطار قبل أيام قليلة"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields.selection,name:oh_employee_documents_expiry.selection__hr_employee_document__notification_type__everyday_after
msgid "Notification on and after expiry"
msgstr "الإخطار في وبعد انتهاء الصلاحية"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields.selection,name:oh_employee_documents_expiry.selection__hr_employee_document__notification_type__single
msgid "Notification on expiry date"
msgstr "الإخطار في تاريخ انتهاء الصلاحية"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__11
msgid "November"
msgstr "نوفمبر"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_needaction_counter
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_needaction_counter
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_needaction_counter
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_needaction_counter
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_needaction_counter
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_needaction_counter
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_needaction_counter
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_needaction_counter
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_needaction_counter
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_has_error_counter
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_has_error_counter
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_has_error_counter
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_has_error_counter
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_has_error_counter
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_has_error_counter
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_has_error_counter
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_has_error_counter
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_has_error_counter
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الاخطاء"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__message_needaction_counter
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__message_needaction_counter
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__message_needaction_counter
#: model:ir.model.fields,help:oh_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__message_needaction_counter
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__message_needaction_counter
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__message_needaction_counter
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__message_needaction_counter
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__message_needaction_counter
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__message_needaction_counter
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__message_has_error_counter
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__message_has_error_counter
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__message_has_error_counter
#: model:ir.model.fields,help:oh_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__message_has_error_counter
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__message_has_error_counter
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__message_has_error_counter
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__message_has_error_counter
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__message_has_error_counter
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__message_has_error_counter
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل مع خطأ التسليم"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__message_unread_counter
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__message_unread_counter
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__message_unread_counter
#: model:ir.model.fields,help:oh_appraisal.field_hr_appraisal__message_unread_counter
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__message_unread_counter
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__message_unread_counter
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__message_unread_counter
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__message_unread_counter
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__message_unread_counter
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__message_unread_counter
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__employee_working_days
msgid "Number of working days per month"
msgstr "عدد أيام العمل في الشهر"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__10
msgid "October"
msgstr "اكتوبر"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__ticket_type__one
msgid "One Way"
msgstr "اتجاه واحد"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_vacation.py:0
#, python-format
msgid "Only an HR Officer or Manager can approve leave requests."
msgstr ".يمكن فقط للموظف البشري أو مدير الموارد البشرية الموافقة على طلبات الإجازات"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_vacation.py:0
#, python-format
msgid "Only an HR Officer or Manager can book flight tickets."
msgstr ".يمكن لموظف إدارة الموارد البشرية أو مديرها فقط حجز تذاكر الطيران"

#. module: ohrms_core
#. openerp-web
#: code:addons/ohrms_core/static/src/xml/link_view.xml:0
#, python-format
msgid "Open HRMS"
msgstr "افتح نظام إدارة الموارد البشرية"

#. modules: oh_employee_check_list, ohrms_service_request
#: model:ir.model.fields.selection,name:oh_employee_check_list.selection__employee_checklist__document_type__other
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__service_type__other
msgid "Other"
msgstr "آخر"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_leave__overlapping_leaves
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.hr_vacation_form_view
msgid "Overlapping Leaves"
msgstr "أوراق متداخلة"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__punch_type__4
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__punch_type__4
msgid "Overtime In"
msgstr "العمل الإضافي في"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__punch_type__5
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__punch_type__5
msgid "Overtime Out"
msgstr "العمل الإضافي خارج"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__paid
msgid "Paid"
msgstr "دفع"

#. module: oh_hr_lawsuit_management
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__party1
msgid "Party 1"
msgstr "الحزب 1"

#. module: oh_hr_lawsuit_management
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__party2
msgid "Party 2"
msgstr "الحزب 2"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__3
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__attendance_type__3
msgid "Password"
msgstr "كلمة المرور"

#. modules: ohrms_salary_advance, ohrms_loan_accounting, hr_vacation_mngmt,
#. hr_insurance, ohrms_loan, hr_employee_shift, saudi_gosi
#: model:ir.model,name:hr_employee_shift.model_hr_payslip
#: model:ir.model,name:hr_insurance.model_hr_payslip
#: model:ir.model,name:hr_vacation_mngmt.model_hr_payslip
#: model:ir.model,name:ohrms_loan.model_hr_payslip
#: model:ir.model,name:ohrms_loan_accounting.model_hr_payslip
#: model:ir.model,name:ohrms_salary_advance.model_hr_payslip
#: model:ir.model,name:saudi_gosi.model_hr_payslip
msgid "Pay Slip"
msgstr "قسيمة المرتب"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__date
msgid "Payment Date"
msgstr "موعد الدفع"

#. module: ohrms_salary_advance
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__payment_method
msgid "Payment Method"
msgstr "طريقة الدفع او السداد"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__payment_date
msgid "Payment Start Date"
msgstr "تاريخ بدء الدفع"

#. module: hr_multi_company
#: model:ir.model,name:hr_multi_company.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "دفعات المرتب"

#. module: ohrms_loan
#: model:ir.model,name:ohrms_loan.model_hr_payslip_input
msgid "Payslip Input"
msgstr "مدخلات المرتب"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan_line__payslip_id
msgid "Payslip Ref."
msgstr ".كشوف المرتبات"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_leave__pending_tasks
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_task_reassign__pending_tasks
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_pending_task
msgid "Pending Tasks"
msgstr "المهام العالقة"

#. module: hr_vacation_mngmt
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.hr_vacation_form_view
msgid "Pending Works"
msgstr "يعمل في انتظار"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__percentage
msgid "Percentage"
msgstr "النسبة المئوية"

#. module: ohrms_service_request
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.projection_view_pivot
msgid "Pivot View"
msgstr "عرض المحور"

#. module: oh_appraisal
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_form_view
msgid "Plan"
msgstr "خطة"

#. module: ohrms_loan
#: code:addons/ohrms_loan/models/hr_loan.py:0
#, python-format
msgid "Please Compute installment"
msgstr "يرجى حساب الدفعة"

#. module: uae_wps_report
#: code:addons/uae_wps_report/wizard/wizard.py:0
#, python-format
msgid "Please Set Company Registry Number First"
msgstr "يرجى تعيين رقم تسجيل الشركة أولاً"

#. module: uae_wps_report
#: code:addons/uae_wps_report/wizard/wizard.py:0
#, python-format
msgid "Please Set Employee Card Number of All Employees"
msgstr "يرجى تعيين رقم بطاقة الموظف لجميع الموظفين"

#. module: uae_wps_report
#: code:addons/uae_wps_report/wizard/wizard.py:0
#, python-format
msgid "Please Set Labour Card Number of All Employees"
msgstr "يرجى تعيين رقم بطاقة العمل لجميع الموظفين"

#. module: uae_wps_report
#: code:addons/uae_wps_report/wizard/wizard.py:0
#, python-format
msgid "Please Set Salary Card Number / Account Number of All Employees"
msgstr "يرجى تعيين رقم بطاقة الراتب / رقم الحساب لجميع الموظفين"

#. module: uae_wps_report
#: code:addons/uae_wps_report/wizard/wizard.py:0
#, python-format
msgid "Please Set a User Timezone"
msgstr "يرجى تحديد المنطقة الزمنية للمستخدم"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_employee_ticket.py:0
#, python-format
msgid "Please add ticket fare."
msgstr ".يرجى إضافة تذكرة التذكرة"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/wizard/reassign_task.py:0
#, python-format
msgid "Please assign pending task to employees."
msgstr ".يرجى تعيين المهمة المعلقة للموظفين"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_employee_ticket.py:0
#, python-format
msgid "Please select expense account for the flight tickets."
msgstr ".يرجى تحديد حساب المصاريف لتذاكر الطيران"

#. module: hr_insurance
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__policy_id
msgid "Policy"
msgstr "سياسات"

#. module: hr_insurance
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__amount
msgid "Policy Amount"
msgstr "مبلغ السياسة"

#. module: hr_insurance
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__policy_coverage
msgid "Policy Coverage"
msgstr "تغطية السياسة"

#. module: hr_insurance
#: model_terms:ir.ui.view,arch_db:hr_insurance.insurance_policy_details_form
#: model_terms:ir.ui.view,arch_db:hr_insurance.insurance_policy_details_tree
msgid "Policy Details"
msgstr "تفاصيل السياسة"

#. module: hr_insurance
#: model_terms:ir.ui.view,arch_db:hr_insurance.employee_insurance__details_form
msgid "Policy Period"
msgstr "فترة السياسة"

#. module: hr_reminder
#: model_terms:ir.ui.view,arch_db:hr_reminder.hr_reminder_tree_view
msgid "Pop-Up Reminder"
msgstr "تذكير منبثق"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine__port_no
msgid "Port No"
msgstr "ميناء رقم"

#. module: ohrms_core
#. openerp-web
#: code:addons/ohrms_core/static/src/xml/link_view.xml:0
#, python-format
msgid "Preferences"
msgstr "التفضيلات"

#. module: hr_leave_request_aliasing
#: model_terms:ir.ui.view,arch_db:hr_leave_request_aliasing.view_hr_leave_configuration
msgid "Prefix"
msgstr "اختصار"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__ticket_class__premium_economy
msgid "Premium Economy"
msgstr "اقتصاد مميز"

#. module: uae_wps_report
#: model_terms:ir.ui.view,arch_db:uae_wps_report.wps_wizard_form
msgid "Print xlsx"
msgstr "xlsx طباعة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_contract__probation_id
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_contract__state__probation
msgid "Probation"
msgstr "فترة التجربة"

#. module: hr_gratuity_settlement
#: model:ir.actions.act_window,name:hr_gratuity_settlement.training_menu_action
#: model:ir.ui.menu,name:hr_gratuity_settlement.menu_hr_training
msgid "Probation Details"
msgstr "تفاصيل الاختبار"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_priority
msgid "Probationary Details"
msgstr "التفاصيل تحت الاختبار"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_contract__training_info
msgid "Probationary Info"
msgstr "معلومات الاختبار"

#. module: oh_hr_lawsuit_management
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.hr_lawsuit_form_view
msgid "Process"
msgstr "معالجة"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__project_id
msgid "Project"
msgstr "مشروع"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__punching_time
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_report_daily_attendance__punching_time
msgid "Punching Time"
msgstr "اللكم الوقت"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__punch_type
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_report_daily_attendance__punch_type
msgid "Punching Type"
msgstr "نوع اللكم"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/models/hr_vacation.py:0
#, python-format
msgid "Re-Assign Task"
msgstr "إعادة تعيين المهمة"

#. module: ohrms_salary_advance
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__reason
msgid "Reason"
msgstr "السبب"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__service_name
msgid "Reason For Service"
msgstr "سبب الخدمة"

#. module: hr_employee_transfer
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.employee_transfer
msgid "Receive"
msgstr "تسلم"

#. modules: hr_gratuity_settlement, ohrms_service_request, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__name
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__name
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__name
msgid "Reference"
msgstr "مرجع"

#. modules: ohrmspro_holidays_approval, hr_reward_warning,
#. ohrms_loan_accounting, ohrms_loan
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_form
#: model_terms:ir.ui.view,arch_db:ohrms_loan.hr_loan_form_view
#: model_terms:ir.ui.view,arch_db:ohrms_loan_accounting.hr_loan_inherited
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_validators
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_validators_leave_create
msgid "Refuse"
msgstr "رفض"

#. modules: hr_reward_warning, ohrms_loan_accounting, ohrms_loan
#: model:ir.model.fields.selection,name:hr_reward_warning.selection__hr_announcement__state__rejected
#: model:ir.model.fields.selection,name:ohrms_loan.selection__hr_loan__state__refuse
#: model:ir.model.fields.selection,name:ohrms_loan_accounting.selection__hr_loan__state__refuse
msgid "Refused"
msgstr "رفض"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__regularization
msgid "Regularization"
msgstr "تسوية"

#. modules: ohrms_salary_advance, ohrms_service_request
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_form
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_request11
msgid "Reject"
msgstr "رفض"

#. modules: ohrms_salary_advance, ohrms_service_request
#: model:ir.model.fields.selection,name:ohrms_salary_advance.selection__salary_advance__state__reject
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_execute__state_execute__reject
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__state__reject
msgid "Rejected"
msgstr "مرفوض"

#. module: hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__rel_hr_schedule
msgid "Rel Hr Schedule"
msgstr "جدول الموارد البشرية ذات الصلة"

#. module: oh_employee_creation_from_user
#: model:ir.model.fields,field_description:oh_employee_creation_from_user.field_res_users__employee_id
msgid "Related Employee"
msgstr "موظف ذو صلة"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_leave__remaining_leaves
msgid "Remaining Legal Leaves"
msgstr "الأوراق القانونية المتبقية"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__reminder_active
msgid "Reminder Active"
msgstr "تذكير بالموقع"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_res_config_settings__reminder_day_before
msgid "Reminder Day Before"
msgstr "تذكير يوم قبل"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__expiry_date
msgid "Reminder Expiry Date"
msgstr "تذكير تاريخ انتهاء الصلاحية"

#. module: hr_reminder
#: model_terms:ir.ui.view,arch_db:hr_reminder.hr_reminder_form_view
msgid "Reminder Title..."
msgstr "...عنوان التذكير"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__days_before
msgid "Reminder before"
msgstr "تذكير من قبل"

#. module: hr_reminder
#: model:ir.actions.server,name:hr_reminder.ir_cron_scheduler_reminder_action_ir_actions_server
#: model:ir.cron,cron_name:hr_reminder.ir_cron_scheduler_reminder_action
#: model:ir.cron,name:hr_reminder.ir_cron_scheduler_reminder_action
msgid "Reminder scheduler"
msgstr "جدولة تذكير"

#. module: hr_vacation_mngmt
#: model:mail.template,subject:hr_vacation_mngmt.email_template_hr_leave_reminder_mail
msgid "Reminder: ${object.display_name}"
msgstr "${object.display_name} :تذكير"

#. module: hr_reminder
#. openerp-web
#: code:addons/hr_reminder/static/src/xml/reminder_topbar.xml:0
#: code:addons/hr_reminder/static/src/xml/reminder_topbar.xml:0
#: model:ir.actions.act_window,name:hr_reminder.action_hr_reminder
#: model:ir.ui.menu,name:hr_reminder.hr_reminder_menu
#, python-format
msgid "Reminders"
msgstr "تذكير"

#. module: ohrms_service_request
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__service_type__repair
msgid "Repair"
msgstr "يصلح"

#. module: ohrms_service_request
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__service_type__replace
msgid "Replace"
msgstr "يحل محل"

#. module: uae_wps_report
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__report_file
msgid "Report File"
msgstr "ملف التقرير"

#. module: ohrms_service_request
#: model:ir.ui.menu,name:ohrms_service_request.service_report
msgid "Reporting"
msgstr "التقارير"

#. module: ohrms_salary_advance
#: model:ir.ui.menu,name:ohrms_salary_advance.menu_my_salary_advance
msgid "Request Salary Advance"
msgstr "طلب الراتب مقدما"

#. module: ohrms_service_request
#: model:ir.ui.menu,name:ohrms_service_request.service_requests
msgid "Request Service"
msgstr "طلب خدمة"

#. module: ohrms_salary_advance
#: code:addons/ohrms_salary_advance/models/salary_advance.py:0
#, python-format
msgid "Request can be done after \"%s\" Days From prevoius month salary"
msgstr "يمكن تقديم الطلب بعد \"%s\" أيام من راتب الشهر السابق"

#. module: ohrms_loan
#: model:ir.actions.act_window,name:ohrms_loan.action_hr_loan_request
#: model:ir.ui.menu,name:ohrms_loan.menu_hr_loan_request
msgid "Request for Loan"
msgstr "طلب قرض"

#. module: ohrms_service_request
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_execute__state_execute__requested
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__state__requested
msgid "Requested"
msgstr "طلب"

#. module: hr_reward_warning
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__requested_date
msgid "Requested Date"
msgstr "التاريخ المطلوب"

#. module: ohrms_core
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Resignation Process"
msgstr "عملية الاستقالة"

#. module: hr_employee_shift
#: model:ir.model,name:hr_employee_shift.model_resource_calendar
msgid "Resource Working Time"
msgstr "فترة عمل المورد"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__response_id
msgid "Response"
msgstr "استجابة"

#. module: hr_employee_transfer
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__responsible
msgid "Responsible"
msgstr "مسؤول"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__activity_user_id
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__activity_user_id
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__activity_user_id
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__activity_user_id
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__activity_user_id
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__activity_user_id
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__activity_user_id
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__activity_user_id
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__activity_user_id
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__date_return
msgid "Return Date"
msgstr "تاريخ العودة"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__return_flight_details
msgid "Return Flight Details"
msgstr "العودة تفاصيل الرحلة"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__ticket_type__round
msgid "Round Trip"
msgstr "ذهابا وإيابا"

#. module: uae_wps_report
#: model:ir.model.fields,field_description:uae_wps_report.field_res_bank__routing_code
msgid "Routing Code"
msgstr "كود توجيه"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
msgid "Rules"
msgstr "قواعد"

#. modules: hr_gratuity_settlement, oh_hr_lawsuit_management
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_contract__state__open
#: model:ir.model.fields.selection,name:oh_hr_lawsuit_management.selection__hr_lawsuit__state__running
msgid "Running"
msgstr "جاري"

#. module: uae_wps_report
#: model:ir.ui.menu,name:uae_wps_report.wps_wizard_menu
msgid "SIF"
msgstr "SIF"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_has_sms_error
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_has_sms_error
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_has_sms_error
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_has_sms_error
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_has_sms_error
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_has_sms_error
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_has_sms_error
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_has_sms_error
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_has_sms_error
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة"

#. module: ohrms_salary_advance
#: model:ir.actions.act_window,name:ohrms_salary_advance.action_my_salary_advance
#: model:ir.actions.act_window,name:ohrms_salary_advance.action_my_salary_advance_request_approved
#: model:ir.actions.act_window,name:ohrms_salary_advance.action_salary_advance_to_approve
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_form
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_tree
msgid "Salary Advance"
msgstr "سلفه على الراتب"

#. module: ohrms_salary_advance
#: model:ir.ui.menu,name:ohrms_salary_advance.menu_salary_advance
msgid "Salary Advance To Approve"
msgstr "الراتب مقدما للموافقة"

#. module: ohrms_salary_advance
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_hr_payroll_structure__advance_date
msgid "Salary Advance-After days"
msgstr "الراتب مقدما بعد أيام"

#. module: uae_wps_report
#: model:ir.model.fields,field_description:uae_wps_report.field_hr_employee__salary_card_number
msgid "Salary Card Number/Account Number"
msgstr "رقم بطاقة الراتب / رقم الحساب"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.hr_contract_form_inherit_wage
msgid "Salary Information"
msgstr "معلومات الراتب"

#. module: hr_multi_company
#: model:ir.model,name:hr_multi_company.model_hr_salary_rule_category
msgid "Salary Rule Category"
msgstr "فئة قاعدة مرتبات"

#. module: ohrms_loan
#: code:addons/ohrms_loan/models/hr_payroll.py:0
#, python-format
msgid "Salary Slip of %s for %s"
msgstr "قسيمة مرتب %s إلى عن على %s"

#. module: ohrms_salary_advance
#: model:ir.model,name:ohrms_salary_advance.model_hr_payroll_structure
msgid "Salary Structure"
msgstr "هيكلية الرواتب"

#. module: hr_insurance
#: model:ir.model.fields,field_description:hr_insurance.field_hr_employee__deduced_amount_per_month
msgid "Salary deduced per month"
msgstr "يتم خصم الراتب شهريًا"

#. module: hr_insurance
#: model:ir.model.fields,field_description:hr_insurance.field_hr_employee__deduced_amount_per_year
msgid "Salary deduced per year"
msgstr "الراتب المستقطع سنويا"

#. module: saudi_gosi
#: model:ir.model.fields.selection,name:saudi_gosi.selection__hr_employee__type__saudi
msgid "Saudi"
msgstr "سعودي"

#. module: ohrms_salary_advance
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
msgid "Search"
msgstr "بحث"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__search_by
msgid "Search By"
msgstr "البحث بواسطة"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__emp_survey_id
msgid "Select Appraisal Form"
msgstr "اختر نموذج التقييم"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__hr_colleague_id
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__hr_colloborator_id
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__hr_manager_id
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_form_view
msgid "Select Appraisal Reviewer"
msgstr "حدد تقييم المعلقين"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__date_set
msgid "Select Date"
msgstr "حدد تاريخ"

#. module: ohrms_service_request
#: code:addons/ohrms_service_request/models/service.py:0
#, python-format
msgid "Select Executer For the Requested Service"
msgstr "حدد المنفذ للخدمة المطلوبة"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__colleague_survey_id
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__colloborator_survey_id
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__manager_survey_id
msgid "Select Opinion Form"
msgstr "اختر نموذج الرأي"

#. module: hr_employee_transfer
#: model:ir.model.fields,help:hr_employee_transfer.field_employee_transfer__employee_id
msgid "Select the employee you are going to transfer"
msgstr "حدد الموظف الذي ستنقله"

#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/hr_gratuity.py:0
#, python-format
msgid "Selected Employee is not eligible for Gratuity Settlement"
msgstr "الموظف المختار غير مؤهل للحصول على تسوية المكافأة"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/wizard/reassign_task.py:0
#, python-format
msgid "Selected employee %s is not available"
msgstr "غير متاح %s الموظف المختار"

#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/hr_gratuity.py:0
#, python-format
msgid "Selected employee have multiple or no running contracts!"
msgstr "!الموظف المختار لديه عقود متعددة أو معدومة"

#. module: hr_vacation_mngmt
#: code:addons/hr_vacation_mngmt/wizard/reassign_task.py:0
#, python-format
msgid "Selected employees %s are not available"
msgstr "ليست متاحة %s الموظف المختار"

#. module: hr_reward_warning
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_form
msgid "Send For Approval"
msgstr "إرسال للموافقة"

#. module: hr_vacation_mngmt
#: model_terms:ir.ui.view,arch_db:hr_vacation_mngmt.view_hr_leave_configuration
msgid "Send leave remainder emails to holiday managers"
msgstr "إرسال إجازة رسائل البريد الإلكتروني المتبقية لمديري العطلات"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,help:hr_vacation_mngmt.field_res_config_settings__leave_reminder
msgid "Send leave remainder emails to hr managers"
msgstr "إرسال إجازة تذكير البريد الإلكتروني لمديري الموارد البشرية"

#. module: oh_appraisal
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_kanban
msgid "Sent Questions:"
msgstr ":الأسئلة المرسلة"

#. module: uae_wps_report
#: model:ir.model.fields.selection,name:uae_wps_report.selection__wps_wizard__salary_month__09
msgid "September"
msgstr "سبتمبر"

#. modules: oh_appraisal, hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_resource_calendar__sequence
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal_stages__sequence
msgid "Sequence"
msgstr "تسلسل"

#. module: hr_employee_transfer
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__sequence_number
msgid "Sequence Number"
msgstr "رقم التسلسل"

#. module: ohrms_service_request
#: model:ir.module.category,name:ohrms_service_request.module_service_execute
msgid "Service"
msgstr "الخدمات"

#. module: ohrms_service_request
#: model:ir.actions.act_window,name:ohrms_service_request.action_view_service_approve
#: model:ir.ui.menu,name:ohrms_service_request.service_approve
msgid "Service Approval"
msgstr "موافقة الخدمة"

#. module: ohrms_service_request
#: model:ir.actions.act_window,name:ohrms_service_request.action_view_service_check
msgid "Service Check"
msgstr "فحص الخدمة"

#. modules: ohrms_service_request, saudi_gosi
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_execute2
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.view_execute_tree1
#: model_terms:ir.ui.view,arch_db:saudi_gosi.gosi_payslip3
#: model_terms:ir.ui.view,arch_db:saudi_gosi.view_gosisaudi
msgid "Service Execute"
msgstr "تنفيذ الخدمة"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__service_executer
#: model:res.groups,name:ohrms_service_request.service_group_executer
msgid "Service Executer"
msgstr "منفذي الخدمة"

#. module: ohrms_service_request
#: model:ir.ui.menu,name:ohrms_service_request.execute_service
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_execute2
msgid "Service Execution"
msgstr "تنفيذ الخدمة"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__executer_product
msgid "Service Item"
msgstr "بند الخدمة"

#. module: ohrms_service_request
#: model:ir.ui.menu,name:ohrms_service_request.service_pivot
msgid "Service Reporting"
msgstr "خدمة التقارير"

#. module: ohrms_service_request
#: model:ir.actions.act_window,name:ohrms_service_request.action_view_service_requests
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_request11
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.view_service_tree1
msgid "Service Request"
msgstr "طلب خدمة"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__type_service
msgid "Service Type"
msgstr "نوع الخدمة"

#. module: ohrms_service_request
#: model:ir.ui.menu,name:ohrms_service_request.service_root
msgid "Services"
msgstr "خدمات"

#. module: hr_reminder
#: model:ir.model.fields.selection,name:hr_reminder.selection__hr_reminder__search_by__set_date
msgid "Set Date"
msgstr "تحديد التاريخ"

#. module: hr_reminder
#: model:ir.model.fields.selection,name:hr_reminder.selection__hr_reminder__search_by__set_period
msgid "Set Period"
msgstr "ضبط الفترة"

#. module: oh_appraisal
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_form_view
msgid "Set To Draft"
msgstr "تعيين إلى مشروع"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
msgid "Set to draft"
msgstr "تعيين إلى مشروع"

#. module: hr_leave_request_aliasing
#: model:ir.ui.menu,name:hr_leave_request_aliasing.menu_hr_leave_global_settings
msgid "Settings"
msgstr "الإعدادات"

#. module: hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_schedule__hr_shift
#: model:ir.ui.menu,name:hr_employee_shift.menu_shift_schedule_generate_id_menu
msgid "Shift"
msgstr "تحول"

#. module: hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_contract__shift_schedule
#: model_terms:ir.ui.view,arch_db:hr_employee_shift.employee_contract_form_inherited
msgid "Shift Schedule"
msgstr "جدول المناوبة"

#. module: hr_employee_shift
#: model:ir.ui.menu,name:hr_employee_shift.menu_conf_shift
#: model:ir.ui.menu,name:hr_employee_shift.menu_shift
msgid "Shifts"
msgstr "التحولات"

#. module: oh_appraisal
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__state
msgid "Stage"
msgstr "المسرح"

#. module: oh_appraisal
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_form_view
msgid "Start Appraisal And Send Forms"
msgstr "بدء التقييم وإرسال النماذج"

#. modules: hr_gratuity_settlement, uae_wps_report, hr_reward_warning,
#. hr_vacation_mngmt, hr_reminder, hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_shift_generate__start_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity_accounting_configuration__gratuity_start_date
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__start_date
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__date_from
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__date_start
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__date_start
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__start_date
msgid "Start Date"
msgstr "تاريخ البدء"

#. module: hr_employee_shift
#: code:addons/hr_employee_shift/models/hr_employee_contract.py:0
#, python-format
msgid "Start date should be less than end date."
msgstr ".يجب أن يكون تاريخ البدء أقل من تاريخ الانتهاء"

#. module: hr_vacation_mngmt
#: model:ir.model.fields.selection,name:hr_vacation_mngmt.selection__hr_flight_ticket__state__started
msgid "Started"
msgstr "بدأت"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_loan_accounting, ohrms_service_request, hr_reward_warning,
#. hr_insurance, ohrms_loan
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__state
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_training__state
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__state
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_search
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__state
#: model:ir.model.fields,field_description:ohrms_loan_accounting.field_hr_loan__state
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__state
msgid "State"
msgstr "حالة"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__state_execute
msgid "State Execute"
msgstr "تنفيذ الدولة"

#. modules: hr_gratuity_settlement, ohrms_salary_advance, hr_reward_warning,
#. oh_hr_lawsuit_management, hr_vacation_mngmt, hr_employee_transfer,
#. ohrms_loan
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__state
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_contract__state
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__state
#: model_terms:ir.ui.view,arch_db:hr_reward_warning.view_hr_announcement_search
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__state
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__state
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.hr_lawsuit_search_view
#: model_terms:ir.ui.view,arch_db:ohrms_loan.view_loan_request_search_form
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__state
msgid "Status"
msgstr "الحالة"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__activity_state
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__activity_state
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__activity_state
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__activity_state
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__activity_state
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__activity_state
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__activity_state
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__activity_state
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__activity_state
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الوضع القائم على الأنشطة\n"
"متأخر: تاريخ الاستحقاق مرت بالفعل\n"
"اليوم: تاريخ النشاط اليوم\n"
"تنظيم: الأنشطة المستقبلية."
#. module: hr_gratuity_settlement
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_contract__state
msgid "Status of the contract"
msgstr "حالة العقد"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_loan_accounting, ohrms_service_request, ohrms_loan
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.employee_gratuity_form
#: model_terms:ir.ui.view,arch_db:ohrms_loan.hr_loan_form_view
#: model_terms:ir.ui.view,arch_db:ohrms_loan_accounting.hr_loan_inherited
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_form
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_request11
msgid "Submit"
msgstr "إرسال"

#. modules: hr_gratuity_settlement, ohrms_loan_accounting,
#. ohrms_salary_advance, ohrms_loan
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_gratuity__state__submit
#: model:ir.model.fields.selection,name:ohrms_loan.selection__hr_loan__state__waiting_approval_1
#: model:ir.model.fields.selection,name:ohrms_loan_accounting.selection__hr_loan__state__waiting_approval_1
#: model:ir.model.fields.selection,name:ohrms_salary_advance.selection__salary_advance__state__submit
msgid "Submitted"
msgstr "قدمت"

#. module: ohrms_salary_advance
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
msgid "Submitted Requests"
msgstr "الطلبات المقدمة"

#. module: hr_insurance
#: model:ir.model.fields,field_description:hr_insurance.field_hr_insurance__sum_insured
msgid "Sum Insured"
msgstr "مبلغ التأمين"

#. module: oh_appraisal
#: model:ir.model,name:oh_appraisal.model_survey_user_input
msgid "Survey User Input"
msgstr "مُدخلات مستخدم الاستطلاع"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__name
msgid "Task"
msgstr "مهمة"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__test_module_hr_custody
msgid "Test Module Hr Custody"
msgstr "اختبار وحدة الموارد البشرية حراسة"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__test_module_hr_employee_shift
msgid "Test Module Hr Employee Shift"
msgstr "اختبار وحدة الموارد البشرية تحول الموظف"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__test_module_hr_insurance
msgid "Test Module Hr Insurance"
msgstr "وحدة اختبار تأمين الموارد البشرية"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__test_module_hr_resignation
msgid "Test Module Hr Resignation"
msgstr "استقالة اختبار وحدة الموارد البشرية"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__test_module_hr_vacation_mngmt
msgid "Test Module Hr Vacation Mngmt"
msgstr "اختبار وحدة إدارة الموارد البشرية عطلة"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__test_module_oh_hr_lawsuit_management
msgid "Test Module Oh Hr Lawsuit Management"
msgstr "اختبار وحدة إدارة الموارد البشرية الدعوى"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__test_module_oh_hr_zk_attendance
msgid "Test Module Oh Hr Zk Attendance"
msgstr "اختبار وحدة من الموارد البشرية الحضور"

#. module: ohrms_core
#: model:ir.model.fields,field_description:ohrms_core.field_res_config_settings__test_oh_employee_check_list
msgid "Test Oh Employee Check List"
msgstr "اختبار فتح نظام إدارة الموارد البشرية الموظف المرجعية"

#. module: ohrms_salary_advance
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__exceed_condition
msgid "The Advance is greater than the maximum percentage in salary structure"
msgstr "السلفة أكبر من النسبة المئوية القصوى في هيكل الرواتب"

#. module: uae_wps_report
#: code:addons/uae_wps_report/wizard/wizard.py:0
#, python-format
msgid "The Dates Can of Same Month Only"
msgstr ""

#. module: hr_employee_shift
#: code:addons/hr_employee_shift/models/hr_employee_contract.py:0
#, python-format
msgid "The dates may not overlap with one another."
msgstr ".قد لا تتداخل التواريخ مع بعضها البعض"

#. module: ohrms_loan
#: code:addons/ohrms_loan/models/hr_loan.py:0
#, python-format
msgid "The employee has already a pending installment"
msgstr "الموظف لديه بالفعل قسط معلقة"

#. module: uae_wps_report
#: code:addons/uae_wps_report/wizard/wizard.py:0
#, python-format
msgid "There are no payslip Created for the selected month"
msgstr "لا توجد كشوف تم إنشاؤها للشهر المحدد"

#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/hr_gratuity.py:0
#, python-format
msgid ""
"There is a date conflict in Gratuity accounting configuration. Please remove"
" the conflict and try again!"
msgstr ""
"يوجد تعارض في تاريخ تكوين المكافأة. يرجى إزالة"
" الصراع وحاول مرة أخرى!"
#. module: uae_wps_report
#: code:addons/uae_wps_report/wizard/wizard.py:0
#, python-format
msgid "There is no payslips created for this month"
msgstr "لا توجد كشوف تم إنشاؤها لهذا الشهر"

#. module: oh_appraisal
#: model:ir.model.fields,help:oh_appraisal.field_hr_appraisal_stages__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"يتم طي هذه المرحلة في طريقة عرض kanban عند عدم وجود سجلات في ذلك "
"مرحلة لعرض."
#. module: hr_employee_shift
#: code:addons/hr_employee_shift/models/hr_employee_shift.py:0
#, python-format
msgid "Thursday Morning"
msgstr "صباح الخميس"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__ticket_fare
msgid "Ticket Fare"
msgstr "أجرة تذكرة"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_hr_flight_ticket__ticket_type
msgid "Ticket Type"
msgstr "نوع التذكرة"

#. module: uae_wps_report
#: model:ir.model.fields,field_description:uae_wps_report.field_wps_wizard__time
msgid "Time"
msgstr "زمن"

#. modules: hr_gratuity_settlement, hr_multi_company,
#. ohrmspro_holidays_approval, hr_vacation_mngmt, hr_leave_request_aliasing
#: model:ir.model,name:hr_gratuity_settlement.model_hr_leave
#: model:ir.model,name:hr_leave_request_aliasing.model_hr_leave
#: model:ir.model,name:hr_multi_company.model_hr_leave
#: model:ir.model,name:hr_vacation_mngmt.model_hr_leave
#: model:ir.model,name:ohrmspro_holidays_approval.model_hr_leave
msgid "Time Off"
msgstr "غادر"

#. module: ohrmspro_holidays_approval
#: model:ir.model,name:ohrmspro_holidays_approval.model_hr_leave_type
msgid "Time Off Type"
msgstr "نوع الإجازة"

#. modules: hr_reward_warning, hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__name
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__announcement_reason
msgid "Title"
msgstr "عنوان"

#. module: ohrms_service_request
#: model_terms:ir.ui.view,arch_db:ohrms_service_request.service_request11
msgid "Title For Service"
msgstr "العنوان للخدمة"

#. module: ohrms_salary_advance
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
msgid "To Approve"
msgstr "ليوافق"

#. module: ohrms_salary_advance
#: model_terms:ir.ui.view,arch_db:ohrms_salary_advance.view_salary_advance_filter
msgid "To Submit"
msgstr "لتقديم"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__to_year
msgid "To Year"
msgstr "إلى سنة"

#. modules: oh_hr_zk_attendance, hr_reminder
#: model:ir.model.fields.selection,name:hr_reminder.selection__hr_reminder__search_by__today
#: model_terms:ir.ui.view,arch_db:oh_hr_zk_attendance.view_zk_report_daily_attendance_search
msgid "Today"
msgstr "اليوم"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__total_amount
msgid "Total Amount"
msgstr "المبلغ الإجمالي"

#. module: ohrms_loan
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__total_paid_amount
msgid "Total Paid Amount"
msgstr "إجمالي المبلغ المدفوع"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__total_working_years
msgid "Total Years Worked"
msgstr "مجموع سنوات العمل"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,help:hr_vacation_mngmt.field_hr_leave__remaining_leaves
msgid ""
"Total number of paid time off allocated to this employee, change this value "
"to create allocation/time off request. Total based on all the time off types"
" without overriding limit."
msgstr ""
"إجمالي عدد الإجازات المدفوعة المخصصة لهذا الموظف ، قم بتغيير هذه القيمة "
"لإنشاء تخصيص / إجازة طلب. المجموع على أساس كل الوقت قبالة أنواع"
" دون تجاوز الحد."
#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_contract__training_amount
msgid "Training Amount"
msgstr "مبلغ التدريب"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.training_from_view
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.training_tree_view
msgid "Training Details"
msgstr "تفاصيل التدريب"

#. module: hr_employee_transfer
#: model:ir.actions.act_window,name:hr_employee_transfer.action_employee_transfer
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.employee_transfer
msgid "Transfer"
msgstr "نقل"

#. module: hr_employee_transfer
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__branch
msgid "Transfer Branch"
msgstr "فرع النقل"

#. module: hr_employee_transfer
#: model:ir.model,name:hr_employee_transfer.model_transfer_company
msgid "Transfer Company"
msgstr "شركة نقل"

#. module: hr_employee_transfer
#: model:ir.model.fields,field_description:hr_employee_transfer.field_employee_transfer__transferred
#: model:ir.model.fields,field_description:hr_employee_transfer.field_hr_contract__from_transfer
#: model:ir.model.fields.selection,name:hr_employee_transfer.selection__employee_transfer__state__transfer
#: model_terms:ir.ui.view,arch_db:hr_employee_transfer.view_employee_transfer_filter
msgid "Transferred"
msgstr "نقل"

#. module: hr_employee_transfer
#: model:ir.model.fields,field_description:hr_employee_transfer.field_hr_contract__emp_transfer
msgid "Transferred Employee"
msgstr "الموظف المحول"

#. module: hr_employee_transfer
#: model:ir.ui.menu,name:hr_employee_transfer.menu_employee_transfer
msgid "Transfers"
msgstr "نقل"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_res_config_settings__default_expense_account
msgid "Travel Expense Account"
msgstr "حساب مصاريف السفر"

#. module: ohrms_loan_accounting
#: model:ir.model.fields,field_description:ohrms_loan_accounting.field_hr_loan__treasury_account_id
msgid "Treasury Account"
msgstr "حساب الخزينة"

#. module: hr_employee_shift
#: code:addons/hr_employee_shift/models/hr_employee_shift.py:0
#, python-format
msgid "Tuesday Morning"
msgstr "صباح الثلاثاء"

#. module: saudi_gosi
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__type_gosi
#: model:ir.model.fields,field_description:saudi_gosi.field_hr_employee__type
msgid "Type"
msgstr "اكتب"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__service_type
msgid "Type Of Service"
msgstr "نوع الخدمة"

#. module: oh_employee_check_list
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__document_type
msgid "Type of Checklist"
msgstr "نوع قائمة التحقق"

#. module: oh_employee_check_list
#: model:ir.model.fields,help:oh_employee_check_list.field_hr_employee_document__document_name
msgid "Type of Document"
msgstr "نوع المستند"

#. modules: hr_gratuity_settlement, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__activity_exception_decoration
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__activity_exception_decoration
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__activity_exception_decoration
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__activity_exception_decoration
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__activity_exception_decoration
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__activity_exception_decoration
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__activity_exception_decoration
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__activity_exception_decoration
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__activity_exception_decoration
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ".نوع نشاط الاستثناء في السجل"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__2
#: model:ir.model.fields.selection,name:oh_hr_zk_attendance.selection__zk_report_daily_attendance__attendance_type__2
msgid "Type_2"
msgstr "النوع 2"

#. module: oh_hr_zk_attendance
#: code:addons/oh_hr_zk_attendance/models/zk_machine.py:0
#: code:addons/oh_hr_zk_attendance/models/zk_machine.py:0
#, python-format
msgid ""
"Unable to connect, please check the parameters and network connections."
msgstr ""
"غير قادر على الاتصال ، يرجى التحقق من المعلمات واتصالات الشبكة."

#. module: oh_hr_zk_attendance
#: code:addons/oh_hr_zk_attendance/models/zk_machine.py:0
#: code:addons/oh_hr_zk_attendance/models/zk_machine.py:0
#, python-format
msgid "Unable to get the attendance log, please try again later."
msgstr ".غير قادر على الحصول على سجل الحضور ، يرجى المحاولة مرة أخرى لاحقًا"

#. module: hr_vacation_mngmt
#: model:ir.model.fields,field_description:hr_vacation_mngmt.field_pending_task__unavailable_employee
msgid "Unavailable Employees"
msgstr "غير متوفر الموظفين"

#. module: hr_gratuity_settlement
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_gratuity__employee_contract_type__unlimited
#: model:ir.model.fields.selection,name:hr_gratuity_settlement.selection__hr_gratuity_accounting_configuration__config_contract_type__unlimited
msgid "Unlimited"
msgstr "غير محدود"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_unread
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_unread
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_unread
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_unread
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_unread
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_unread
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_unread
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_unread
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_unread
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_unread
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_unread
msgid "Unread Messages"
msgstr "رسائل غير مقروءة"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__message_unread_counter
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__message_unread_counter
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__message_unread_counter
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__message_unread_counter
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__message_unread_counter
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__message_unread_counter
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__message_unread_counter
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__message_unread_counter
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__message_unread_counter
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__message_unread_counter
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عداد الرسائل غير المقروءة"

#. module: ohrms_service_request
#: model:ir.model.fields.selection,name:ohrms_service_request.selection__service_request__service_type__updation
msgid "Updation"
msgstr "تحديث"

#. module: hr_employee_transfer
#: model_terms:ir.actions.act_window,help:hr_employee_transfer.action_employee_transfer
msgid ""
"Use this menu to browse previous transfer. To record new\n"
"                transfer, you may use the create button."
msgstr ""
"استخدم هذه القائمة لتصفح النقل السابق. لتسجيل جديد\n"
"                نقل ، يمكنك استخدام زر إنشاء."
#. module: ohrms_loan
#: model_terms:ir.actions.act_window,help:ohrms_loan.action_hr_loan_request
msgid "Use this menu to create loan requests."
msgstr ".استخدم هذه القائمة لإنشاء طلبات القروض"

#. modules: oh_employee_creation_from_user, ohrmspro_holidays_approval
#: model:ir.model,name:oh_employee_creation_from_user.model_res_users
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_double_validation
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_validators
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_validators_leave_create
msgid "Users"
msgstr "المستخدمون"

#. module: ohrms_core
#: model_terms:ir.ui.view,arch_db:ohrms_core.view_hr_general_config
msgid "Vacation Management"
msgstr "إدارة العطلات"

#. module: ohrmspro_holidays_approval
#: model:ir.model.fields,field_description:ohrmspro_holidays_approval.field_hr_leave_type__validation_type
msgid "Validation"
msgstr "الاعتماد"

#. module: ohrmspro_holidays_approval
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_validators
#: model_terms:ir.ui.view,arch_db:ohrmspro_holidays_approval.hr_holidays_status_validators_leave_create
msgid "Validation Status"
msgstr "حالة التحقق من الصحة"

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
msgid "Validity Details"
msgstr "تفاصيل الصلاحية"

#. module: ohrms_service_request
#: model:ir.actions.act_window,name:ohrms_service_request.action_view_pivot
msgid "View Pivot"
msgstr "عرض المحور"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_contract__wage_type
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__wage_type
msgid "Wage Type"
msgstr "نوع الأجور"

#. modules: ohrms_loan_accounting, ohrms_salary_advance
#: model:ir.model.fields.selection,name:ohrms_loan_accounting.selection__hr_loan__state__waiting_approval_2
#: model:ir.model.fields.selection,name:ohrms_salary_advance.selection__salary_advance__state__waiting_approval
msgid "Waiting Approval"
msgstr "في انتظار الموافقة"

#. modules: hr_gratuity_settlement, hr_reward_warning
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_contract__waiting_for_approval
#: model:ir.model.fields.selection,name:hr_reward_warning.selection__hr_announcement__state__to_approve
msgid "Waiting For Approval"
msgstr "بانتظار الموافقة"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__website_message_ids
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_hr_gratuity__website_message_ids
#: model:ir.model.fields,field_description:hr_reward_warning.field_hr_announcement__website_message_ids
#: model:ir.model.fields,field_description:oh_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,field_description:oh_employee_check_list.field_employee_checklist__website_message_ids
#: model:ir.model.fields,field_description:oh_hr_lawsuit_management.field_hr_lawsuit__website_message_ids
#: model:ir.model.fields,field_description:ohrms_loan.field_hr_loan__website_message_ids
#: model:ir.model.fields,field_description:ohrms_salary_advance.field_salary_advance__website_message_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__website_message_ids
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__website_message_ids
#: model:ir.model.fields,field_description:saudi_gosi.field_gosi_payslip__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع"

#. modules: hr_gratuity_settlement, oh_appraisal, ohrms_salary_advance,
#. ohrms_service_request, hr_reward_warning, oh_hr_lawsuit_management,
#. oh_employee_check_list, ohrms_loan, saudi_gosi
#: model:ir.model.fields,help:hr_gratuity_settlement.field_gratuity_configuration__website_message_ids
#: model:ir.model.fields,help:hr_gratuity_settlement.field_hr_gratuity__website_message_ids
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__website_message_ids
#: model:ir.model.fields,help:oh_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,help:oh_employee_check_list.field_employee_checklist__website_message_ids
#: model:ir.model.fields,help:oh_hr_lawsuit_management.field_hr_lawsuit__website_message_ids
#: model:ir.model.fields,help:ohrms_loan.field_hr_loan__website_message_ids
#: model:ir.model.fields,help:ohrms_salary_advance.field_salary_advance__website_message_ids
#: model:ir.model.fields,help:ohrms_service_request.field_service_execute__website_message_ids
#: model:ir.model.fields,help:ohrms_service_request.field_service_request__website_message_ids
#: model:ir.model.fields,help:saudi_gosi.field_gosi_payslip__website_message_ids
msgid "Website communication history"
msgstr "سجل اتصالات الموقع"

#. module: hr_employee_shift
#: code:addons/hr_employee_shift/models/hr_employee_shift.py:0
#, python-format
msgid "Wednesday Morning"
msgstr "صباح الأربعاء"

#. module: oh_hr_lawsuit_management
#: model:ir.model.fields.selection,name:oh_hr_lawsuit_management.selection__hr_lawsuit__state__won
#: model_terms:ir.ui.view,arch_db:oh_hr_lawsuit_management.hr_lawsuit_form_view
msgid "Won"
msgstr "وون"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__worked_hours
msgid "Worked Hours"
msgstr "ساعات العمل"

#. module: oh_hr_zk_attendance
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine__address_id
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_machine_attendance__address_id
#: model:ir.model.fields,field_description:oh_hr_zk_attendance.field_zk_report_daily_attendance__address_id
msgid "Working Address"
msgstr "عنوان العمل"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__employee_working_days
msgid "Working Days"
msgstr "أيام العمل"

#. module: hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_employee__resource_calendar_ids
msgid "Working Hours"
msgstr "ساعات العمل"

#. module: hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_hr_contract__working_hours
msgid "Working Schedule"
msgstr "جدول العمل"

#. module: hr_employee_shift
#: model:ir.model.fields,field_description:hr_employee_shift.field_resource_calendar__attendance_ids
msgid "Workingssss Time"
msgstr "وقت العمل"

#. module: hr_insurance
#: model:ir.model.fields.selection,name:hr_insurance.selection__hr_insurance__policy_coverage__yearly
msgid "Yearly"
msgstr "سنوي"

#. module: hr_reward_warning
#: model:ir.model.fields,help:hr_reward_warning.field_hr_announcement__attachment_id
msgid "You can attach the copy of your Letter"
msgstr "يمكنك إرفاق نسخة من رسالتك"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,help:oh_employee_documents_expiry.field_hr_employee_document__doc_attachment_id
msgid "You can attach the copy of your document"
msgstr "يمكنك إرفاق نسخة من المستند"

#. module: oh_employee_documents_expiry
#: model:ir.model.fields,help:oh_employee_documents_expiry.field_hr_employee_document__name
msgid "You can give yourDocument number."
msgstr ".يمكنك إعطاء رقم المستند الخاص بك"

#. module: hr_leave_request_aliasing
#: model_terms:ir.ui.view,arch_db:hr_leave_request_aliasing.view_hr_leave_configuration
msgid ""
"You can setup a generic email alias to create\n"
"                                incoming leave request easily. Write an email with the desired\n"
"                                format to create leave request in one click.\n"
"                                Format:- Start subject with 'LEAVE REQUEST'. After your mail content mention\n"
"                                'Date From:' and 'Date To:'."
msgstr ""
"يمكنك إعداد اسم مستعار عام للبريد الإلكتروني لإنشاء\n"
"                                طلب إجازة واردة بسهولة. اكتب رسالة بريد الكتروني مع المطلوب\n"
"                                شكل لإنشاء طلب إجازة في نقرة واحدة.\n"
"                                شكل:- ابدأ الموضوع بـ 'إجازة الطلب'. بعد بريدك ذكر المحتوى\n"
"                                'التاريخ من:' و 'تاريخ ل:'."
#. module: hr_gratuity_settlement
#: code:addons/hr_gratuity_settlement/models/hr_contract.py:0
#: code:addons/hr_gratuity_settlement/models/hr_contract.py:0
#, python-format
msgid "You cannot change the status of non-approved Contracts"
msgstr "لا يمكنك تغيير حالة العقود غير المعتمدة"

#. module: hr_employee_transfer
#: code:addons/hr_employee_transfer/models/employee_transfer.py:0
#, python-format
msgid "You cant transfer to same company."
msgstr ".لا يمكنك نقل إلى نفس الشركة"

#. module: hr_insurance
#: model_terms:ir.actions.act_window,help:hr_insurance.action_employee_insurance_details
#: model_terms:ir.actions.act_window,help:hr_insurance.action_insurance_policy_management
msgid "You haven't created any policy yet."
msgstr ".لم تنشئ أي سياسة حتى الآن"

#. module: hr_employee_transfer
#: code:addons/hr_employee_transfer/models/employee_transfer.py:0
#, python-format
msgid "You should select the transfer branch/company."
msgstr ".يجب عليك اختيار فرع النقل / الشركة"

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__yr_from_flag
msgid "Yr From Flag"
msgstr ""

#. module: hr_gratuity_settlement
#: model:ir.model.fields,field_description:hr_gratuity_settlement.field_gratuity_configuration__yr_to_flag
msgid "Yr To Flag"
msgstr ""

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__read_only
msgid "check field"
msgstr "تحقق الحقل"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__service_date
msgid "date"
msgstr "تاريخ"

#. module: oh_employee_documents_expiry
#: model:ir.model,name:oh_employee_documents_expiry.model_document_type
msgid "document.type"
msgstr ""

#. module: hr_gratuity_settlement
#: model_terms:ir.ui.view,arch_db:hr_gratuity_settlement.view_hr_gratuity_accounting_configuration_form
msgid "e.g. Configuration Limited"
msgstr "مثلا التكوين محدودة"

#. module: hr_vacation_mngmt
#: model:ir.model,name:hr_vacation_mngmt.model_hr_flight_ticket
msgid "hr.flight.ticket"
msgstr ""

#. module: ohrmspro_holidays_approval
#: model:ir.model,name:ohrmspro_holidays_approval.model_hr_holidays_validators
msgid "hr.holidays.validators"
msgstr ""

#. module: hr_reminder
#: model:ir.model,name:hr_reminder.model_hr_reminder
msgid "hr.reminder"
msgstr ""

#. module: hr_employee_shift
#: model:ir.model,name:hr_employee_shift.model_hr_shift_generate
msgid "hr.shift.generate"
msgstr ""

#. module: hr_employee_shift
#: model:ir.model,name:hr_employee_shift.model_hr_shift_schedule
msgid "hr.shift.schedule"
msgstr ""

#. module: hr_insurance
#: model:ir.model,name:hr_insurance.model_insurance_policy
msgid "insurance.policy"
msgstr ""

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__internal_note
msgid "internal notes"
msgstr ""

#. module: ohrms_service_request
#: model:ir.model,name:ohrms_service_request.model_service_execute
msgid "issue"
msgstr ""

#. module: ohrmspro_holidays_approval
#: model:ir.model,name:ohrmspro_holidays_approval.model_leave_validation_status
msgid "leave.validation.status"
msgstr ""

#. module: oh_appraisal
#: model_terms:ir.ui.view,arch_db:oh_appraisal.hr_appraisal_kanban
msgid "oe_kanban_text_red"
msgstr ""

#. module: hr_vacation_mngmt
#: model:ir.model,name:hr_vacation_mngmt.model_pending_task
msgid "pending.task"
msgstr ""

#. module: ohrms_salary_advance
#: model:ir.model,name:ohrms_salary_advance.model_salary_advance
msgid "salary.advance"
msgstr ""

#. module: ohrms_service_request
#: model:ir.model,name:ohrms_service_request.model_service_request
msgid "service_name"
msgstr ""

#. module: hr_vacation_mngmt
#: model:ir.model,name:hr_vacation_mngmt.model_task_reassign
msgid "task.reassign"
msgstr ""

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_execute__test
msgid "test"
msgstr "اختبار"

#. module: ohrms_service_request
#: model:ir.model.fields,field_description:ohrms_service_request.field_service_request__tester
msgid "tester"
msgstr "اختبار"

#. module: uae_wps_report
#: model:ir.model,name:uae_wps_report.model_wps_wizard
msgid "wps.wizard"
msgstr ""

#. module: oh_hr_zk_attendance
#: model:ir.model,name:oh_hr_zk_attendance.model_zk_machine
msgid "zk.machine"
msgstr ""

#. module: oh_hr_zk_attendance
#: model:ir.model,name:oh_hr_zk_attendance.model_zk_machine_attendance
msgid "zk.machine.attendance"
msgstr ""

#. module: oh_hr_zk_attendance
#: model:ir.model,name:oh_hr_zk_attendance.model_zk_report_daily_attendance
msgid "zk.report.daily.attendance"
msgstr ""
