from odoo import fields, models, api, _
import logging

_logger = logging.getLogger(__name__)

class PosSalesWizard(models.TransientModel):
    _name = "pos.sales.wizard"
    _description = "POS Sales Wizard"

    kb_date_from = fields.Date(string='Date From', required=True)
    kb_date_to = fields.Date(string='Date To', required=True)
    kb_config_id = fields.Many2one(comodel_name='pos.config', string='Point of Sale')
    
    # Fields to store counted values for different payment methods
    cash_counted = fields.Float(string='Cash Counted')
    bank_counted = fields.Float(string='Bank Counted')
    customer_account_counted = fields.Float(string='Customer Account Counted')

    def _get_payment_data(self, order_ids):
        payments = []
        if not order_ids:
            return payments

        currency = self.env.company.currency_id
        payment_methods = self.env['pos.payment.method'].search([])
        
        # Get all sessions for these orders
        sessions = order_ids.mapped('session_id')
        
        # Get the closing cash details from the sessions
        for method in payment_methods:
            payment_lines = self.env['pos.payment'].search([
                ('pos_order_id', 'in', order_ids.ids),
                ('payment_method_id', '=', method.id)
            ])
            
            if payment_lines:
                total_amount = sum(payment_lines.mapped('amount'))
                money_counted = total_amount  # Default value
                
                # Get counted values from session closing details
                for session in sessions:
                    # For cash payments, use the cash register balance
                    if method.is_cash_count:
                        money_counted = session.cash_register_balance_end_real
                    else:
                        # For non-cash payments (bank, customer account), get from session payments
                        session_payments = self.env['account.payment'].search([
                            ('pos_session_id', '=', session.id),
                            ('pos_payment_method_id', '=', method.id)
                        ])
                        if session_payments:
                            money_counted = sum(session_payments.mapped('amount'))
                    
                payment_data = {
                    'name': method.name,
                    'count': True,
                    'final_count': total_amount,
                    'money_counted': money_counted,
                    'money_difference': money_counted - total_amount,
                    'cash_moves': []
                }
                
                # Add cash moves if there's a difference
                if payment_data['money_difference']:
                    move_name = 'Difference observed during the counting ({})'.format(
                        'Profit' if payment_data['money_difference'] > 0 else 'Loss'
                    )
                    payment_data['cash_moves'].append({
                        'name': move_name,
                        'amount': abs(payment_data['money_difference'])
                    })
                
                payments.append(payment_data)

        return payments

    def _prepare_report_data(self):
        # Add debug logging
        _logger.info("Date From: %s", self.kb_date_from)
        _logger.info("Date To: %s", self.kb_date_to)
        _logger.info("Config ID: %s", self.kb_config_id.id)
        
        date_from_str = self.kb_date_from.strftime('%Y-%m-%d')
        date_to_str = self.kb_date_to.strftime('%Y-%m-%d')
        
        data = {
            'lines': {
                'date_from': date_from_str,
                'date_to': date_to_str,
                'kb_config_id': self.kb_config_id.id,
            },
            'docs': self,
        }
        _logger.info("Prepared Data: %s", data)
        return data

    def action_pdf_print(self):
        data = self._prepare_report_data()
        return self.env.ref('kb_pos_sales_report.pos_sales_report_action').report_action(self, data=data)




