# -*- coding: utf-8 -*-

# from odoo import models, fields, api


# class kb_analytic_account(models.Model):
#     _name = 'kb_analytic_account.kb_analytic_account'
#     _description = 'kb_analytic_account.kb_analytic_account'

#     name = fields.Char()
#     value = fields.Integer()
#     value2 = fields.Float(compute="_value_pc", store=True)
#     description = fields.Text()
#
#     @api.depends('value')
#     def _value_pc(self):
#         for record in self:
#             record.value2 = float(record.value) / 100
