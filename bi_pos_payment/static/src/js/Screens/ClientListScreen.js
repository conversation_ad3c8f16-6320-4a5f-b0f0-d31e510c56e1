odoo.define('bi_pos_payment.PartnerListScreen', function(require) {
	'use strict';

	const PartnerListScreen = require('point_of_sale.PartnerListScreen');
	const Registries = require('point_of_sale.Registries');
	const core = require('web.core');
	const rpc = require('web.rpc');

	const _t = core._t;

	const BiPartnerListScreen = PartnerListScreen => class extends PartnerListScreen {
		
		registerPayment(partner){
			var self = this;
			
			if (!partner) {

				self.showPopup('ErrorPopup', {
					'title': _t('Unknown customer'),
					'body': _t('You cannot Register Payment. Select customer first.'),
				});
				return false;
			}

			self.showPopup('RegisterPaymentPopupWidget', {'partner':partner});
		}
	};

	Registries.Component.extend(PartnerListScreen, BiPartnerListScreen);

	return PartnerListScreen;

});