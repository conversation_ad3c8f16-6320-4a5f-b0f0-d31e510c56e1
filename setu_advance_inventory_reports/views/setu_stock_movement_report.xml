<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <data>
        <record id="setu_stock_movement_report_form" model="ir.ui.view">
            <field name="name">setu.stock.movement.report.form</field>
            <field name="model">setu.stock.movement.report</field>
            <field name="arch" type="xml">
                <form string="Stock Movement Report">
                    <sheet string="Stock Transaction Report">
                        <group expand="0" string="Filters">
                            <group expand="0" string="Transactions up to a certain date">
                                <field name="get_report_from_beginning"/>
                            </group>
                            <group expand="0" string="Select Proper Date To Filter Transactions ">
                                <field name="upto_date" attrs="{'required': [('get_report_from_beginning','=',True)], 'invisible': [('get_report_from_beginning','=',False)]}"/>
                                <field name="start_date" attrs="{'required': [('get_report_from_beginning','=',False)], 'invisible': [('get_report_from_beginning','=',True)]}"/>
                                <field name="end_date" attrs="{'required': [('get_report_from_beginning','=',False)], 'invisible': [('get_report_from_beginning','=',True)]}"/>
                            </group>
                        </group>
                        <group expand="0" >
                            <group expand="0" string="Products &#038; Categories">
                                <field name="product_category_ids" widget="many2many_tags" options="{'no_create_edit': True}"/>
                                <field name="product_ids" widget="many2many_tags" options="{'no_create_edit': True}"/>
                            </group>
                            <group expand="0" string="Companies &#038; Warehouses">
                                <field name="company_ids" widget="many2many_tags"
                                       domain="[('id', 'in', allowed_company_ids)]" options="{'no_create_edit': True}"/>
                                <field name="warehouse_ids" widget="many2many_tags" options="{'no_create_edit': True}"/>
                            </group>
                        </group>

                    </sheet>
                    <footer>
                        <button name="download_report" string="Download Report" type="object"
                                class="oe_highlight"/>
                        <button string="Cancel" class="oe_link" special="cancel" />
                    </footer>

                </form>
            </field>
        </record>


        <record id="setu_stock_movement_report_action" model="ir.actions.act_window">
            <field name="name">Stock Movement Analysis</field>
            <field name="res_model">setu.stock.movement.report</field>
            <field name="binding_view_types">form</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <menuitem id="setu_advance_inventory_reports_menu" name="Setu Inventory Reports"
                  parent="stock.menu_warehouse_report" sequence="17" />

        <menuitem id="setu_stock_movement_report_menu" action="setu_stock_movement_report_action"
                  parent="setu_advance_inventory_reports.setu_advance_inventory_reports_menu" sequence="26" />
    </data>
</odoo>