<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="purchase_order_inherit_view_form" model="ir.ui.view">
        <field name="name">purchase.order.inherit.view.form</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form" />
        <field name="arch" type="xml">
            <field name="partner_ref" position="after">
                <field name="remarks" attrs="{'invisible': [('is_remarks','=',False)],'required':[('is_remarks_mandatory','=',True),('is_boolean','=',True)]}" />
                <field name="is_remarks" invisible="1" />
                <field name="is_remarks_mandatory" invisible="1" />
                <field name="is_boolean" invisible="1" />
            </field>
        </field>
    </record>
</odoo>
