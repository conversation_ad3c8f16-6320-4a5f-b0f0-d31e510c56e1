<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="stock_picking_inherit_view_form_purchase" model="ir.ui.view">
        <field name="name">stock.picking.inherit.view.form</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form" />
        <field name="arch" type="xml">
            <field name="date_deadline" position="after">
                <field name="remarks_for_purchase" attrs="{'invisible': ['|',('is_remarks_for_purchase','=',False),('remarks_for_purchase','=',False)]}" />
                <field name="is_remarks_for_purchase" invisible="1" />
            </field>
        </field>
    </record>
</odoo>
