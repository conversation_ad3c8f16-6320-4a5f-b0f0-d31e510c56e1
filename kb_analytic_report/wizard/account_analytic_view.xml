<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_analytic_wizard_view_form" model="ir.ui.view">
        <field name="name">account.analytic.wizard.form</field>
        <field name="model">account.analytic.wizard</field>
        <field name="arch" type="xml">
            <form string="Report analytic">
                <group>
                    <field name="report_type" />
                    <field name="account_ids" widget="many2many_tags" options="{'no_open': True}" />
                    <field name="analytic_ids" widget="many2many_tags" options="{'no_open': True}" />
                    <field name="date_from" required="True"/>
                    <field name="date_to" required="True"/>
                    <field name="target_move" widget="radio"/>
                    <field name="user_id" />
                </group>
                <footer>
                    <button name="print_pdf" type="object" string="PDF" class="oe_highlight" attrs="{'invisible': [('report_type','!=','1')]}"/>
                    <button name="print_excel" type="object" string="Excel" class="oe_highlight" attrs="{'invisible': [('report_type','!=','1')]}"/>
                    <button name="print_pdf_v2" type="object" string="PDF v2" class="oe_highlight" attrs="{'invisible': [('report_type','!=','2')]}"/>
                    <button name="print_excel_v2" type="object" string="Excel v2" class="oe_highlight" attrs="{'invisible': [('report_type','!=','2')]}"/>
                    <button name="print_pdf_v3" type="object" string="PDF v3" class="oe_highlight" attrs="{'invisible': [('report_type','!=','3')]}"/>
                    <button name="print_excel_v3" type="object" string="Excel v3" class="oe_highlight" attrs="{'invisible': [('report_type','!=','3')]}"/>
                    <button special="cancel" string="Cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="account_analytic_action" model="ir.actions.act_window">
        <field name="name">Analytic Account Report</field>
        <field name="res_model">account.analytic.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="account_analytic_wizard_view_form"/>
    </record>

    <menuitem id="menu_account_analytic"
              name="Analytic account report"
              sequence="100"
              action="account_analytic_action"
              parent="account.menu_finance_reports"
              groups="account.group_account_manager,account.group_account_user"/>

</odoo>
