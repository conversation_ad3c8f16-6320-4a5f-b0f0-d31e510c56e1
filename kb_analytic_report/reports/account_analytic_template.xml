<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="account_analytic_report">
            <t t-call="web.html_container">
                <t t-call="web.external_layout">
                    <link href="https://fonts.googleapis.com/css2?family=Almarai" rel="stylesheet" />
                    <div class="page" style="font-family: 'Almarai', sans-serif;">
                        <br />
                        <br />
                        <style>
                            #table_css {
                                border: 1px solid black border-collapse: collapse;
                                border-spacing: 0px;
                                border-top-spacing: 0px;
                            }

                            #table_css td,
                            #table_css th {
                                border: 1px solid black
                            }

                            #table_css tr: {
                                border: 1px solid black
                            }

                            #table_css th {
                                style="border: 1px solid black"
                            }
                        </style>
                        <table style="width:100%; border:1px solid black;">
                            <tr>
                                <th  colspan="4" style="border:1px solid black; background-color: white; padding:2px;">
                                    Analytic Account Report - <span t-esc="report_type" /> 
                                </th>
                            </tr>
                            <tr>
                                <th style="border:1px solid black; background-color: white; padding:2px;">
                                    Accounts
                                </th>
                                <td style="border:1px solid black; background-color: white; padding:2px;">
                                    <span t-if="account" t-esc="account" />
                                </td>
                                <th style="border:1px solid black; background-color: white; padding:2px;">
                                    Analytics
                                </th>
                                <td style="border:1px solid black; background-color: white; padding:2px;">
                                    <span t-if="analytic_names" t-esc="analytic_names" />
                                </td>
                            </tr>
                            <tr>
                                <th style="border:1px solid black; background-color: white; padding:2px;">
                                    Date
                                </th>
                                <td style="border:1px solid black; background-color: white; padding:2px;">
                                    <span t-esc="form_data['date_from']" /> to <span t-esc="form_data['date_to']" />
                                </td>
                                <th style="border:1px solid black; background-color: white; padding:2px;">
                                    Target
                                </th>
                                <td style="border:1px solid black; background-color: white; padding:2px;">
                                     <span t-esc="target_move" />
                                </td>
                            </tr>
                            <tr>
                                <th style="border:1px solid black; background-color: white; padding:2px;">
                                    Print Date
                                </th>
                                <td style="border:1px solid black; background-color: white; padding:2px;">
                                    <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M')"/>
                                </td>
                                <th style="border:1px solid black; background-color: white; padding:2px;">
                                    User
                                </th>
                                <td style="border:1px solid black; background-color: white; padding:2px;">
                                    <span t-esc="form_data['user_id'][1]" />
                                </td>
                            </tr>
                        </table>
                        <br/>
                        <table class="table table-sm o_main_table" id="table_css">
                            <thead>
                                <tr>
                                    <th style="background-color: lightgrey;width:25%;">Date</th>
                                    <th style="background-color: lightgrey;">Code</th>
                                    <th style="background-color: lightgrey;">Account</th>
                                    <th style="background-color: lightgrey;">Move</th>
                                    <th style="background-color: lightgrey;">Partner</th>
                                    <th style="background-color: lightgrey;">Debit</th>
                                    <th style="background-color: lightgrey;">Credit</th>
                                    <th style="background-color: lightgrey;">Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-set="seq" t-value="1" />
                                <t t-set="total_debit" t-value="0" />
                                <t t-set="total_credit" t-value="0" />
                                <t t-set="total_balance" t-value="0" />
                                <t t-set="total_opening_balance" t-value="0" />
                                <t t-foreach="out_list" t-as="line">
                                    <tr>
                                        <td>
                                            <span t-esc="line['date']" />
                                        </td>
                                        <td>
                                            <span t-esc="line['account_code']" />
                                        </td>
                                        <td>
                                            <span t-esc="line['account_name']" />
                                        </td>
                                        <td>
                                            <span t-esc="line['move_name']" />
                                        </td>
                                        <td>
                                            <span t-esc="line['partner_name']" />
                                        </td>
                                        <td>
                                           <span t-esc="line['debit']" />
                                            <t t-set="total_debit" t-value="total_debit + line['debit']" />
                                        </td>
                                        <td>
                                            <span t-esc="line['credit']" />
                                            <t t-set="total_credit" t-value="total_credit + line['credit']" />
                                        </td>
                                        <td>
                                           <span t-esc="line['balance']" />
                                            <t t-set="total_balance" t-value="total_balance + line['balance']" />
                                        </td>
                                        
                                    </tr>
                                </t>
                                <tr>
                                    <td colspan="5" style="text-align:center;"> Total </td>
                                    <td><span t-esc="total_debit" t-options='{"widget": "float", "precision": 2}'/></td>
                                    <td><span t-esc="total_credit" t-options='{"widget": "float", "precision": 2}'/></td>
                                    <td><span t-esc="total_balance" t-options='{"widget": "float", "precision": 2}'/></td>
                                </tr>
                            </tbody>
                        </table>
                        <br />


                        <table style="border:none; width:100%; background-color:white;">
                            <tr style="border:none;">
                                <td colspan="6" style="border:none; text-align:center; background-color:white;">
                                    <h4>End of document</h4>
                                    <h4 style="text-align:center"> Powerd by Knowledge bonds </h4>
                        <h5 style="text-align:center"> www.rawabt.sa </h5>
                                </td>
                            </tr>
                        </table>

                    </div>
                </t>
            </t>
        </template>

    </data>
</odoo>