<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_partner_form_inh_request" model="ir.ui.view">
            <field name="name">res.partner.form.inh</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='property_product_pricelist']" position="after">
                    <field name="is_kb_credit" attrs="{'readonly':[('is_kb_credit_access','=',False)]}"/>
                    <field name="kb_credit_limit" attrs="{'invisible':[('is_kb_credit','=',False)],'readonly':[('is_kb_credit_access','=',False)]}"/>
                    <field name="is_kb_credit_access" invisible="1"/>
                </xpath>
            </field>
        </record>


    </data>
</odoo>