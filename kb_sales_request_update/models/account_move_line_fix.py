from odoo import api, fields, models


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    @api.model
    def update_analytic_distribution_from_pos(self, records=None):
        """
        Update analytic distribution for account move lines related to POS sessions.
        This method provides a fix for the pos_session_ids field issue in Odoo 18.3
        """
        if records is None:
            records = self
            
        for line in records:
            if line.move_id:
                # Find POS sessions that have this move as their move_id
                sessions = self.env['pos.session'].search([
                    ('move_id', '=', line.move_id.id)
                ])
                
                if sessions:
                    # Take the first session (usually there's only one)
                    session = sessions[0]
                    config = session.config_id

                    # Check if the field exists and has a value
                    if hasattr(config, 'analytic_account_id') and config.analytic_account_id:
                        line.write({
                            'analytic_distribution': {config.analytic_account_id.id: 100}
                        })

    @api.model
    def process_pos_analytic_distribution(self):
        """
        Batch process to update analytic distribution for POS-related moves.
        This can be called from a scheduled action or manually.
        """
        # Find account move lines that are related to POS sessions but don't have analytic distribution
        pos_moves = self.env['pos.session'].search([
            ('move_id', '!=', False)
        ]).mapped('move_id')
        
        lines_to_update = self.env['account.move.line'].search([
            ('move_id', 'in', pos_moves.ids),
            ('analytic_distribution', '=', False)
        ])
        
        self.update_analytic_distribution_from_pos(lines_to_update)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Success',
                'message': f'Updated {len(lines_to_update)} account move lines with analytic distribution.',
                'type': 'success',
            }
        }
