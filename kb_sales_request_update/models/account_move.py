from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class AccountMove(models.Model):
    _inherit = "account.move"

    entry_move_id = fields.Many2one(
        comodel_name='account.move',
        string='Entry Move',
        required=False)
    req_id = fields.Many2one('kb_request_for_sale', string="Request for sale",store=True)

    @api.model
    def _get_default_branch(self):
        branch = self.env.user.branch_id
        return branch

    def _get_branch_domain(self):
        """methode to get branch domain"""
        company = self.env.company
        branch_ids = self.env.user.branch_ids
        branch = branch_ids.filtered(
            lambda branch: branch.company_id == company)
        return [('id', 'in', branch.ids)]

    branch_id = fields.Many2one('res.branch', string='Branch',
                                default=_get_default_branch,
                                domain=_get_branch_domain)

    # def action_post(self):
    #     res = super().action_post()
    #     for move in self:
    #         if move.move_type == 'out_invoice':
    #             for line in move.invoice_line_ids:
    #                 cost_account_id = line.product_id.categ_id.stock_cost_account_id
    #                 if not cost_account_id:
    #                     raise ValidationError(f"Please Set Cost Account in {line.product_id.categ_id.name}")
    #                 intermediate_account_id = line.product_id.categ_id.property_stock_account_output_categ_id
    #                 if not intermediate_account_id:
    #                     raise ValidationError(f"Please Set Intermediate Account in {line.product_id.categ_id.name}")
    #                 picking_id = self.env['sale.order'].search([('invoice_ids', 'in', [move.id])]).mapped(
    #                     'picking_ids')
    #                 account_move = self.env['account.move']
    #                 entry_vals = {
    #                     'ref': f'{move.name}'
    #                            f'{line.product_id.name}',
    #                     'move_type': 'entry',
    #                     'entry_move_id': move.id,
    #                     'journal_id': self.env['account.journal'].search([('code', '=', 'MISC')]).id,
    #                 }
    #
    #                 product_entry_id = account_move.search(
    #                     [('ref', 'like', picking_id.name), ('ref', 'like', line.product_id.name)])
    #
    #                 print("product_entry_id", product_entry_id.ref, product_entry_id.amount_total_signed)
    #                 lines = [
    #                     (0, 0, {
    #                         'name': f'Cost - '
    #                                 f'{line.product_id.name}',
    #                         'debit': product_entry_id.amount_total_signed,  # Example amount
    #                         'credit': 0.0,
    #                         'account_id': cost_account_id.id,
    #                     }),
    #                     (0, 0, {
    #                         'name': f'Intermediate - '
    #                                 f'{line.product_id.name}',
    #                         'debit': 0.0,
    #                         'credit': product_entry_id.amount_total_signed,
    #                         'account_id': intermediate_account_id.id,
    #                     }),
    #                 ]
    #                 if lines:
    #                     journal_entry = account_move.create(entry_vals)
    #                     print("journal_entry", journal_entry)
    #
    #                     if journal_entry:
    #                         journal_entry.line_ids = lines
    #                         journal_entry.action_post()
    #                         print("journal_entry2", journal_entry)
    #     return res


class AccountMoveLine(models.Model):
    _inherit = "account.move.line"

    req_id = fields.Many2one('kb_request_for_sale', string="Request for sale", related="move_id.req_id" ,store=True)

    # @api.onchange('product_id', 'account_id')
    # def update_analytic(self):
    #     for line in self:
    #         if line.product_id or line.account_id:
    #             analytic = self.env['account.analytic.account'].search(
    #                 [('kb_branch_id', '=', line.move_id.branch_id.id)],
    #                 limit=1)
    #             line.analytic_distribution = {analytic.id: 100}