# -*- coding: utf-8 -*-
{
    'name': "Deferred Expense",
    'sequence': -250,
    'application': True,
    'summary': """
        Deferred Expense""",

    'description': """
        Deferred Expense
    """,

    'author': "Knowledge",
    'website': "https://www.yourcompany.com",
    'version': '16',
    'depends': ['base', 'account', 'mail', 'om_account_accountant'],

    'data': [
        'security/ir.model.access.csv',
        'data/data.xml',
        'views/account_asset.xml',
        'views/account_account_views.xml',
        'views/account_move_views.xml',
        'views/account_deferred_expense.xml',
        'wizard/asset_modify_views.xml',
        # 'wizard/deferred_expense.xml',
    ],
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
}
